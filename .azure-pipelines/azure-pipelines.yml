# Azure DevOps Pipeline for AgriCare 1.0
# This file triggers the main pipeline

trigger:
- main

pool:
  vmImage: 'ubuntu-latest'

steps:
- script: |
    echo "🚀 Triggering AgriCare 1.0 Comprehensive Pipeline"
    echo "This is a trigger file to ensure Azure DevOps recognizes the pipeline"
    echo "Main pipeline file: azure-pipelines.yml"
  displayName: 'Pipeline Trigger'

- script: |
    echo "📋 Pipeline Information:"
    echo "Repository: ag1may25"
    echo "Branch: main"
    echo "Main Pipeline: azure-pipelines.yml"
    echo "Application: AgriCare 1.0"
    echo ""
    echo "✅ Pipeline trigger completed"
    echo "The main comprehensive pipeline should now be available in Azure DevOps"
  displayName: 'Pipeline Info'
