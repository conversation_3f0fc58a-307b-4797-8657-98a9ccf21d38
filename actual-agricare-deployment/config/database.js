const mongoose = require('mongoose');
require('dotenv').config();

const connectDB = async () => {
    try {
        // Set mongoose options to avoid deprecation warnings
        const options = {
            useNewUrlParser: true,
            useUnifiedTopology: true,
            serverSelectionTimeoutMS: 5000, // Timeout after 5s instead of 30s
            socketTimeoutMS: 45000, // Close sockets after 45s of inactivity
        };

        const conn = await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/agricare', options);
        console.log(`✅ MongoDB Connected: ${conn.connection.host}`);
    } catch (error) {
        console.error(`❌ MongoDB Connection Error: ${error.message}`);
        console.error('Make sure MongoDB is running. Try starting it with: mongod --config mongod.conf --fork');
        process.exit(1);
    }
};

module.exports = connectDB;