const admin = require('firebase-admin');
const path = require('path');
require('dotenv').config();

// Use mock configuration in development mode
if (process.env.NODE_ENV === 'development') {
    console.log('Using mock Firebase configuration for development');
    module.exports = require('./firebase');
    return;
}

// Initialize Firebase Admin with service account file
const serviceAccountPath = path.resolve(__dirname, 'firebase-credentials.json');

try {
    admin.initializeApp({
        credential: admin.credential.cert(serviceAccountPath),
        projectId: process.env.FIREBASE_PROJECT_ID
    });
    console.log('✅ Firebase Admin initialized successfully');
} catch (error) {
    console.error('❌ Failed to initialize Firebase Admin:', error);
    // Don't exit process, allow fallback to development mode
}

module.exports = admin; 