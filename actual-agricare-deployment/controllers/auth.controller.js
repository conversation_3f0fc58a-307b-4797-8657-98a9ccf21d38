const mongoose = require('mongoose');
const User = require('../models/user.model');
const Farm = require('../models/Farm');
const Farmer = require('../models/Farmer');
const otpService = require('../services/otp.service');
const smsService = require('../services/sms.service');

// Since there's no TM model file, we'll use mongoose directly for TM queries

// Send OTP to user's phone
const sendOTP = async (req, res) => {
    try {
        const { phoneNumber, role } = req.body;

        // Validate input
        if (!phoneNumber || typeof phoneNumber !== 'string') {
            return res.status(400).json({
                success: false,
                message: 'Phone number is required and must be a string'
            });
        }

        if (!role || typeof role !== 'string') {
            return res.status(400).json({
                success: false,
                message: 'Role is required and must be a string'
            });
        }

        // Normalize role
        const normalizedRole = role === 'TMManager' ? 'TM' : role;

        // Verify user exists
        let userExists = false;

        if (normalizedRole === 'Farmer') {
            const farmer = await Farmer.findOne({ mobile: phoneNumber });
            userExists = !!farmer;
        } else if (normalizedRole === 'TM') {
            const tm = await mongoose.model('TM').findOne({ mobile: phoneNumber });
            userExists = !!tm;
        }

        if (!userExists) {
            return res.status(404).json({
                success: false,
                message: `No ${normalizedRole} found with this phone number`
            });
        }

        // Normalize phone number
        const normalizedPhone = phoneNumber.replace(/\D/g, '').slice(-10);

        // Generate OTP - use fixed OTP for development or specific test number
        let generatedOTP;
        if (process.env.NODE_ENV === 'development' || normalizedPhone === '9611966747') {
            generatedOTP = '123456'; // Fixed OTP for testing or specific number
            console.log(`Using fixed OTP 123456 for phone number ${normalizedPhone}`);
        } else {
            generatedOTP = otpService.generateOTP(6);
        }

        // Store OTP with expiration (10 minutes)
        otpService.storeOTP(normalizedPhone, generatedOTP, 10);

        // Send OTP via SMS
        const otpMessage = `Your AgriCare verification code is: ${generatedOTP}. Valid for 10 minutes.`;
        await smsService.sendSMS(phoneNumber, otpMessage);

        // Return success response
        return res.status(200).json({
            success: true,
            message: 'OTP sent successfully',
            // Include the OTP in the response for testing purposes only
            // In production, this should NOT be included
            otp: process.env.NODE_ENV === 'development' ? generatedOTP : undefined
        });
    } catch (error) {
        console.error('Error sending OTP:', error);
        return res.status(500).json({
            success: false,
            message: error.message || 'Internal server error'
        });
    }
};

// Verify OTP and create/update user
const verifyOTP = async (req, res) => {
    try {
        const { phoneNumber, otp, role } = req.body;

        console.log('OTP Verification Request:', { phoneNumber, otp, role, env: process.env.NODE_ENV });

        // Validate input
        if (!phoneNumber || typeof phoneNumber !== 'string') {
            return res.status(400).json({
                success: false,
                message: 'Phone number is required and must be a string'
            });
        }

        if (!otp || typeof otp !== 'string') {
            return res.status(400).json({
                success: false,
                message: 'OTP is required and must be a string'
            });
        }

        if (!role) {
            console.log('Role validation failed');
            return res.status(400).json({
                success: false,
                message: 'Role is required'
            });
        }

        // Normalize role to handle both 'TM' and 'TMManager'
        const normalizedRole = role === 'TMManager' ? 'TM' : role;

        if (!['Farmer', 'TM'].includes(normalizedRole)) {
            console.log('Invalid role:', role);
            return res.status(400).json({
                success: false,
                message: 'Role must be either Farmer or Territory Manager'
            });
        }

        // Normalize phone number
        const normalizedPhone = phoneNumber.replace(/\D/g, '').slice(-10);

        // Verify OTP using our OTP service
        let isValidOTP;
        if (process.env.NODE_ENV === 'development' || normalizedPhone === '9611966747') {
            // For development or specific test number, accept fixed OTP
            isValidOTP = (otp === '123456');
            console.log(`Verifying fixed OTP for ${normalizedPhone}: ${isValidOTP ? 'Valid' : 'Invalid'}`);
        } else {
            // For production, use OTP service
            isValidOTP = otpService.verifyOTP(normalizedPhone, otp);
        }

        if (isValidOTP) {
            try {
                console.log('Development Mode: Verifying OTP');
                // Find or create user in MongoDB
                let user = await User.findOne({ phoneNumber: normalizedPhone });
                console.log('Existing User:', user);

                // Check if user exists but with a different role
                if (user && user.role !== normalizedRole) {
                    console.log(`User exists with role ${user.role} but trying to login as ${normalizedRole}`);

                    // If the user is trying to login as a Farmer but is registered as TM or vice versa
                    if ((normalizedRole === 'Farmer' && user.role === 'TM') ||
                        (normalizedRole === 'TM' && user.role === 'Farmer')) {
                        // Update the user's role to the new role
                        user.role = normalizedRole;
                        await user.save();
                        console.log(`Updated user role from ${user.role} to ${normalizedRole}`);
                    } else {
                        return res.status(400).json({
                            success: false,
                            message: `This phone number is already registered as a ${user.role}. Please login with the correct role.`
                        });
                    }
                }

                if (!user) {
                    console.log('Creating new user');

                    // Check if there's an existing farmer or TM with this phone number
                    let existingName = null;
                    let existingId = null;

                    if (normalizedRole === 'Farmer') {
                        const existingFarmer = await Farmer.findOne({ mobile: normalizedPhone });
                        if (existingFarmer) {
                            existingName = existingFarmer.name;
                            existingId = existingFarmer._id;
                            console.log('Found existing farmer:', existingFarmer.name);
                        }
                    } else if (normalizedRole === 'TM') {
                        // Use mongoose directly since we don't have a TM model imported
                        const existingTM = await mongoose.model('TM').findOne({ mobile: normalizedPhone });
                        if (existingTM) {
                            existingName = existingTM.name;
                            existingId = existingTM._id;
                            console.log('Found existing TM:', existingTM.name);
                        }
                    }

                    // Create new user with normalized data
                    const userData = {
                        phoneNumber: normalizedPhone,
                        name: existingName || `User${normalizedPhone.slice(-4)}`,
                        role: normalizedRole,
                        firebaseUid: `dev_${normalizedPhone.replace(/[^0-9]/g, '')}`,
                        email: `user_${normalizedPhone.replace(/[^0-9]/g, '')}@example.com`
                    };
                    console.log('Creating user with data:', userData);

                    try {
                        user = await User.create(userData);
                        console.log('New User Created:', user);
                    } catch (createError) {
                        console.error('Error creating user:', createError);
                        if (createError.code === 11000) {
                            // This is a duplicate key error
                            // Try to find the user again in case it was created in a race condition
                            user = await User.findOne({ phoneNumber: normalizedPhone });
                            if (!user) {
                                return res.status(400).json({
                                    success: false,
                                    message: 'Phone number already registered but user not found'
                                });
                            }
                        } else {
                            throw createError;
                        }
                    }

                    // Create a default farm for new farmers
                    if (normalizedRole === 'Farmer') {
                        console.log('Creating default farm for new farmer');
                        const defaultFarm = await Farm.create({
                            farmerId: user._id.toString(),
                            farmerName: user.name,
                            state: 'Default State',
                            district: 'Default District',
                            location: {
                                type: 'Point',
                                coordinates: [0, 0] // Default coordinates
                            },
                            farmSize: 'Not specified',
                            cropType: 'Not specified',
                            irrigationStatus: 'Not specified'
                        });
                        console.log('Default farm created:', defaultFarm);
                    }
                } else {
                    console.log('Updating existing user');
                    // Update existing user's role if it has changed
                    if (user.role !== normalizedRole) {
                        user.role = normalizedRole;
                    }
                    user.lastLogin = new Date();
                    await user.save();
                    console.log('User Updated:', user);

                    // Check if farmer has a farm, if not create one
                    if (normalizedRole === 'Farmer') {
                        // Try to find a farmer with the same phone number
                        const Farmer = require('../models/Farmer');
                        const farmer = await Farmer.findOne({ mobile: normalizedPhone });

                        // If farmer record exists, update the user's name to match
                        if (farmer && farmer.name !== user.name) {
                            console.log(`Found farmer record with name: ${farmer.name}, updating user name from: ${user.name}`);
                            user.name = farmer.name;
                            await user.save();
                            console.log('User name updated to match farmer record');
                        }

                        const existingFarm = await Farm.findOne({ farmerId: user._id.toString() });
                        if (!existingFarm) {
                            console.log('Creating default farm for existing farmer');
                            const defaultFarm = await Farm.create({
                                farmerId: user._id.toString(),
                                farmerName: user.name,
                                state: 'Default State',
                                district: 'Default District',
                                location: {
                                    type: 'Point',
                                    coordinates: [0, 0] // Default coordinates
                                },
                                farmSize: 'Not specified',
                                cropType: 'Not specified',
                                irrigationStatus: 'Not specified'
                            });
                            console.log('Default farm created:', defaultFarm);
                        }
                    }
                }

                const responseData = {
                    success: true,
                    data: {
                        user: {
                            id: user._id,
                            phoneNumber: user.phoneNumber,
                            name: user.name,
                            role: user.role,
                            preferredLanguage: user.preferredLanguage || 'en'
                        }
                    }
                };
                console.log('Sending response:', responseData);
                return res.status(200).json(responseData);
            } catch (error) {
                console.error('Development Mode Error:', error);
                throw error;
            }
        }

        // OTP verification failed
        return res.status(400).json({
            success: false,
            message: 'Invalid OTP. Please try again.'
        });

            // Find or create user in MongoDB
            let user = await User.findOne({ phoneNumber: normalizedPhone });

            // Check if user exists but with a different role
            if (user && user.role !== normalizedRole) {
                console.log(`User exists with role ${user.role} but trying to login as ${normalizedRole}`);

                // If the user is trying to login as a Farmer but is registered as TM or vice versa
                if ((normalizedRole === 'Farmer' && user.role === 'TM') ||
                    (normalizedRole === 'TM' && user.role === 'Farmer')) {
                    // Update the user's role to the new role
                    user.role = normalizedRole;
                    await user.save();
                    console.log(`Updated user role from ${user.role} to ${normalizedRole}`);
                } else {
                    return res.status(400).json({
                        success: false,
                        message: `This phone number is already registered as a ${user.role}. Please login with the correct role.`
                    });
                }
            }

            if (!user) {
                // Check if there's an existing farmer or TM with this phone number
                let existingName = null;
                let existingId = null;

                if (normalizedRole === 'Farmer') {
                    const existingFarmer = await Farmer.findOne({ mobile: normalizedPhone });
                    if (existingFarmer) {
                        existingName = existingFarmer.name;
                        existingId = existingFarmer._id;
                        console.log('Found existing farmer:', existingFarmer.name);
                    }
                } else if (normalizedRole === 'TM') {
                    // Use mongoose directly since we don't have a TM model imported
                    const existingTM = await mongoose.model('TM').findOne({ mobile: normalizedPhone });
                    if (existingTM) {
                        existingName = existingTM.name;
                        existingId = existingTM._id;
                        console.log('Found existing TM:', existingTM.name);
                    }
                }

                // Create new user
                try {
                    user = await User.create({
                        firebaseUid: `custom_${normalizedPhone.replace(/[^0-9]/g, '')}`,
                        phoneNumber: normalizedPhone,
                        name: existingName || `User${normalizedPhone.slice(-4)}`,
                        role: normalizedRole,
                        email: `user_${normalizedPhone.replace(/[^0-9]/g, '')}@example.com`
                    });
                } catch (createError) {
                    console.error('Error creating user in production mode:', createError);
                    if (createError.code === 11000) {
                        // This is a duplicate key error
                        // Try to find the user again in case it was created in a race condition
                        user = await User.findOne({ phoneNumber: normalizedPhone });
                        if (!user) {
                            return res.status(400).json({
                                success: false,
                                message: 'Phone number already registered but user not found'
                            });
                        }
                    } else {
                        throw createError;
                    }
                }

                // Create a default farm for new farmers
                if (normalizedRole === 'Farmer') {
                    await Farm.create({
                        farmerId: user._id.toString(),
                        farmerName: user.name,
                        state: 'Default State',
                        district: 'Default District',
                        location: {
                            type: 'Point',
                            coordinates: [0, 0] // Default coordinates
                        },
                        farmSize: 'Not specified',
                        cropType: 'Not specified',
                        irrigationStatus: 'Not specified'
                    });
                }
            } else {
                // Update existing user's role if it has changed
                if (user.role !== normalizedRole) {
                    user.role = normalizedRole;
                }
                user.lastLogin = new Date();
                // Update firebaseUid if needed
                if (!user.firebaseUid) {
                    user.firebaseUid = `custom_${normalizedPhone.replace(/[^0-9]/g, '')}`;
                }
                await user.save();

                // Check if farmer has a farm, if not create one
                if (normalizedRole === 'Farmer') {
                    const existingFarm = await Farm.findOne({ farmerId: user._id.toString() });
                    if (!existingFarm) {
                        await Farm.create({
                            farmerId: user._id.toString(),
                            farmerName: user.name,
                            state: 'Default State',
                            district: 'Default District',
                            location: {
                                type: 'Point',
                                coordinates: [0, 0] // Default coordinates
                            },
                            farmSize: 'Not specified',
                            cropType: 'Not specified',
                            irrigationStatus: 'Not specified'
                        });
                    }
                }
            }

            return res.status(200).json({
                success: true,
                data: {
                    user: {
                        id: user._id,
                        phoneNumber: user.phoneNumber,
                        name: user.name,
                        role: user.role,
                        preferredLanguage: user.preferredLanguage || 'en'
                    }
                }
            });
        // This code is unreachable now, but kept for future reference

    } catch (error) {
        console.error('OTP Verification Error:', error);
        console.error('Error Details:', {
            code: error.code,
            name: error.name,
            message: error.message,
            stack: error.stack
        });

        // Send a more specific error message based on the error type
        let errorMessage = 'OTP verification failed';
        if (error.code === 11000) {
            errorMessage = 'Phone number already registered';
        } else if (error.name === 'ValidationError') {
            errorMessage = 'Invalid user data provided';
        }

        res.status(500).json({
            success: false,
            message: errorMessage,
            error: error.message
        });
    }
};

// Update user profile
const updateProfile = async (req, res) => {
    try {
        const { name, location, preferredLanguage } = req.body;
        const user = await User.findById(req.user._id);

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        if (name) user.name = name;
        if (location) user.location = location;
        if (preferredLanguage) user.preferredLanguage = preferredLanguage;

        await user.save();

        res.status(200).json({
            success: true,
            data: {
                user: {
                    id: user._id,
                    phoneNumber: user.phoneNumber,
                    name: user.name,
                    location: user.location,
                    preferredLanguage: user.preferredLanguage,
                    role: user.role
                }
            }
        });

    } catch (error) {
        console.error('Profile Update Error:', error);
        res.status(500).json({
            success: false,
            message: 'Profile update failed',
            error: error.message
        });
    }
};

// Get user profile
const getProfile = async (req, res) => {
    try {
        const user = await User.findById(req.user._id);

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        res.status(200).json({
            success: true,
            data: {
                user: {
                    id: user._id,
                    phoneNumber: user.phoneNumber,
                    name: user.name,
                    location: user.location,
                    preferredLanguage: user.preferredLanguage,
                    role: user.role
                }
            }
        });

    } catch (error) {
        console.error('Get Profile Error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get profile',
            error: error.message
        });
    }
};

module.exports = {
    sendOTP,
    verifyOTP,
    updateProfile,
    getProfile
};