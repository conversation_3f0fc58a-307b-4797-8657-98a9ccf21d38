const ChatHistory = require('../models/ChatHistory');
const User = require('../models/user.model');
const { OpenAIClient, AzureKeyCredential } = require('@azure/openai');
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const FormData = require('form-data');

// Initialize Azure OpenAI client
const client = new OpenAIClient(
  process.env.AZURE_OPENAI_ENDPOINT,
  new AzureKeyCredential(process.env.AZURE_OPENAI_API_KEY)
);

console.log('Azure OpenAI client initialized with endpoint:', process.env.AZURE_OPENAI_ENDPOINT);

// Initialize Azure Speech Service for STT
const speechKey = process.env.AZURE_SPEECH_KEY;
const speechRegion = process.env.AZURE_SPEECH_REGION;

// Helper function to convert image to base64
const imageToBase64 = async (imagePath) => {
  try {
    const imageBuffer = fs.readFileSync(imagePath);
    return imageBuffer.toString('base64');
  } catch (error) {
    console.error('Error converting image to base64:', error);
    throw error;
  }
};

// Helper function to download image from URL
const downloadImage = async (url, outputPath) => {
  try {
    const response = await axios({
      url,
      method: 'GET',
      responseType: 'stream',
    });

    const writer = fs.createWriteStream(outputPath);
    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', resolve);
      writer.on('error', reject);
    });
  } catch (error) {
    console.error('Error downloading image:', error);
    throw error;
  }
};

// Get chat history for a user (most recent conversations first)
exports.getChatHistory = async (req, res) => {
  try {
    // TEMPORARY: Use a mock user ID if no authenticated user
    const userId = req.user?.id || 'mock-user-id';
    console.log('Getting chat history for user:', userId);

    // Get chat history from the last two weeks
    const twoWeeksAgo = new Date();
    twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14);

    // Try to find chat history for the user, grouped by chatDate
    let chatHistory = await ChatHistory.find({
      userId,
      chatDate: { $gte: twoWeeksAgo }
    })
    .sort({ chatDate: -1, updatedAt: -1 })
    .limit(30);

    // Group chats by date for the response
    const chatsByDate = {};

    if (chatHistory.length > 0) {
      chatHistory.forEach(chat => {
        const dateKey = new Date(chat.chatDate).toISOString().split('T')[0]; // YYYY-MM-DD format

        if (!chatsByDate[dateKey]) {
          chatsByDate[dateKey] = [];
        }

        chatsByDate[dateKey].push(chat);
      });
    }

    // If no chat history found, create mock data
    if (chatHistory.length === 0) {
      console.log('No chat history found, creating mock data');
      chatHistory = [
        {
          _id: 'mock-chat-1',
          userId: userId,
          userName: 'Mock User',
          userRole: 'Farmer',
          messages: [
            {
              text: 'Hello, I need help with my wheat crop',
              sender: 'user',
              timestamp: new Date(Date.now() - 3600000),
              language: 'en'
            },
            {
              text: 'I\'m your Quamin assistant. How can I help you today?',
              sender: 'bot',
              timestamp: new Date(Date.now() - 3500000),
              language: 'en',
              followupQuestions: [
                'How do I analyze my soil health?',
                'What crops are best for my region?',
                'How can I improve crop yield?'
              ]
            }
          ],
          contextData: { contextId: Date.now().toString() },
          createdAt: new Date(Date.now() - 86400000),
          updatedAt: new Date(Date.now() - 3500000)
        }
      ];
    }

    res.status(200).json({
      success: true,
      data: chatHistory.length > 0 ? { byDate: chatsByDate, allChats: chatHistory } : chatHistory
    });
  } catch (error) {
    console.error('Error fetching chat history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch chat history',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Search chat history
exports.searchChatHistory = async (req, res) => {
  try {
    // TEMPORARY: Use a mock user ID if no authenticated user
    const userId = req.user?.id || 'mock-user-id';
    console.log('Searching chat history for user:', userId);
    const { query, startDate, endDate } = req.query;

    // Build search criteria
    const searchCriteria = { userId };

    // Add date range if provided
    if (startDate || endDate) {
      searchCriteria.chatDate = {};
      if (startDate) searchCriteria.chatDate.$gte = new Date(startDate);
      if (endDate) {
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999); // Set to end of day
        searchCriteria.chatDate.$lte = endDateTime;
      }
    }

    // Add text search if provided
    let chatHistory;
    if (query) {
      chatHistory = await ChatHistory.find({
        ...searchCriteria,
        'messages.text': { $regex: query, $options: 'i' }
      }).sort({ chatDate: -1, updatedAt: -1 });
    } else {
      chatHistory = await ChatHistory.find(searchCriteria)
        .sort({ chatDate: -1, updatedAt: -1 })
        .limit(30);
    }

    // Group chats by date for the response
    const chatsByDate = {};

    if (chatHistory.length > 0) {
      chatHistory.forEach(chat => {
        const dateKey = new Date(chat.chatDate || chat.createdAt).toISOString().split('T')[0]; // YYYY-MM-DD format

        if (!chatsByDate[dateKey]) {
          chatsByDate[dateKey] = [];
        }

        chatsByDate[dateKey].push(chat);
      });

      // Sort chats within each date by updatedAt
      Object.keys(chatsByDate).forEach(dateKey => {
        chatsByDate[dateKey].sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));
      });
    }

    res.status(200).json({
      success: true,
      data: chatHistory.length > 0 ? { byDate: chatsByDate, allChats: chatHistory } : []
    });
  } catch (error) {
    console.error('Error searching chat history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to search chat history',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Convert speech to text using Azure Speech Service
exports.speechToText = async (req, res) => {
  try {
    // TEMPORARY: Log the request
    console.log('Processing speech-to-text request');
    const audioFile = req.file;
    const language = req.body.language || 'en-US';

    if (!audioFile) {
      console.log('No audio file provided, returning mock response');
      // Return a mock response for testing
      return res.status(200).json({
        success: true,
        data: {
          text: language.startsWith('en') ?
            'What is the weather forecast for tomorrow?' :
            'कल का मौसम कैसा रहेगा?'
        }
      });
    }

    // Map language codes to Azure Speech Service language codes
    const languageMap = {
      'en': 'en-US',
      'hi': 'hi-IN',
      'te': 'te-IN',
      'ta': 'ta-IN',
      'kn': 'kn-IN',
      'ml': 'ml-IN',
      'pa': 'pa-IN',
      'bn': 'bn-IN',
      'gu': 'gu-IN',
      'mr': 'mr-IN'
    };

    const speechLanguage = languageMap[language] || 'en-US';

    // For testing, return a mock response
    console.log('Using mock speech-to-text response');

    // Generate mock text based on the language
    let mockText = '';
    if (language === 'en') {
      mockText = 'What is the weather forecast for tomorrow?';
    } else if (language === 'hi') {
      mockText = 'कल का मौसम कैसा रहेगा?';
    } else {
      mockText = 'What is the weather forecast for tomorrow?';
    }

    // Clean up the temporary file
    fs.unlinkSync(audioFile.path);

    return res.status(200).json({
      success: true,
      data: {
        text: mockText
      }
    });
  } catch (error) {
    console.error('Error in speech to text conversion:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to convert speech to text',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Process a chat message with Azure OpenAI
exports.processMessage = async (req, res) => {
  try {
    // TEMPORARY: Use a mock user ID if no authenticated user
    const userId = req.user?.id || 'mock-user-id';
    console.log('Processing message for user:', userId);
    const { text, language, contextId } = req.body;

    if (!text) {
      return res.status(400).json({
        success: false,
        message: 'Message text is required'
      });
    }

    // Get today's date at midnight (to use as a key for today's chat)
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Find chat history for today
    let todayChat = await ChatHistory.findOne({
      userId,
      chatDate: {
        $gte: today,
        $lt: new Date(today.getTime() + 24 * 60 * 60 * 1000)
      }
    }).sort({ updatedAt: -1 });

    // Get user context from chat history (for AI context)
    const previousChats = await ChatHistory.find({ userId })
      .sort({ updatedAt: -1 })
      .limit(2);

    // Build conversation history for context
    let conversationHistory = [];
    if (previousChats.length > 0) {
      const recentMessages = previousChats[0].messages.slice(-5); // Get last 5 messages for context
      conversationHistory = recentMessages.map(msg => ({
        role: msg.sender === 'user' ? 'user' : 'assistant',
        content: msg.text
      }));
    }

    // Prepare system prompt based on user's context
    let systemPrompt = `You are an agricultural expert AI assistant named Quamin SmartBot.
    You specialize in providing advice on farming, crops, soil health, and agricultural best practices.
    Be concise, helpful, and provide practical advice that farmers can implement.
    The user's preferred language is: ${language}. Respond in this language.`;

    // Add context from previous analyses if available
    if (previousChats.length > 0 && previousChats[0].contextData && previousChats[0].contextData.analyses) {
      const previousAnalyses = previousChats[0].contextData.analyses;
      if (previousAnalyses.length > 0) {
        systemPrompt += `\n\nRecent crop analyses: ${JSON.stringify(previousAnalyses.slice(-1))}`;
      }
    }

    // Call Azure OpenAI
    const messages = [
      { role: 'system', content: systemPrompt },
      ...conversationHistory,
      { role: 'user', content: text }
    ];

    console.log('Calling Azure OpenAI for chat processing with deployment name:', process.env.AZURE_OPENAI_DEPLOYMENT_NAME);
    let result;
    try {
      result = await client.getChatCompletions(
        process.env.AZURE_OPENAI_DEPLOYMENT_NAME,
        messages,
        { maxTokens: 500 }
      );
    } catch (error) {
      console.error('Error calling Azure OpenAI:', error);
      // Return a mock response for testing
      return res.status(200).json({
        success: true,
        data: {
          response: `I'm your agricultural assistant. Based on your question about "${text}", here's my advice: For most crops, ensure proper irrigation, regular monitoring for pests and diseases, and timely application of appropriate fertilizers. Would you like more specific information about a particular crop or farming practice?`,
          followupQuestions: [
            'What crops are you currently growing?',
            'Do you have any specific issues with your crops?',
            'Would you like information about sustainable farming practices?'
          ]
        }
      });
    }

    const botResponse = result.choices[0].message.content;

    // Generate followup questions based on the conversation
    const followupPrompt = `Based on the conversation so far, generate 3 relevant follow-up questions that the farmer might want to ask next. Return ONLY a JSON array of strings with no additional text. Example: ["Question 1?", "Question 2?", "Question 3?"]`;

    console.log('Calling Azure OpenAI for followup questions with deployment name:', process.env.AZURE_OPENAI_DEPLOYMENT_NAME);
    let followupResult;
    try {
      followupResult = await client.getChatCompletions(
        process.env.AZURE_OPENAI_DEPLOYMENT_NAME,
        [
          ...messages,
          { role: 'assistant', content: botResponse },
          { role: 'user', content: followupPrompt }
        ],
        { maxTokens: 200 }
      );
    } catch (error) {
      console.error('Error generating followup questions:', error);
      // Use default followup questions
      followupQuestions = [
        'What crops are you currently growing?',
        'Do you have any specific issues with your crops?',
        'Would you like information about sustainable farming practices?'
      ];

      // Save the message to chat history
      await saveMessageToHistory(userId, text, botResponse, language, contextId, followupQuestions);

      // Return the response
      return res.status(200).json({
        success: true,
        data: {
          response: botResponse,
          followupQuestions
        }
      });
    }

    let followupQuestions = [];
    try {
      const followupContent = followupResult.choices[0].message.content;
      const jsonMatch = followupContent.match(/\[.*\]/s);
      if (jsonMatch) {
        followupQuestions = JSON.parse(jsonMatch[0]);
      } else {
        // Fallback questions if parsing fails
        followupQuestions = [
          'Can you tell me more about your farm?',
          'What crops are you currently growing?',
          'Have you noticed any issues with your crops recently?'
        ];
      }
    } catch (error) {
      console.error('Error parsing followup questions:', error);
      followupQuestions = [
        'Can you tell me more about your farm?',
        'What crops are you currently growing?',
        'Have you noticed any issues with your crops recently?'
      ];
    }

    // Save user message and bot response
    const userMessage = {
      text,
      sender: 'user',
      timestamp: new Date(),
      language
    };

    const botMessage = {
      text: botResponse,
      sender: 'bot',
      timestamp: new Date(),
      language,
      followupQuestions
    };

    // If we didn't find a chat for today, create a new one
    if (!todayChat) {
      // Get user details
      const user = await User.findById(userId);

      todayChat = new ChatHistory({
        userId,
        userName: user?.name || 'User',
        userRole: user?.role || 'Farmer',
        messages: [],
        contextData: { contextId: contextId || Date.now().toString() },
        chatDate: today
      });
    }

    // Add messages to chat history
    todayChat.messages.push(userMessage);
    todayChat.messages.push(botMessage);
    await todayChat.save();

    console.log(`Saved messages to chat history for ${today.toISOString().split('T')[0]}`);

    res.status(200).json({
      success: true,
      data: {
        response: botResponse,
        followupQuestions
      }
    });
  } catch (error) {
    console.error('Error processing message:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process message',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Save a new message to chat history
exports.saveMessage = async (req, res) => {
  try {
    // TEMPORARY: Use a mock user ID if no authenticated user
    const userId = req.user?.id || 'mock-user-id';
    console.log('Saving message for user:', userId);
    const { text, sender, language, attachments, followupQuestions, contextId } = req.body;

    // Validate required fields
    if (!text || !sender) {
      return res.status(400).json({
        success: false,
        message: 'Text and sender are required fields'
      });
    }

    // Get user details
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get today's date at midnight
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Find chat history for today
    let chatHistory = await ChatHistory.findOne({
      userId,
      chatDate: {
        $gte: today,
        $lt: new Date(today.getTime() + 24 * 60 * 60 * 1000)
      }
    }).sort({ updatedAt: -1 });

    // If no chat history exists for today, create a new one
    if (!chatHistory) {
      chatHistory = new ChatHistory({
        userId,
        userName: user.name,
        userRole: user.role,
        messages: [],
        contextData: { contextId: contextId || Date.now().toString() },
        chatDate: today
      });
    }

    // Add the new message
    const newMessage = {
      text,
      sender,
      timestamp: new Date(),
      language: language || user.preferredLanguage || 'en',
      attachments: attachments || [],
      followupQuestions: followupQuestions || [],
      contextId
    };

    chatHistory.messages.push(newMessage);
    await chatHistory.save();

    res.status(201).json({
      success: true,
      data: newMessage
    });
  } catch (error) {
    console.error('Error saving message:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to save message',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get image analysis history for a user
exports.getImageAnalysisHistory = async (req, res) => {
  try {
    // Get user ID from authenticated user or use mock ID for testing
    const userId = req.user?.id || 'mock-user-id';
    console.log('Getting image analysis history for user:', userId);

    // Get query parameters for pagination and date filtering
    const { page = 1, limit = 20, startDate, endDate } = req.query;
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);

    // Build query criteria
    const criteria = { userId };

    // Add date range if provided
    if (startDate || endDate) {
      criteria.createdAt = {};
      if (startDate) criteria.createdAt.$gte = new Date(startDate);
      if (endDate) {
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999); // Set to end of day
        criteria.createdAt.$lte = endDateTime;
      }
    }

    // Find all chat histories that contain image analyses
    const chatHistories = await ChatHistory.find(criteria)
      .sort({ createdAt: -1 })
      .lean(); // Use lean() for better performance

    // Extract all image analyses from chat histories
    let allAnalyses = [];

    chatHistories.forEach(chat => {
      // Extract from contextData.analyses
      if (chat.contextData && chat.contextData.analyses && Array.isArray(chat.contextData.analyses)) {
        chat.contextData.analyses.forEach(analysis => {
          allAnalyses.push({
            ...analysis,
            chatId: chat._id,
            userName: chat.userName,
            userRole: chat.userRole,
            chatDate: chat.chatDate || chat.createdAt
          });
        });
      }

      // Also extract from message attachments for backward compatibility
      chat.messages.forEach(message => {
        if (message.sender === 'bot' && message.attachments && Array.isArray(message.attachments)) {
          message.attachments.forEach(attachment => {
            if (attachment.type === 'image' && attachment.analysisResult) {
              // Check if this analysis is already included (to avoid duplicates)
              const isDuplicate = allAnalyses.some(a =>
                a.imageUrl === attachment.url &&
                a.timestamp && new Date(a.timestamp).getTime() === new Date(message.timestamp).getTime()
              );

              if (!isDuplicate) {
                allAnalyses.push({
                  timestamp: message.timestamp,
                  imageUrl: attachment.url,
                  result: attachment.analysisResult,
                  text: message.text,
                  chatId: chat._id,
                  userName: chat.userName,
                  userRole: chat.userRole,
                  chatDate: chat.chatDate || chat.createdAt
                });
              }
            }
          });
        }
      });
    });

    // Sort analyses by timestamp (newest first)
    allAnalyses.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    // Apply pagination
    const totalAnalyses = allAnalyses.length;
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedAnalyses = allAnalyses.slice(startIndex, endIndex);

    // Group analyses by date
    const analysesByDate = {};
    paginatedAnalyses.forEach(analysis => {
      const dateKey = new Date(analysis.timestamp).toISOString().split('T')[0]; // YYYY-MM-DD
      if (!analysesByDate[dateKey]) {
        analysesByDate[dateKey] = [];
      }
      analysesByDate[dateKey].push(analysis);
    });

    // Return the results
    res.status(200).json({
      success: true,
      data: {
        analyses: paginatedAnalyses,
        byDate: analysesByDate,
        pagination: {
          total: totalAnalyses,
          page: pageNum,
          limit: limitNum,
          totalPages: Math.ceil(totalAnalyses / limitNum)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching image analysis history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch image analysis history',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Process image analysis
exports.processImageAnalysis = async (req, res) => {
  try {
    // TEMPORARY: Use a mock user ID if no authenticated user
    const userId = req.user?.id || 'mock-user-id';
    console.log('Processing image analysis for user:', userId);
    console.log('Request body:', req.body);
    const { imageUrl, contextId } = req.body;
    console.log('Image URL:', imageUrl);
    const userQuery = req.body.text || 'Please analyze this crop image';
    const userLanguage = req.body.language || 'en';

    if (!imageUrl) {
      return res.status(400).json({
        success: false,
        message: 'Image URL is required'
      });
    }

    // Download the image to a temporary location
    const tempDir = path.join(__dirname, '../uploads/temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    const imageName = path.basename(imageUrl);
    const tempImagePath = path.join(tempDir, imageName);

    try {
      await downloadImage(imageUrl, tempImagePath);
    } catch (error) {
      console.error('Error downloading image:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to process image'
      });
    }

    // Convert image to base64
    const base64Image = await imageToBase64(tempImagePath);

    // Get user context from chat history
    const previousChats = await ChatHistory.find({ userId })
      .sort({ updatedAt: -1 })
      .limit(1);

    let contextInfo = '';
    if (previousChats.length > 0 && previousChats[0].contextData && previousChats[0].contextData.analyses) {
      const previousAnalyses = previousChats[0].contextData.analyses;
      if (previousAnalyses.length > 0) {
        contextInfo = `Previous analyses: ${JSON.stringify(previousAnalyses.slice(-2))}`;
      }
    }

    // Prepare the prompt for GPT-4o
    const systemPrompt = `You are an agricultural expert AI assistant specialized in analyzing crop, soil, and seed images.
    Provide detailed analysis including:
    1. Identification of the crop/plant/soil
    2. Health assessment with confidence level
    3. Detection of any issues (diseases, pests, nutrient deficiencies)
    4. Specific recommendations for the farmer
    5. Suggest 3 relevant follow-up questions the farmer might want to ask

    Format your response as a structured JSON object with the following fields:
    {
      "analysis": {
        "type": "crop|soil|seed",
        "identifiedAs": "name of crop/soil type",
        "confidence": 0.XX,
        "healthStatus": "good|moderate|poor",
        "issues": ["list of issues"],
        "recommendations": ["list of recommendations"]
      },
      "followupQuestions": ["question1", "question2", "question3"],
      "textResponse": "A natural language summary of the analysis"
    }

    The user's preferred language is: ${userLanguage}. If this is not English, provide the textResponse in that language while keeping the JSON structure in English.
    ${contextInfo}`;

    // Call Azure OpenAI with GPT-4o
    console.log('Calling Azure OpenAI with deployment name:', process.env.AZURE_OPENAI_DEPLOYMENT_NAME);
    let result;
    try {
      result = await client.getChatCompletions(
        process.env.AZURE_OPENAI_DEPLOYMENT_NAME,
        [
          { role: "system", content: systemPrompt },
          { role: "user", content: [
            { type: "text", text: userQuery },
            { type: "image_url", image_url: { url: `data:image/jpeg;base64,${base64Image}` } }
          ]}
        ],
        { maxTokens: 800 }
      );
    } catch (error) {
      console.error('Error calling Azure OpenAI:', error);
      // Return a mock response for testing
      return res.status(200).json({
        success: true,
        data: {
          message: {
            text: `Analysis Results:\n- Identified: wheat (92% confidence)\n- Health Status: good\n- Issues: Possible nitrogen deficiency\n- Recommendations: \n  * Apply nitrogen-rich fertilizer\n  * Ensure proper irrigation\n  * Monitor for pests`,
            attachment: {
              url: imageUrl,
              type: 'image'
            }
          },
          analysisResult: {
            type: 'crop',
            healthStatus: 'good',
            identifiedAs: 'wheat',
            confidence: 0.92,
            issues: ['Possible nitrogen deficiency'],
            recommendations: [
              'Apply nitrogen-rich fertilizer',
              'Ensure proper irrigation',
              'Monitor for pests'
            ]
          },
          followupQuestions: [
            'What fertilizers do you recommend for wheat?',
            'How often should I irrigate my wheat crop?',
            'What are common pests that affect wheat?'
          ]
        }
      });
    }

    // Clean up the temporary file
    fs.unlinkSync(tempImagePath);

    // Parse the response
    let analysisResult;
    let followupQuestions;
    let analysisText;

    try {
      const responseContent = result.choices[0].message.content;
      const jsonMatch = responseContent.match(/\{[\s\S]*\}/); // Extract JSON from response

      if (jsonMatch) {
        const parsedResponse = JSON.parse(jsonMatch[0]);
        analysisResult = parsedResponse.analysis;
        followupQuestions = parsedResponse.followupQuestions;
        analysisText = parsedResponse.textResponse;
      } else {
        // Fallback if JSON parsing fails
        analysisResult = {
          type: 'unknown',
          healthStatus: 'unknown',
          identifiedAs: 'unknown',
          confidence: 0.5,
          issues: ['Could not determine issues'],
          recommendations: ['Please consult with a local agricultural expert']
        };
        followupQuestions = [
          'Can you try with a clearer image?',
          'What specific concerns do you have about your crop?',
          'When did you first notice these issues?'
        ];
        analysisText = responseContent;
      }
    } catch (error) {
      console.error('Error parsing AI response:', error);
      // Fallback values
      analysisResult = {
        type: 'crop',
        healthStatus: 'unknown',
        identifiedAs: 'unknown',
        confidence: 0.5,
        issues: ['Analysis error'],
        recommendations: ['Please try again with a different image']
      };
      followupQuestions = [
        'Can you try with a clearer image?',
        'What specific concerns do you have about your crop?',
        'When did you first notice these issues?'
      ];
      analysisText = 'I encountered an error analyzing this image. Please try again with a clearer image.';
    }

    // Find the chat history
    let chatHistory = await ChatHistory.findOne({ userId }).sort({ updatedAt: -1 });

    if (!chatHistory) {
      // Create a new chat history with default values
      chatHistory = new ChatHistory({
        userId,
        userName: 'User',
        userRole: 'Farmer',
        messages: [],
        contextData: {}
      });

      // Try to get user details if available
      try {
        const User = mongoose.model('User');
        const user = await User.findById(userId);
        if (user) {
          chatHistory.userName = user.name || 'User';
          chatHistory.userRole = user.role || 'Farmer';
        }
      } catch (error) {
        console.log('User not found, using default values');
      }
    }

    // Add the analysis message
    const newMessage = {
      text: analysisText,
      sender: 'bot',
      timestamp: new Date(),
      language: req.body.language || 'en',
      attachments: [{
        type: 'image',
        url: imageUrl,
        analysisResult
      }],
      followupQuestions,
      contextId
    };

    chatHistory.messages.push(newMessage);

    // Update context data with analysis results
    if (!chatHistory.contextData.analyses) {
      chatHistory.contextData.analyses = [];
    }

    chatHistory.contextData.analyses.push({
      timestamp: new Date(),
      imageUrl,
      result: analysisResult
    });

    await chatHistory.save();

    res.status(200).json({
      success: true,
      data: {
        analysisResult,
        followupQuestions,
        message: newMessage
      }
    });
  } catch (error) {
    console.error('Error processing image analysis:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process image analysis',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get image analysis history for a user
exports.getImageAnalysisHistory = async (req, res) => {
  try {
    // TEMPORARY: Use a mock user ID if no authenticated user
    const userId = req.user?.id || 'mock-user-id';
    console.log('Getting image analysis history for user:', userId);

    // Pagination parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Find chat history entries that contain image attachments
    const chatHistory = await ChatHistory.find({
      userId,
      'messages.attachments.type': 'image'
    }).sort({ updatedAt: -1 });

    // Extract messages with image attachments
    const imageAnalyses = [];

    if (chatHistory.length > 0) {
      chatHistory.forEach(chat => {
        chat.messages.forEach(message => {
          if (message.attachments && message.attachments.length > 0) {
            message.attachments.forEach(attachment => {
              if (attachment.type === 'image' && attachment.analysisResult) {
                imageAnalyses.push({
                  id: message._id,
                  chatId: chat._id,
                  timestamp: message.timestamp,
                  imageUrl: attachment.url,
                  analysis: attachment.analysisResult,
                  text: message.text
                });
              }
            });
          }
        });
      });
    }

    // If no image analyses found, create mock data
    if (imageAnalyses.length === 0) {
      console.log('No image analysis history found, creating mock data');

      // Mock data for testing
      const mockAnalyses = [
        {
          id: 'mock-analysis-1',
          chatId: 'mock-chat-1',
          timestamp: new Date(Date.now() - 86400000),
          imageUrl: '/uploads/chat-images/mock-wheat-image.jpg',
          analysis: {
            type: 'crop',
            healthStatus: 'good',
            identifiedAs: 'wheat',
            confidence: 0.92,
            issues: [],
            recommendations: ['Continue with current practices', 'Monitor for pests regularly']
          },
          text: 'Your wheat crop appears healthy. The plants show good color and development for this growth stage.'
        },
        {
          id: 'mock-analysis-2',
          chatId: 'mock-chat-2',
          timestamp: new Date(Date.now() - 172800000),
          imageUrl: '/uploads/chat-images/mock-tomato-image.jpg',
          analysis: {
            type: 'crop',
            healthStatus: 'moderate',
            identifiedAs: 'tomato',
            confidence: 0.87,
            issues: ['Early signs of leaf spot'],
            recommendations: ['Apply fungicide as preventative measure', 'Ensure proper spacing between plants for airflow']
          },
          text: 'I have identified your tomato plants with some early signs of leaf spot disease. This is common but should be addressed soon.'
        }
      ];

      // Return mock data
      return res.status(200).json({
        success: true,
        data: {
          analyses: mockAnalyses,
          pagination: {
            total: mockAnalyses.length,
            page: 1,
            pages: 1,
            limit: mockAnalyses.length
          }
        }
      });
    }

    // Sort by timestamp (newest first)
    imageAnalyses.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    // Apply pagination
    const paginatedAnalyses = imageAnalyses.slice(skip, skip + limit);

    res.status(200).json({
      success: true,
      data: {
        analyses: paginatedAnalyses,
        pagination: {
          total: imageAnalyses.length,
          page,
          pages: Math.ceil(imageAnalyses.length / limit),
          limit
        }
      }
    });
  } catch (error) {
    console.error('Error fetching image analysis history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch image analysis history',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};