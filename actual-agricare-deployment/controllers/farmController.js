const Farm = require('../models/Farm');
const FarmData = require('../models/FarmData');
const mongoose = require('mongoose');
const axios = require('axios');

// Get all farms for a farmer
exports.getFarmerFarms = async (farmerId) => {
  return await Farm.find({ farmerId });
};

// Get a single farm
exports.getFarmById = async (farmId) => {
  return await Farm.findById(farmId);
};

// Create a new farm
exports.createFarm = async (farmData) => {
  const farm = new Farm(farmData);
  return await farm.save();
};

// Update a farm
exports.updateFarm = async (farmId, updateData) => {
  return await Farm.findByIdAndUpdate(
    farmId,
    { $set: updateData },
    { new: true, runValidators: true }
  );
};

// Delete a farm
exports.deleteFarm = async (farmId) => {
  return await Farm.findByIdAndDelete(farmId);
};

// Get nearby farms within a certain radius (in kilometers)
exports.getNearbyFarms = async (farmId, radiusKm = 10) => {
  const farm = await Farm.findById(farmId);
  if (!farm || !farm.location) {
    throw new Error('Farm not found or location not set');
  }

  return await Farm.find({
    _id: { $ne: farmId },
    location: {
      $near: {
        $geometry: farm.location,
        $maxDistance: radiusKm * 1000 // Convert km to meters
      }
    }
  }).select('farmerName cropType location');
};

// Get location-based predictions
exports.getLocationBasedPredictions = async (farmId) => {
  const farm = await Farm.findById(farmId);
  if (!farm || !farm.location) {
    throw new Error('Farm not found or location not set');
  }

  // Get nearby farms with similar crops
  const nearbyFarms = await Farm.find({
    _id: { $ne: farmId },
    cropType: farm.cropType,
    location: {
      $near: {
        $geometry: farm.location,
        $maxDistance: 50000 // 50km radius
      }
    }
  });

  // Analyze crop performance in the area
  const areaAnalysis = {
    totalSimilarFarms: nearbyFarms.length,
    averageYield: 0, // To be implemented with actual yield data
    commonIssues: [], // To be implemented with historical data
    recommendations: []
  };

  // Get historical weather data for the location
  const weatherHistory = await getWeatherHistory(farm.location);

  // Get soil conditions from nearby farms
  const soilConditions = await FarmData.find({
    farmId: { $in: nearbyFarms.map(f => f._id) }
  }).select('soilData');

  // Generate predictions and recommendations
  const predictions = {
    weather: {
      shortTerm: weatherHistory.shortTermForecast,
      longTerm: weatherHistory.longTermForecast
    },
    soil: {
      type: analyzeSoilConditions(soilConditions),
      recommendations: generateSoilRecommendations(soilConditions)
    },
    crops: {
      bestPlantingTime: calculateOptimalPlantingTime(weatherHistory, farm.cropType),
      expectedYield: estimateYield(areaAnalysis, weatherHistory),
      risks: assessRisks(weatherHistory, soilConditions)
    }
  };

  return {
    areaAnalysis,
    predictions
  };
};

// Get farm statistics
exports.getFarmStats = async (farmerId) => {
  const stats = await Farm.aggregate([
    { $match: { farmerId } },
    {
      $group: {
        _id: null,
        totalFarms: { $sum: 1 },
        // Add more aggregation fields as needed
      }
    }
  ]);
  return stats[0] || { totalFarms: 0 };
};

// Search farms
exports.searchFarms = async (query) => {
  const searchCriteria = {};
  
  if (query.state) {
    searchCriteria.state = new RegExp(query.state, 'i');
  }
  
  if (query.district) {
    searchCriteria.district = new RegExp(query.district, 'i');
  }
  
  if (query.cropType) {
    searchCriteria.cropType = new RegExp(query.cropType, 'i');
  }

  // Add geospatial search if coordinates are provided
  if (query.latitude && query.longitude && query.radius) {
    searchCriteria.location = {
      $near: {
        $geometry: {
          type: 'Point',
          coordinates: [parseFloat(query.longitude), parseFloat(query.latitude)]
        },
        $maxDistance: parseFloat(query.radius) * 1000 // Convert km to meters
      }
    };
  }
  
  return await Farm.find(searchCriteria);
};

// Get weather history and forecast for a location
const getWeatherHistory = async (location) => {
  try {
    // Using OpenWeatherMap API for weather data
    const API_KEY = process.env.OPENWEATHER_API_KEY;
    const { coordinates } = location;
    const [longitude, latitude] = coordinates;

    // Get current weather
    const currentWeather = await axios.get(
      `https://api.openweathermap.org/data/2.5/weather?lat=${latitude}&lon=${longitude}&appid=${API_KEY}&units=metric`
    );

    // Get 5-day forecast
    const forecast = await axios.get(
      `https://api.openweathermap.org/data/2.5/forecast?lat=${latitude}&lon=${longitude}&appid=${API_KEY}&units=metric`
    );

    // Get historical data for the past 5 days
    const fiveDaysAgo = Math.floor(Date.now() / 1000) - (5 * 24 * 60 * 60);
    const historical = await axios.get(
      `https://api.openweathermap.org/data/2.5/timemachine?lat=${latitude}&lon=${longitude}&dt=${fiveDaysAgo}&appid=${API_KEY}&units=metric`
    );

    return {
      current: currentWeather.data,
      shortTermForecast: forecast.data.list.slice(0, 8), // Next 24 hours (3-hour intervals)
      longTermForecast: forecast.data.list, // 5-day forecast
      historical: historical.data
    };
  } catch (error) {
    console.error('Weather API Error:', error);
    return {
      current: null,
      shortTermForecast: [],
      longTermForecast: [],
      historical: null
    };
  }
};

// Analyze soil conditions based on sensor data
const analyzeSoilConditions = (soilData) => {
  if (!soilData || soilData.length === 0) return null;

  // Calculate averages from nearby farms
  const averages = soilData.reduce((acc, data) => {
    if (!data.soilData) return acc;
    const { nitrogen, phosphorus, potassium, ph } = data.soilData;
    return {
      nitrogen: acc.nitrogen + (nitrogen || 0),
      phosphorus: acc.phosphorus + (phosphorus || 0),
      potassium: acc.potassium + (potassium || 0),
      ph: acc.ph + (ph || 0),
      count: acc.count + 1
    };
  }, { nitrogen: 0, phosphorus: 0, potassium: 0, ph: 0, count: 0 });

  if (averages.count === 0) return null;

  const avgNitrogen = averages.nitrogen / averages.count;
  const avgPhosphorus = averages.phosphorus / averages.count;
  const avgPotassium = averages.potassium / averages.count;
  const avgPH = averages.ph / averages.count;

  // Determine soil characteristics
  const characteristics = [];
  if (avgPH < 6.0) characteristics.push('Acidic soil - may need liming');
  else if (avgPH > 7.5) characteristics.push('Alkaline soil - may need sulfur');
  else characteristics.push('Optimal pH range');

  if (avgNitrogen < 140) characteristics.push('Low nitrogen - may need fertilization');
  if (avgPhosphorus < 10) characteristics.push('Low phosphorus - may need phosphate');
  if (avgPotassium < 200) characteristics.push('Low potassium - may need potash');

  return {
    type: determineSoilType(avgPH, avgNitrogen, avgPhosphorus, avgPotassium),
    characteristics,
    averages: {
      nitrogen: avgNitrogen,
      phosphorus: avgPhosphorus,
      potassium: avgPotassium,
      ph: avgPH
    }
  };
};

// Generate soil recommendations based on analysis
const generateSoilRecommendations = (soilData) => {
  const analysis = analyzeSoilConditions(soilData);
  if (!analysis) return [];

  const recommendations = [];
  const { averages, characteristics } = analysis;

  // pH recommendations
  if (averages.ph < 6.0) {
    recommendations.push({
      type: 'soil_amendment',
      action: 'Apply agricultural lime',
      reason: 'Increase soil pH to optimal range',
      priority: 'high'
    });
  } else if (averages.ph > 7.5) {
    recommendations.push({
      type: 'soil_amendment',
      action: 'Apply agricultural sulfur',
      reason: 'Decrease soil pH to optimal range',
      priority: 'high'
    });
  }

  // Nutrient recommendations
  if (averages.nitrogen < 140) {
    recommendations.push({
      type: 'fertilization',
      action: 'Apply nitrogen-rich fertilizer',
      reason: 'Increase nitrogen levels for better crop growth',
      priority: 'medium'
    });
  }

  if (averages.phosphorus < 10) {
    recommendations.push({
      type: 'fertilization',
      action: 'Apply phosphate fertilizer',
      reason: 'Improve phosphorus levels for root development',
      priority: 'medium'
    });
  }

  if (averages.potassium < 200) {
    recommendations.push({
      type: 'fertilization',
      action: 'Apply potash fertilizer',
      reason: 'Enhance potassium levels for crop quality',
      priority: 'medium'
    });
  }

  return recommendations;
};

// Calculate optimal planting time based on weather and crop type
const calculateOptimalPlantingTime = (weatherHistory, cropType) => {
  if (!weatherHistory || !weatherHistory.longTermForecast) {
    return null;
  }

  const forecast = weatherHistory.longTermForecast;
  
  // Define optimal conditions for different crops
  const cropConditions = {
    rice: { minTemp: 20, maxTemp: 35, minRainfall: 5 },
    wheat: { minTemp: 15, maxTemp: 30, minRainfall: 2 },
    corn: { minTemp: 18, maxTemp: 32, minRainfall: 3 },
    cotton: { minTemp: 21, maxTemp: 35, minRainfall: 1 }
  };

  const conditions = cropConditions[cropType.toLowerCase()] || cropConditions.rice;

  // Find the best 5-day window in the forecast
  let bestWindow = null;
  let bestScore = -Infinity;

  for (let i = 0; i < forecast.length - 5; i++) {
    const window = forecast.slice(i, i + 5);
    let score = 0;

    for (const day of window) {
      const temp = day.main.temp;
      const rain = day.rain?.['3h'] || 0;

      // Score based on temperature and rainfall
      if (temp >= conditions.minTemp && temp <= conditions.maxTemp) {
        score += 2;
      }
      if (rain >= conditions.minRainfall) {
        score += 1;
      }
    }

    if (score > bestScore) {
      bestScore = score;
      bestWindow = window[0].dt * 1000; // Convert to milliseconds
    }
  }

  return bestWindow ? new Date(bestWindow) : null;
};

// Estimate yield based on historical data and current conditions
const estimateYield = (areaAnalysis, weatherHistory) => {
  if (!weatherHistory || !areaAnalysis) {
    return { low: 0, expected: 0, high: 0 };
  }

  // Base yield from historical data (to be replaced with actual historical yield data)
  const baseYield = 100; // Example: 100 quintals per hectare

  // Weather impact factors
  const weatherImpact = calculateWeatherImpact(weatherHistory);

  // Calculate yield range
  const expected = baseYield * weatherImpact;
  const variation = 0.2; // 20% variation for low/high estimates

  return {
    low: Math.round(expected * (1 - variation)),
    expected: Math.round(expected),
    high: Math.round(expected * (1 + variation))
  };
};

// Assess risks based on weather and soil conditions
const assessRisks = (weatherHistory, soilConditions) => {
  const risks = [];

  if (!weatherHistory || !soilConditions) {
    return risks;
  }

  // Weather-based risks
  const forecast = weatherHistory.shortTermForecast;
  if (forecast) {
    // Check for extreme temperatures
    const highTemp = Math.max(...forecast.map(f => f.main.temp));
    const lowTemp = Math.min(...forecast.map(f => f.main.temp));
    
    if (highTemp > 35) {
      risks.push({
        type: 'weather',
        severity: 'high',
        description: 'Risk of heat stress to crops',
        mitigation: 'Consider additional irrigation and shade measures'
      });
    }

    if (lowTemp < 10) {
      risks.push({
        type: 'weather',
        severity: 'high',
        description: 'Risk of cold damage to crops',
        mitigation: 'Prepare frost protection measures'
      });
    }

    // Check for heavy rainfall
    const maxRainfall = Math.max(...forecast.map(f => f.rain?.['3h'] || 0));
    if (maxRainfall > 30) {
      risks.push({
        type: 'weather',
        severity: 'high',
        description: 'Risk of flooding and water logging',
        mitigation: 'Ensure proper drainage systems are functioning'
      });
    }
  }

  // Soil-based risks
  const soilAnalysis = analyzeSoilConditions(soilConditions);
  if (soilAnalysis) {
    const { averages } = soilAnalysis;

    if (averages.ph < 5.5 || averages.ph > 8.0) {
      risks.push({
        type: 'soil',
        severity: 'medium',
        description: 'Extreme soil pH may affect nutrient availability',
        mitigation: 'Apply appropriate soil amendments'
      });
    }

    if (averages.nitrogen < 100) {
      risks.push({
        type: 'soil',
        severity: 'medium',
        description: 'Low nitrogen levels may limit crop growth',
        mitigation: 'Consider nitrogen fertilization'
      });
    }
  }

  return risks;
};

// Helper function to determine soil type
const determineSoilType = (ph, nitrogen, phosphorus, potassium) => {
  // Simplified soil type determination
  if (ph < 6.0) return 'Acidic';
  if (ph > 7.5) return 'Alkaline';
  return 'Neutral';
};

// Helper function to calculate weather impact on yield
const calculateWeatherImpact = (weatherHistory) => {
  if (!weatherHistory || !weatherHistory.current) return 1.0;

  const { current, shortTermForecast } = weatherHistory;
  let impact = 1.0;

  // Adjust impact based on current conditions
  const temp = current.main.temp;
  if (temp < 15 || temp > 35) impact *= 0.9;
  
  // Adjust for rainfall
  const rainfall = current.rain?.['1h'] || 0;
  if (rainfall > 10) impact *= 0.95;
  if (rainfall < 1) impact *= 0.98;

  return impact;
};

// Get farm details including sensor data
exports.getFarmDetails = async (farmId) => {
  const farm = await Farm.findById(farmId);
  if (!farm) {
    throw new Error('Farm not found');
  }

  // Get the latest sensor data
  const latestData = await FarmData.findOne({ farmId })
    .sort({ timestamp: -1 });

  // Get recent alerts
  const recentAlerts = await FarmData.aggregate([
    { $match: { farmId: mongoose.Types.ObjectId(farmId) } },
    { $unwind: '$alerts' },
    { $sort: { 'alerts.timestamp': -1 } },
    { $limit: 10 }
  ]);

  // Get active tasks
  const tasks = await FarmData.aggregate([
    { $match: { farmId: mongoose.Types.ObjectId(farmId) } },
    { $unwind: '$tasks' },
    { $match: { 'tasks.status': { $ne: 'Completed' } } },
    { $sort: { 'tasks.dueDate': 1 } }
  ]);

  return {
    farm,
    sensorData: latestData?.soilData || null,
    weatherData: latestData?.weatherData || null,
    alerts: recentAlerts.map(a => a.alerts),
    tasks: tasks.map(t => t.tasks)
  };
};

// Add new sensor data
exports.addFarmData = async (farmId, data) => {
  const farmData = new FarmData({
    farmId,
    ...data
  });
  return await farmData.save();
};

// Add task to farm
exports.addFarmTask = async (farmId, taskData) => {
  const farmData = await FarmData.findOne({ farmId })
    .sort({ timestamp: -1 });

  if (!farmData) {
    return await new FarmData({
      farmId,
      tasks: [taskData]
    }).save();
  }

  farmData.tasks.push(taskData);
  return await farmData.save();
};

// Add alert to farm
exports.addFarmAlert = async (farmId, alertData) => {
  const farmData = await FarmData.findOne({ farmId })
    .sort({ timestamp: -1 });

  if (!farmData) {
    return await new FarmData({
      farmId,
      alerts: [alertData]
    }).save();
  }

  farmData.alerts.push(alertData);
  return await farmData.save();
}; 