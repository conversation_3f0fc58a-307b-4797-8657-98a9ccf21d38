const FarmFinancial = require('../models/FarmFinancial');
const User = require('../models/User');

// Get financial data for a specific farmer
exports.getFarmerFinancialData = async (req, res) => {
  try {
    const financialData = await FarmFinancial.findOne({ farmerId: req.params.farmerId });
    
    if (!financialData) {
      return res.status(404).json({
        success: false,
        error: 'Financial data not found for this farmer'
      });
    }
    
    res.status(200).json({
      success: true,
      data: financialData
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

// Get yield history with date range filter
exports.getYieldHistory = async (req, res) => {
  try {
    const { farmerId, startYear, endYear, crop } = req.query;
    
    if (!farmerId) {
      return res.status(400).json({
        success: false,
        error: 'Farmer ID is required'
      });
    }
    
    const financialData = await FarmFinancial.findOne({ farmerId });
    
    if (!financialData) {
      return res.status(404).json({
        success: false,
        error: 'Financial data not found for this farmer'
      });
    }
    
    let yieldHistory = financialData.yieldHistory;
    
    // Apply filters if provided
    if (startYear) {
      yieldHistory = yieldHistory.filter(record => record.year >= parseInt(startYear));
    }
    
    if (endYear) {
      yieldHistory = yieldHistory.filter(record => record.year <= parseInt(endYear));
    }
    
    if (crop) {
      yieldHistory = yieldHistory.filter(record => record.crop.toLowerCase() === crop.toLowerCase());
    }
    
    res.status(200).json({
      success: true,
      count: yieldHistory.length,
      data: yieldHistory
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

// Get payment history with date range filter
exports.getPaymentHistory = async (req, res) => {
  try {
    const { farmerId, startDate, endDate, status } = req.query;
    
    if (!farmerId) {
      return res.status(400).json({
        success: false,
        error: 'Farmer ID is required'
      });
    }
    
    const financialData = await FarmFinancial.findOne({ farmerId });
    
    if (!financialData) {
      return res.status(404).json({
        success: false,
        error: 'Financial data not found for this farmer'
      });
    }
    
    let paymentHistory = financialData.paymentHistory;
    
    // Apply filters if provided
    if (startDate) {
      const startDateObj = new Date(startDate);
      paymentHistory = paymentHistory.filter(payment => new Date(payment.date) >= startDateObj);
    }
    
    if (endDate) {
      const endDateObj = new Date(endDate);
      paymentHistory = paymentHistory.filter(payment => new Date(payment.date) <= endDateObj);
    }
    
    if (status) {
      paymentHistory = paymentHistory.filter(payment => payment.status.toLowerCase() === status.toLowerCase());
    }
    
    res.status(200).json({
      success: true,
      count: paymentHistory.length,
      data: paymentHistory
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

// Get loan details with filters
exports.getLoanDetails = async (req, res) => {
  try {
    const { farmerId, status, bankName } = req.query;
    
    if (!farmerId) {
      return res.status(400).json({
        success: false,
        error: 'Farmer ID is required'
      });
    }
    
    const financialData = await FarmFinancial.findOne({ farmerId });
    
    if (!financialData) {
      return res.status(404).json({
        success: false,
        error: 'Financial data not found for this farmer'
      });
    }
    
    let loans = financialData.loans;
    
    // Apply filters if provided
    if (status) {
      loans = loans.filter(loan => loan.status.toLowerCase() === status.toLowerCase());
    }
    
    if (bankName) {
      loans = loans.filter(loan => loan.bankName.toLowerCase().includes(bankName.toLowerCase()));
    }
    
    res.status(200).json({
      success: true,
      count: loans.length,
      data: loans
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

// Create or update financial data for a farmer
exports.updateFinancialData = async (req, res) => {
  try {
    const { farmerId } = req.params;
    
    // Check if farmer exists
    const farmer = await User.findById(farmerId);
    if (!farmer) {
      return res.status(404).json({
        success: false,
        error: 'Farmer not found'
      });
    }
    
    // Find and update or create new financial data
    const financialData = await FarmFinancial.findOneAndUpdate(
      { farmerId },
      req.body,
      { new: true, upsert: true, runValidators: true }
    );
    
    res.status(200).json({
      success: true,
      data: financialData
    });
  } catch (error) {
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      
      return res.status(400).json({
        success: false,
        error: messages
      });
    } else {
      return res.status(500).json({
        success: false,
        error: 'Server Error'
      });
    }
  }
};

// Add a new loan
exports.addLoan = async (req, res) => {
  try {
    const { farmerId } = req.params;
    
    const financialData = await FarmFinancial.findOne({ farmerId });
    
    if (!financialData) {
      return res.status(404).json({
        success: false,
        error: 'Financial data not found for this farmer'
      });
    }
    
    financialData.loans.push(req.body);
    await financialData.save();
    
    res.status(200).json({
      success: true,
      data: financialData.loans
    });
  } catch (error) {
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      
      return res.status(400).json({
        success: false,
        error: messages
      });
    } else {
      return res.status(500).json({
        success: false,
        error: 'Server Error'
      });
    }
  }
};

// Add a new yield record
exports.addYieldRecord = async (req, res) => {
  try {
    const { farmerId } = req.params;
    
    const financialData = await FarmFinancial.findOne({ farmerId });
    
    if (!financialData) {
      return res.status(404).json({
        success: false,
        error: 'Financial data not found for this farmer'
      });
    }
    
    financialData.yieldHistory.push(req.body);
    await financialData.save();
    
    res.status(200).json({
      success: true,
      data: financialData.yieldHistory
    });
  } catch (error) {
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      
      return res.status(400).json({
        success: false,
        error: messages
      });
    } else {
      return res.status(500).json({
        success: false,
        error: 'Server Error'
      });
    }
  }
};

// Add a payment record
exports.addPaymentRecord = async (req, res) => {
  try {
    const { farmerId } = req.params;
    
    const financialData = await FarmFinancial.findOne({ farmerId });
    
    if (!financialData) {
      return res.status(404).json({
        success: false,
        error: 'Financial data not found for this farmer'
      });
    }
    
    financialData.paymentHistory.push(req.body);
    await financialData.save();
    
    res.status(200).json({
      success: true,
      data: financialData.paymentHistory
    });
  } catch (error) {
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      
      return res.status(400).json({
        success: false,
        error: messages
      });
    } else {
      return res.status(500).json({
        success: false,
        error: 'Server Error'
      });
    }
  }
};

// Generate financial report
exports.generateFinancialReport = async (req, res) => {
  try {
    const { farmerId } = req.params;
    const { startDate, endDate, reportType } = req.query;
    
    const financialData = await FarmFinancial.findOne({ farmerId });
    
    if (!financialData) {
      return res.status(404).json({
        success: false,
        error: 'Financial data not found for this farmer'
      });
    }
    
    // Default date range is current year
    const currentYear = new Date().getFullYear();
    const startDateObj = startDate ? new Date(startDate) : new Date(currentYear, 0, 1);
    const endDateObj = endDate ? new Date(endDate) : new Date(currentYear, 11, 31);
    
    let report = {
      farmerId: farmerId,
      generatedAt: new Date(),
      reportPeriod: {
        startDate: startDateObj,
        endDate: endDateObj
      }
    };
    
    // Generate different types of reports based on reportType
    switch(reportType) {
      case 'loan':
        report.title = 'Loan Summary Report';
        report.loans = financialData.loans;
        report.totalLoanAmount = financialData.loans.reduce((sum, loan) => sum + loan.amount, 0);
        report.totalRemainingAmount = financialData.loans.reduce((sum, loan) => sum + loan.remainingAmount, 0);
        report.averageInterestRate = financialData.loans.reduce((sum, loan) => sum + loan.interestRate, 0) / financialData.loans.length;
        break;
        
      case 'yield':
        report.title = 'Yield Performance Report';
        
        // Filter yield history by date range
        const filteredYieldHistory = financialData.yieldHistory.filter(record => {
          const recordYear = record.year;
          return recordYear >= startDateObj.getFullYear() && recordYear <= endDateObj.getFullYear();
        });
        
        report.yieldHistory = filteredYieldHistory;
        report.totalProduction = filteredYieldHistory.reduce((sum, record) => sum + record.production, 0);
        report.totalRevenue = filteredYieldHistory.reduce((sum, record) => sum + record.revenue, 0);
        report.totalProfit = filteredYieldHistory.reduce((sum, record) => sum + record.profit, 0);
        report.cropwiseAnalysis = {};
        
        // Group by crop type
        filteredYieldHistory.forEach(record => {
          if (!report.cropwiseAnalysis[record.crop]) {
            report.cropwiseAnalysis[record.crop] = {
              totalProduction: 0,
              totalRevenue: 0,
              totalProfit: 0,
              records: []
            };
          }
          
          report.cropwiseAnalysis[record.crop].totalProduction += record.production;
          report.cropwiseAnalysis[record.crop].totalRevenue += record.revenue;
          report.cropwiseAnalysis[record.crop].totalProfit += record.profit;
          report.cropwiseAnalysis[record.crop].records.push(record);
        });
        break;
        
      case 'payment':
        report.title = 'Payment History Report';
        
        // Filter payment history by date range
        const filteredPaymentHistory = financialData.paymentHistory.filter(payment => {
          const paymentDate = new Date(payment.date);
          return paymentDate >= startDateObj && paymentDate <= endDateObj;
        });
        
        report.paymentHistory = filteredPaymentHistory;
        report.totalPayments = filteredPaymentHistory.length;
        report.totalAmountPaid = filteredPaymentHistory.reduce((sum, payment) => sum + payment.amount, 0);
        report.onTimePayments = filteredPaymentHistory.filter(payment => payment.status === 'Paid').length;
        report.delayedPayments = filteredPaymentHistory.filter(payment => payment.status === 'Delayed').length;
        report.missedPayments = filteredPaymentHistory.filter(payment => payment.status === 'Missed').length;
        break;
        
      case 'asset':
        report.title = 'Asset Valuation Report';
        report.assets = financialData.assets;
        report.totalLandValue = financialData.assets.land.reduce((sum, land) => sum + land.value, 0);
        report.totalEquipmentValue = financialData.assets.equipment.reduce((sum, equipment) => sum + equipment.value, 0);
        report.totalLivestockValue = financialData.assets.livestock ? financialData.assets.livestock.reduce((sum, livestock) => sum + livestock.value, 0) : 0;
        report.totalStructuresValue = financialData.assets.structures ? financialData.assets.structures.reduce((sum, structure) => sum + structure.value, 0) : 0;
        report.totalAssetValue = report.totalLandValue + report.totalEquipmentValue + report.totalLivestockValue + report.totalStructuresValue;
        break;
        
      case 'comprehensive':
      default:
        report.title = 'Comprehensive Financial Report';
        report.creditScore = financialData.creditScore;
        report.loans = financialData.loans;
        report.assets = financialData.assets;
        report.yieldHistory = financialData.yieldHistory.filter(record => {
          const recordYear = record.year;
          return recordYear >= startDateObj.getFullYear() && recordYear <= endDateObj.getFullYear();
        });
        report.paymentHistory = financialData.paymentHistory.filter(payment => {
          const paymentDate = new Date(payment.date);
          return paymentDate >= startDateObj && paymentDate <= endDateObj;
        });
        report.insuranceDetails = financialData.insuranceDetails;
        report.subsidies = financialData.subsidies;
        
        // Summary calculations
        report.totalLoanAmount = financialData.loans.reduce((sum, loan) => sum + loan.amount, 0);
        report.totalRemainingAmount = financialData.loans.reduce((sum, loan) => sum + loan.remainingAmount, 0);
        report.totalAssetValue = 
          financialData.assets.land.reduce((sum, land) => sum + land.value, 0) +
          financialData.assets.equipment.reduce((sum, equipment) => sum + equipment.value, 0) +
          (financialData.assets.livestock ? financialData.assets.livestock.reduce((sum, livestock) => sum + livestock.value, 0) : 0) +
          (financialData.assets.structures ? financialData.assets.structures.reduce((sum, structure) => sum + structure.value, 0) : 0);
        
        // Calculate net worth
        report.netWorth = report.totalAssetValue - report.totalRemainingAmount;
        
        // Calculate debt-to-asset ratio
        report.debtToAssetRatio = report.totalRemainingAmount / report.totalAssetValue;
        
        // Calculate loan eligibility based on credit score and debt-to-asset ratio
        if (financialData.creditScore.score >= 750 && report.debtToAssetRatio < 0.4) {
          report.loanEligibility = {
            eligible: true,
            maxAmount: Math.min(1000000, report.totalAssetValue * 0.6),
            interestRate: 7.5,
            term: "5 years"
          };
        } else if (financialData.creditScore.score >= 650 && report.debtToAssetRatio < 0.5) {
          report.loanEligibility = {
            eligible: true,
            maxAmount: Math.min(500000, report.totalAssetValue * 0.4),
            interestRate: 8.5,
            term: "3 years"
          };
        } else if (financialData.creditScore.score >= 550 && report.debtToAssetRatio < 0.6) {
          report.loanEligibility = {
            eligible: true,
            maxAmount: Math.min(200000, report.totalAssetValue * 0.3),
            interestRate: 10.5,
            term: "2 years"
          };
        } else {
          report.loanEligibility = {
            eligible: false,
            reason: "Low credit score or high debt-to-asset ratio"
          };
        }
        break;
    }
    
    res.status(200).json({
      success: true,
      data: report
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};
