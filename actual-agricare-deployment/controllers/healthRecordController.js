const HealthRecord = require('../models/HealthRecord');
const Livestock = require('../models/Livestock');

// Get all health records
exports.getAllHealthRecords = async (req, res) => {
  try {
    const healthRecords = await HealthRecord.find()
      .populate('livestockId')
      .populate('vetId', 'name email specialization');
    
    res.status(200).json({
      success: true,
      count: healthRecords.length,
      data: healthRecords
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

// Get health records for a specific livestock
exports.getLivestockHealthRecords = async (req, res) => {
  try {
    const healthRecords = await HealthRecord.find({ livestockId: req.params.livestockId })
      .populate('vetId', 'name email specialization')
      .sort({ date: -1 }); // Sort by date descending (newest first)
    
    res.status(200).json({
      success: true,
      count: healthRecords.length,
      data: healthRecords
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

// Get health records created by a specific veterinarian
exports.getVetHealthRecords = async (req, res) => {
  try {
    const healthRecords = await HealthRecord.find({ vetId: req.params.vetId })
      .populate('livestockId')
      .sort({ date: -1 }); // Sort by date descending (newest first)
    
    res.status(200).json({
      success: true,
      count: healthRecords.length,
      data: healthRecords
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

// Create a new health record
exports.createHealthRecord = async (req, res) => {
  try {
    // Check if the livestock exists
    const livestock = await Livestock.findById(req.body.livestockId);
    
    if (!livestock) {
      return res.status(404).json({
        success: false,
        error: 'Livestock not found'
      });
    }
    
    const healthRecord = await HealthRecord.create(req.body);
    
    // Update livestock health status based on the health record
    if (req.body.diagnosis.toLowerCase().includes('healthy') || 
        req.body.type === 'Regular Checkup') {
      livestock.healthStatus = 'healthy';
    } else if (req.body.type === 'Emergency' || 
               req.body.diagnosis.toLowerCase().includes('critical')) {
      livestock.healthStatus = 'critical';
    } else {
      livestock.healthStatus = 'concerning';
    }
    
    // Update last checkup date
    livestock.lastCheckup = req.body.date || Date.now();
    
    await livestock.save();
    
    res.status(201).json({
      success: true,
      data: healthRecord
    });
  } catch (error) {
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      
      return res.status(400).json({
        success: false,
        error: messages
      });
    } else {
      return res.status(500).json({
        success: false,
        error: 'Server Error'
      });
    }
  }
};

// Update a health record
exports.updateHealthRecord = async (req, res) => {
  try {
    const healthRecord = await HealthRecord.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    
    if (!healthRecord) {
      return res.status(404).json({
        success: false,
        error: 'Health record not found'
      });
    }
    
    res.status(200).json({
      success: true,
      data: healthRecord
    });
  } catch (error) {
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      
      return res.status(400).json({
        success: false,
        error: messages
      });
    } else {
      return res.status(500).json({
        success: false,
        error: 'Server Error'
      });
    }
  }
};

// Delete a health record
exports.deleteHealthRecord = async (req, res) => {
  try {
    const healthRecord = await HealthRecord.findById(req.params.id);
    
    if (!healthRecord) {
      return res.status(404).json({
        success: false,
        error: 'Health record not found'
      });
    }
    
    await healthRecord.remove();
    
    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

// Generate health report for a specific livestock
exports.generateHealthReport = async (req, res) => {
  try {
    const livestock = await Livestock.findById(req.params.livestockId);
    
    if (!livestock) {
      return res.status(404).json({
        success: false,
        error: 'Livestock not found'
      });
    }
    
    const healthRecords = await HealthRecord.find({ livestockId: req.params.livestockId })
      .populate('vetId', 'name email specialization')
      .sort({ date: -1 }); // Sort by date descending (newest first)
    
    // Generate a simple report
    const report = {
      livestock: livestock,
      healthRecords: healthRecords,
      summary: {
        totalRecords: healthRecords.length,
        lastCheckup: livestock.lastCheckup,
        currentStatus: livestock.healthStatus,
        vaccinations: healthRecords.filter(record => record.type === 'Vaccination').length,
        emergencies: healthRecords.filter(record => record.type === 'Emergency').length,
        regularCheckups: healthRecords.filter(record => record.type === 'Regular Checkup').length,
        generatedAt: new Date()
      }
    };
    
    res.status(200).json({
      success: true,
      data: report
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};
