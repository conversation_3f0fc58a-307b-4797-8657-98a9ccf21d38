const axios = require("axios");
const Livestock = require("../models/Livestock");
const multer = require("multer");
const path = require("path");
const fs = require("fs");

// Azure OpenAI Config
const AZURE_OPENAI_ENDPOINT = process.env.AZURE_OPENAI_ENDPOINT || "https://your-azure-openai-endpoint.openai.azure.com";
const AZURE_OPENAI_API_KEY = process.env.AZURE_OPENAI_API_KEY || "your-azure-openai-api-key";
const AZURE_OPENAI_DEPLOYMENT = process.env.AZURE_OPENAI_DEPLOYMENT || "gpt-4o";

// Configure multer for image upload
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, 'uploads/livestock/');
    },
    filename: function (req, file, cb) {
        cb(null, Date.now() + path.extname(file.originalname));
    }
});

const upload = multer({
    storage: storage,
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
    fileFilter: function (req, file, cb) {
        if (!file.originalname.match(/\.(jpg|jpeg|png|gif)$/)) {
            return cb(new Error('Only image files are allowed!'), false);
        }
        cb(null, true);
    }
});

// Function to encode image to base64
const encodeImageToBase64 = (imagePath) => {
    try {
        const imageData = fs.readFileSync(imagePath, { encoding: "base64" });
        const mimeType = {
            ".jpg": "image/jpeg",
            ".jpeg": "image/jpeg",
            ".png": "image/png",
        }[path.extname(imagePath).toLowerCase()] || "image/jpeg";

        return { base64: imageData, mimeType };
    } catch (err) {
        console.error(`Error reading image file: ${err.message}`);
        throw new Error("Image file not found or cannot be processed.");
    }
};

// Analyze livestock image with GPT-4o
const analyzeLivestock = async (req, res) => {
    try {
        // Handle file upload
        upload.single('image')(req, res, async function (err) {
            if (err) {
                return res.status(400).json({
                    success: false,
                    message: "Error uploading image",
                    error: err.message
                });
            }

            if (!req.file) {
                return res.status(400).json({
                    success: false,
                    message: "No image file provided"
                });
            }

            // Get livestock details from request
            const { livestockType, breed, age, weight, notes } = req.body;

            // Validate required fields
            if (!livestockType || !breed || !age || !weight) {
                return res.status(400).json({
                    success: false,
                    message: "Missing required livestock details"
                });
            }

            // Get image path
            const imagePath = req.file.path;
            const imageUrl = `/uploads/livestock/${req.file.filename}`;

            // Convert image to base64
            const { base64, mimeType } = encodeImageToBase64(imagePath);

            // Create prompt for GPT-4o
            const prompt = `
                You are an expert veterinarian specializing in livestock health analysis.
                Analyze this image of a ${livestockType} (breed: ${breed}, age: ${age}, weight: ${weight} kg).

                Provide a detailed health assessment including:
                1. Overall health status (healthy, concerning, or critical)
                2. Visible physical condition assessment
                3. Potential health issues or concerns based on visual cues
                4. Recommendations for care or treatment if applicable
                5. Nutritional advice based on the animal's appearance

                Format your response as a structured JSON object with the following fields:
                - overallHealth: "healthy", "concerning", or "critical"
                - physicalCondition: detailed assessment of physical appearance
                - potentialIssues: array of potential health issues identified
                - recommendations: array of care recommendations
                - nutritionalAdvice: specific feeding recommendations
                - summary: brief summary of the analysis

                Additional notes from farmer: ${notes || 'None provided'}
            `;

            console.log("Sending request to Azure OpenAI...");

            // Call Azure OpenAI GPT-4o
            const response = await axios.post(
                `${AZURE_OPENAI_ENDPOINT}/openai/deployments/${AZURE_OPENAI_DEPLOYMENT}/chat/completions?api-version=2024-02-15-preview`,
                {
                    messages: [
                        { role: "system", content: "You are a veterinary expert specializing in livestock health analysis." },
                        {
                            role: "user",
                            content: [
                                { type: "text", text: prompt },
                                { type: "image_url", image_url: { url: `data:${mimeType};base64,${base64}` } }
                            ]
                        }
                    ],
                    temperature: 0.3,
                    max_tokens: 1000,
                    response_format: { type: "json_object" }
                },
                {
                    headers: {
                        "Content-Type": "application/json",
                        "api-key": AZURE_OPENAI_API_KEY
                    }
                }
            );

            // Extract and parse the analysis
            const aiResponse = response.data.choices[0].message.content;
            const analysisResult = JSON.parse(aiResponse);

            // Determine health status from AI response
            const healthStatus = analysisResult.overallHealth === "healthy" ? "healthy" :
                                 analysisResult.overallHealth === "critical" ? "sick" : "unknown";

            // Store analysis in MongoDB
            const newEntry = new Livestock({
                imageUrl,
                analysis: analysisResult,
                year: new Date().getFullYear(),
                farmerId: req.user?.id,
                livestockType,
                breed,
                age,
                weight,
                healthStatus,
                notes: notes || ''
            });

            await newEntry.save();

            res.json({
                success: true,
                message: "Livestock image analyzed successfully",
                analysis: analysisResult,
                imageUrl,
                healthStatus
            });
        });
    } catch (error) {
        console.error("Error in analyzeLivestock:", error);
        res.status(500).json({
            success: false,
            message: "Error analyzing livestock image",
            error: error.message
        });
    }
};

// Get historical livestock data
const getHistoricalData = async (req, res) => {
    try {
        const { startYear, endYear, farmerId } = req.query;

        // Build query
        const query = {
            year: {
                $gte: parseInt(startYear) || new Date().getFullYear() - 5,
                $lte: parseInt(endYear) || new Date().getFullYear()
            }
        };

        // Add farmer filter if provided
        if (farmerId) {
            query.farmerId = farmerId;
        }

        const historicalData = await Livestock.find(query)
            .sort({ year: -1, createdAt: -1 })
            .limit(20);

        res.json({
            success: true,
            message: "Historical data retrieved successfully",
            historicalData
        });
    } catch (error) {
        console.error("Error in getHistoricalData:", error);
        res.status(500).json({
            success: false,
            message: "Error fetching historical data",
            error: error.message
        });
    }
};

module.exports = {
    analyzeLivestock,
    getHistoricalData,
    upload // Export upload middleware for use in routes
};

