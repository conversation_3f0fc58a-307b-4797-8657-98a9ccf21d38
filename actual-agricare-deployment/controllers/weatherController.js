const weatherService = require('../services/weatherService');

const getCurrentWeather = async (req, res) => {
  try {
    console.log('Request params:', req.params);
    console.log('Request query:', req.query);
    
    const farmerId = req.query.farmerId || req.params.farmerId;
    console.log('Using farmerId:', farmerId);
    
    if (!farmerId) {
      return res.status(400).json({
        success: false,
        message: 'Farmer ID is required'
      });
    }

    const weatherData = await weatherService.getCurrentWeather(farmerId);
    
    res.status(200).json({
      success: true,
      data: {
        current: weatherData
      }
    });
  } catch (error) {
    console.error('Error in getCurrentWeather:', error);
    handleWeatherError(res, error);
  }
};

const getWeatherForecast = async (req, res) => {
  try {
    console.log('Request params:', req.params);
    console.log('Request query:', req.query);
    
    const farmerId = req.query.farmerId || req.params.farmerId;
    console.log('Using farmerId:', farmerId);
    
    if (!farmerId) {
      return res.status(400).json({
        success: false,
        message: 'Farmer ID is required'
      });
    }

    const forecastData = await weatherService.getForecast(farmerId);
    
    res.status(200).json({
      success: true,
      data: forecastData
    });
  } catch (error) {
    console.error('Error in getWeatherForecast:', error);
    handleWeatherError(res, error);
  }
};

const getWeatherBulletin = async (req, res) => {
  try {
    console.log('Request params:', req.params);
    console.log('Request query:', req.query);
    
    const farmerId = req.query.farmerId || req.params.farmerId;
    console.log('Using farmerId:', farmerId);
    
    if (!farmerId) {
      return res.status(400).json({
        success: false,
        message: 'Farmer ID is required'
      });
    }

    const bulletin = await weatherService.getWeatherBulletin(farmerId);
    
    res.status(200).json({
      success: true,
      data: {
        bulletin
      }
    });
  } catch (error) {
    console.error('Error in getWeatherBulletin:', error);
    handleWeatherError(res, error);
  }
};

// Helper function to handle weather-related errors
const handleWeatherError = (res, error) => {
  if (error.message.includes('Farmer not found') || error.message.includes('Invalid farmer ID format')) {
    return res.status(404).json({
      success: false,
      message: error.message
    });
  }
  
  if (error.message.includes('No farm found') || error.message.includes('Invalid farm location')) {
    return res.status(404).json({
      success: false,
      message: error.message
    });
  }

  if (error.message.includes('OpenWeather API key is not configured')) {
    return res.status(500).json({
      success: false,
      message: 'Weather service configuration error'
    });
  }

  if (error.response && error.response.status === 429) {
    return res.status(429).json({
      success: false,
      message: 'Weather API rate limit exceeded. Please try again later.'
    });
  }
  
  res.status(500).json({
    success: false,
    message: error.message || 'Failed to fetch weather data'
  });
};

module.exports = {
  getCurrentWeather,
  getWeatherForecast,
  getWeatherBulletin
}; 