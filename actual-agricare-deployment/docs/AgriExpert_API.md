# AgriExpert Management API Documentation

## Overview
The AgriExpert Management API provides comprehensive CRUD operations for managing agricultural experts, institutions, and research centers. This API is designed for Territory Managers (TM) to efficiently manage the agricultural expert network.

## Base URL
```
http://localhost:8000/api/agri-expert
```

## Authentication
All endpoints require Bearer token authentication:
```
Authorization: Bearer <token>
```

For development/testing, use:
```
Authorization: Bearer test-token
```

## Access Control
- **Role Required**: Territory Manager (TM) or Admin
- **Permissions**: Full CRUD operations on all entities

## Endpoints

### 1. Statistics

#### GET /stats
Get dashboard statistics and distributions.

**Response:**
```json
{
  "success": true,
  "data": {
    "counts": {
      "experts": 3,
      "institutions": 2,
      "researchInstitutes": 1,
      "total": 6
    },
    "distributions": {
      "expertsByState": [
        {"_id": "Punjab", "count": 1},
        {"_id": "Haryana", "count": 1}
      ],
      "expertsBySpecialization": [
        {"_id": "Crop Management", "count": 1},
        {"_id": "Soil Management", "count": 1}
      ]
    }
  }
}
```

### 2. Experts Management

#### GET /experts
Get all experts with pagination and filtering.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10)
- `search` (string): Search in name, specialization, designation
- `state` (string): Filter by state
- `district` (string): Filter by district
- `specialization` (string): Filter by specialization
- `status` (string): Filter by status (Active, Inactive, Pending)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "_id": "68526275e247a4f7a1705da0",
      "name": "Dr. Rajesh Kumar",
      "designation": "Senior Agricultural Scientist",
      "specialization": "Crop Management",
      "email": "<EMAIL>",
      "phone": "+91-9876543210",
      "state": "Punjab",
      "district": "Ludhiana",
      "achievements": ["Best Researcher Award 2023"],
      "experience": 15,
      "qualifications": [
        {
          "degree": "Ph.D. in Agriculture",
          "institution": "Punjab Agricultural University",
          "year": 2008
        }
      ],
      "languages": ["English", "Hindi", "Punjabi"],
      "availability": "Available",
      "rating": 0,
      "totalConsultations": 0,
      "status": "Active",
      "createdBy": {
        "_id": "6852622482427990848ec094",
        "name": "Test TM User"
      },
      "createdAt": "2025-06-18T06:53:41.251Z",
      "updatedAt": "2025-06-18T06:53:41.251Z"
    }
  ],
  "pagination": {
    "current": 1,
    "pages": 1,
    "total": 3
  }
}
```

#### GET /experts/:id
Get single expert by ID.

#### POST /experts
Create new expert.

**Request Body:**
```json
{
  "name": "Dr. Test Expert",
  "designation": "Agricultural Scientist",
  "specialization": "Organic Farming",
  "email": "<EMAIL>",
  "phone": "+91-9876543213",
  "state": "Karnataka",
  "district": "Bangalore",
  "achievements": ["Test Achievement"],
  "experience": 5,
  "qualifications": [
    {
      "degree": "Ph.D. in Agriculture",
      "institution": "University Name",
      "year": 2020
    }
  ],
  "languages": ["English", "Hindi"]
}
```

#### PUT /experts/:id
Update expert by ID.

#### DELETE /experts/:id
Delete expert by ID.

### 3. Institutions Management

#### GET /institutions
Get all institutions with pagination and filtering.

**Query Parameters:**
- `page`, `limit`, `search` (same as experts)
- `state` (string): Filter by state
- `type` (string): Filter by type (University, College, Institute, Research Center, Training Center)
- `status` (string): Filter by status

#### POST /institutions
Create new institution.

**Request Body:**
```json
{
  "name": "Test Agricultural University",
  "type": "University",
  "location": "City, State",
  "state": "State Name",
  "district": "District Name",
  "established": "1962",
  "departments": ["Agronomy", "Plant Breeding"],
  "researchAreas": ["Crop Improvement"],
  "contact": {
    "phone": "+91-xxx-xxxxxxx",
    "email": "<EMAIL>",
    "website": "www.university.edu",
    "address": "Full Address"
  },
  "facilities": ["Research Labs", "Library"],
  "studentCapacity": 5000,
  "facultyCount": 300
}
```

#### PUT /institutions/:id
Update institution by ID.

#### DELETE /institutions/:id
Delete institution by ID.

### 4. Research Institutes Management

#### GET /research-institutes
Get all research institutes with pagination and filtering.

**Query Parameters:**
- `page`, `limit`, `search` (same as experts)
- `state` (string): Filter by state
- `focus` (string): Filter by focus areas
- `status` (string): Filter by status

#### POST /research-institutes
Create new research institute.

**Request Body:**
```json
{
  "name": "Test Research Institute",
  "location": "City, State",
  "state": "State Name",
  "district": "District Name",
  "established": "1946",
  "focus": ["Rice Research", "Crop Improvement"],
  "parentOrganization": "ICAR",
  "contact": {
    "phone": "+91-xxx-xxxxxxx",
    "email": "<EMAIL>",
    "website": "www.institute.nic.in",
    "address": "Full Address"
  }
}
```

#### PUT /research-institutes/:id
Update research institute by ID.

#### DELETE /research-institutes/:id
Delete research institute by ID.

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "message": "Validation error",
  "errors": ["Field is required"]
}
```

### 401 Unauthorized
```json
{
  "success": false,
  "message": "Not authorized"
}
```

### 403 Forbidden
```json
{
  "success": false,
  "message": "Access denied. Insufficient permissions"
}
```

### 404 Not Found
```json
{
  "success": false,
  "message": "Expert not found"
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Failed to fetch experts",
  "error": "Detailed error message"
}
```

## Database Schema

### AgriExpert Collection
- **name**: String (required)
- **designation**: String (required)
- **specialization**: String (required)
- **email**: String (required, unique)
- **phone**: String (required)
- **state**: String (required)
- **district**: String (required)
- **achievements**: Array of Strings
- **experience**: Number
- **qualifications**: Array of Objects
- **languages**: Array of Strings
- **availability**: Enum (Available, Busy, Unavailable)
- **rating**: Number (0-5)
- **totalConsultations**: Number
- **status**: Enum (Active, Inactive, Pending)
- **createdBy**: ObjectId (User reference)
- **updatedBy**: ObjectId (User reference)
- **timestamps**: createdAt, updatedAt

### AgriInstitution Collection
- **name**: String (required)
- **type**: Enum (University, College, Institute, Research Center, Training Center)
- **location**: String (required)
- **state**: String (required)
- **district**: String
- **established**: String
- **departments**: Array of Strings
- **researchAreas**: Array of Strings
- **contact**: Object (phone, email, website, address)
- **accreditation**: Array of Objects
- **facilities**: Array of Strings
- **studentCapacity**: Number
- **facultyCount**: Number
- **status**: Enum (Active, Inactive, Under Review)
- **createdBy**: ObjectId (User reference)
- **updatedBy**: ObjectId (User reference)
- **timestamps**: createdAt, updatedAt

### ResearchInstitute Collection
- **name**: String (required)
- **location**: String (required)
- **state**: String (required)
- **district**: String
- **established**: String
- **focus**: Array of Strings (required)
- **parentOrganization**: String
- **contact**: Object (phone, email, website, address)
- **status**: Enum (Active, Inactive, Under Review)
- **createdBy**: ObjectId (User reference)
- **updatedBy**: ObjectId (User reference)
- **timestamps**: createdAt, updatedAt

## Testing

### Sample cURL Commands

**Get Statistics:**
```bash
curl -X GET "http://localhost:8000/api/agri-expert/stats" \
  -H "Authorization: Bearer test-token"
```

**Create Expert:**
```bash
curl -X POST "http://localhost:8000/api/agri-expert/experts" \
  -H "Authorization: Bearer test-token" \
  -H "Content-Type: application/json" \
  -d '{"name":"Dr. Test","designation":"Scientist","specialization":"Farming","email":"<EMAIL>","phone":"+91-1234567890","state":"Karnataka","district":"Bangalore"}'
```

**Delete Expert:**
```bash
curl -X DELETE "http://localhost:8000/api/agri-expert/experts/EXPERT_ID" \
  -H "Authorization: Bearer test-token"
```
