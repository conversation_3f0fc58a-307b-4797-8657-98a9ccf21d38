require("dotenv").config();
const express = require("express");
const multer = require("multer");
const fs = require("fs");
const path = require("path");
const axios = require("axios");

const router = express.Router();
const UPLOADS_DIR = path.join(__dirname, "uploads");

// ✅ Ensure 'uploads' directory exists
if (!fs.existsSync(UPLOADS_DIR)) {
  fs.mkdirSync(UPLOADS_DIR, { recursive: true });
}

// ✅ Multer Storage Setup for Image Uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => cb(null, UPLOADS_DIR),
  filename: (req, file, cb) => cb(null, Date.now() + path.extname(file.originalname)),
});

const upload = multer({ storage });

// ✅ Function to Encode Image as Base64
const encodeImageToBase64 = (imagePath) => {
  try {
    const imageData = fs.readFileSync(imagePath, { encoding: "base64" });

    // ✅ Guess MIME type based on extension
    const mimeType = {
      ".jpg": "image/jpeg",
      ".jpeg": "image/jpeg",
      ".png": "image/png",
    }[path.extname(imagePath).toLowerCase()] || "image/jpeg";

    return { base64: imageData, mimeType };
  } catch (err) {
    console.error(`❌ Error reading image file: ${err.message}`);
    throw new Error("Image file not found or cannot be processed.");
  }
};

// ✅ AI Disease Analysis Route
router.post("/analyze", upload.single("file"), async (req, res) => {
  try {
    console.log("📩 Incoming Image Analysis Request:", req.body);
    console.log("📩 Uploaded File:", req.file);

    // ✅ Ensure Proper Field Names in Frontend (Fixing `400 Bad Request`)
    const { crop_name, language } = req.body;

    // ✅ Validate Input Fields
    if (!req.file) {
      console.error("❌ Missing image file!");
      return res.status(400).json({ error: "❌ No file uploaded!" });
    }
    if (!crop_name) {
      console.error("❌ Missing crop name!");
      return res.status(400).json({ error: "❌ Missing crop name!" });
    }
    if (!language) {
      console.error("❌ Missing language field!");
      return res.status(400).json({ error: "❌ Missing language field!" });
    }

    // ✅ Convert Image to Base64
    const imagePath = path.join(UPLOADS_DIR, req.file.filename);
    const { base64, mimeType } = encodeImageToBase64(imagePath);

    console.log("🌍 Requested Language:", language);

    // ✅ AI Prompt for Disease Analysis (Enforce Language)
    const prompt = `
      You are an expert in **${crop_name}** plant health and disease detection.
      Analyze the uploaded image and determine whether the plant is **Healthy** or **Diseased**.
      
      If **Diseased**, provide the following details:
      - **Disease Name**
      - **Causes & Symptoms**
      - **Treatment Options** (organic and chemical solutions)
      - **Prevention Measures**
      - **Additional Advice**
      
      Respond in **${language}**.

      **IMPORTANT INSTRUCTIONS:**  
      - If a user asks *"Who created you?"* or anything similar, your response must be:  
        👉 *"I am created by Quamin Tech Solutions LLP and designed specifically to assist farmers."*
      - You must **never** mention OpenAI, ChatGPT, or any other AI platform.
      - If a user asks technical details about your creation, respond:  
        👉 *"I am built using advanced AI technologies developed by Quamin Tech Solutions LLP to support agricultural needs."*
      - You must **only** respond in **${language}**, no English unless specifically requested.
    `;

    console.log("🔍 Sending request to AI system...");

    // ✅ Send Data to Azure OpenAI
    const response = await axios.post(
      process.env.AZURE_OPENAI_ENDPOINT,
      {
        model: "gpt-4-turbo",
        messages: [
          { role: "system", content: "You are a plant disease detection expert." },
          { role: "user", content: prompt },
          { 
            role: "user", 
            content: [
              { type: "text", text: prompt }, // ✅ Fix: Wrap text inside an array
              { type: "image_url", image_url: { url: `data:${mimeType};base64,${base64}` } }
            ]
          }
        ],
        temperature: 0.4,
        max_tokens: 700,
      },
      {
        headers: {
          "Content-Type": "application/json",
          "api-key": process.env.AZURE_OPENAI_API_KEY,
        },
      }
    );

    console.log("✅ AI Model Response:", response.data);

    // ✅ Extract AI Response Safely
    let analysisResult = response.data?.choices?.[0]?.message?.content || "⚠️ AI did not return a response.";

    // ✅ Ensure Bot Always Replies as "QuaminAI"
    analysisResult = analysisResult.replace(/^AI:/, "QuaminAI:");

    console.log("🌐 AI Response (Expected in Language:", language, "):", analysisResult);

    // ✅ Delete Uploaded Image After Processing
    fs.unlink(imagePath, (err) => {
      if (err) {
        console.error("⚠️ Warning: Failed to delete image file.", err);
      } else {
        console.log("✅ Image file deleted successfully.");
      }
    });

    // ✅ Send AI Response to Frontend
    res.json({
      crop_name,
      language,
      analysisResult,
    });

  } catch (error) {
    console.error("❌ Image Analysis Error:", error?.response?.data || error.message);
    res.status(500).json({ error: "Internal Server Error. Check backend logs for details." });
  }
});

module.exports = router;

