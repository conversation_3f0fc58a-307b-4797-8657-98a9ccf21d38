const mongoose = require('mongoose');

// Agricultural Expert Schema
const agriExpertSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Expert name is required'],
    trim: true
  },
  designation: {
    type: String,
    required: [true, 'Designation is required'],
    trim: true
  },
  specialization: {
    type: String,
    required: [true, 'Specialization is required'],
    trim: true
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    match: [/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  phone: {
    type: String,
    required: [true, 'Phone number is required'],
    trim: true
  },
  state: {
    type: String,
    required: [true, 'State is required'],
    trim: true
  },
  district: {
    type: String,
    required: [true, 'District is required'],
    trim: true
  },
  achievements: [{
    type: String,
    trim: true
  }],
  experience: {
    type: Number,
    min: 0
  },
  qualifications: [{
    degree: String,
    institution: String,
    year: Number
  }],
  languages: [{
    type: String,
    trim: true
  }],
  availability: {
    type: String,
    enum: ['Available', 'Busy', 'Unavailable'],
    default: 'Available'
  },
  rating: {
    type: Number,
    min: 0,
    max: 5,
    default: 0
  },
  totalConsultations: {
    type: Number,
    default: 0
  },
  status: {
    type: String,
    enum: ['Active', 'Inactive', 'Pending'],
    default: 'Active'
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Agricultural Institution Schema
const agriInstitutionSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Institution name is required'],
    trim: true
  },
  type: {
    type: String,
    required: [true, 'Institution type is required'],
    enum: ['University', 'College', 'Institute', 'Research Center', 'Training Center'],
    trim: true
  },
  location: {
    type: String,
    required: [true, 'Location is required'],
    trim: true
  },
  state: {
    type: String,
    required: [true, 'State is required'],
    trim: true
  },
  district: {
    type: String,
    trim: true
  },
  established: {
    type: String,
    trim: true
  },
  departments: [{
    type: String,
    trim: true
  }],
  researchAreas: [{
    type: String,
    trim: true
  }],
  contact: {
    phone: {
      type: String,
      trim: true
    },
    email: {
      type: String,
      lowercase: true,
      match: [/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
    },
    website: {
      type: String,
      trim: true
    },
    address: {
      type: String,
      trim: true
    }
  },
  accreditation: [{
    body: String,
    grade: String,
    year: Number
  }],
  facilities: [{
    type: String,
    trim: true
  }],
  studentCapacity: {
    type: Number,
    min: 0
  },
  facultyCount: {
    type: Number,
    min: 0
  },
  status: {
    type: String,
    enum: ['Active', 'Inactive', 'Under Review'],
    default: 'Active'
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Research Institute Schema
const researchInstituteSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Institute name is required'],
    trim: true
  },
  location: {
    type: String,
    required: [true, 'Location is required'],
    trim: true
  },
  state: {
    type: String,
    required: [true, 'State is required'],
    trim: true
  },
  district: {
    type: String,
    trim: true
  },
  established: {
    type: String,
    trim: true
  },
  focus: [{
    type: String,
    required: [true, 'At least one focus area is required'],
    trim: true
  }],
  parentOrganization: {
    type: String,
    trim: true
  },
  contact: {
    phone: {
      type: String,
      trim: true
    },
    email: {
      type: String,
      lowercase: true,
      match: [/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
    },
    website: {
      type: String,
      trim: true
    },
    address: {
      type: String,
      trim: true
    }
  },
  researchProjects: [{
    title: String,
    description: String,
    startDate: Date,
    endDate: Date,
    status: {
      type: String,
      enum: ['Ongoing', 'Completed', 'Planned'],
      default: 'Planned'
    }
  }],
  publications: [{
    title: String,
    authors: [String],
    journal: String,
    year: Number,
    doi: String
  }],
  collaborations: [{
    organization: String,
    type: String,
    startDate: Date
  }],
  budget: {
    annual: Number,
    currency: {
      type: String,
      default: 'INR'
    }
  },
  staffCount: {
    researchers: Number,
    technicians: Number,
    administrative: Number
  },
  status: {
    type: String,
    enum: ['Active', 'Inactive', 'Under Review'],
    default: 'Active'
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes for better query performance
agriExpertSchema.index({ state: 1, district: 1 });
agriExpertSchema.index({ specialization: 1 });
agriExpertSchema.index({ status: 1 });
agriExpertSchema.index({ name: 'text', specialization: 'text' });

agriInstitutionSchema.index({ state: 1, type: 1 });
agriInstitutionSchema.index({ status: 1 });
agriInstitutionSchema.index({ name: 'text', departments: 'text' });

researchInstituteSchema.index({ state: 1, focus: 1 });
researchInstituteSchema.index({ status: 1 });
researchInstituteSchema.index({ name: 'text', focus: 'text' });

// Export models
const AgriExpert = mongoose.model('AgriExpert', agriExpertSchema);
const AgriInstitution = mongoose.model('AgriInstitution', agriInstitutionSchema);
const ResearchInstitute = mongoose.model('ResearchInstitute', researchInstituteSchema);

module.exports = {
  AgriExpert,
  AgriInstitution,
  ResearchInstitute
};
