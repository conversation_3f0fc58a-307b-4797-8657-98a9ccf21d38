const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
  text: {
    type: String,
    required: true
  },
  sender: {
    type: String,
    enum: ['user', 'bot'],
    required: true
  },
  timestamp: {
    type: Date,
    default: Date.now
  },
  language: {
    type: String,
    default: 'en'
  },
  attachments: [{
    type: {
      type: String,
      enum: ['image', 'document', 'audio'],
      required: true
    },
    url: {
      type: String,
      required: true
    },
    name: String,
    analysisResult: Object
  }],
  followupQuestions: [String],
  contextId: String
});

const chatHistorySchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  userName: {
    type: String,
    required: true
  },
  userRole: {
    type: String,
    required: true
  },
  messages: [messageSchema],
  contextData: {
    type: Object,
    default: {}
  },
  // Add a date field specifically for grouping by day
  chatDate: {
    type: Date,
    default: function() {
      const date = new Date();
      date.setHours(0, 0, 0, 0);
      return date;
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  expiresAt: {
    type: Date,
    default: function() {
      // Set expiration to 1 year from creation
      const date = new Date();
      date.setFullYear(date.getFullYear() + 1);
      return date;
    }
  }
});

// Create indexes for efficient querying
chatHistorySchema.index({ userId: 1, chatDate: -1 });
chatHistorySchema.index({ userId: 1, updatedAt: -1 });
chatHistorySchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 }); // TTL index for automatic deletion

// Update the updatedAt field on save
chatHistorySchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

const ChatHistory = mongoose.model('ChatHistory', chatHistorySchema);

module.exports = ChatHistory;
