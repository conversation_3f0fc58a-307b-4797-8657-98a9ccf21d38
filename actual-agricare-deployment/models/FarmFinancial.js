const mongoose = require('mongoose');

const farmFinancialSchema = new mongoose.Schema({
  farmerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  creditScore: {
    score: {
      type: Number,
      required: true
    },
    scoreCategory: {
      type: String,
      enum: ['Poor', 'Fair', 'Good', 'Very Good', 'Excellent'],
      required: true
    },
    lastUpdated: {
      type: Date,
      default: Date.now
    }
  },
  loans: [{
    type: {
      type: String,
      required: true
    },
    amount: {
      type: Number,
      required: true
    },
    remainingAmount: {
      type: Number,
      required: true
    },
    interestRate: {
      type: Number,
      required: true
    },
    startDate: {
      type: Date,
      required: true
    },
    endDate: {
      type: Date,
      required: true
    },
    status: {
      type: String,
      enum: ['Active', 'Completed', 'Defaulted', 'Pending'],
      default: 'Active'
    },
    bankName: {
      type: String,
      required: true
    },
    emiAmount: {
      type: Number,
      required: true
    },
    emiStatus: {
      type: String,
      enum: ['On Time', 'Delayed', 'Missed', 'Revolving Credit'],
      default: 'On Time'
    },
    purpose: String,
    collateral: String,
    guarantor: String,
    loanOfficer: String,
    branchCode: String
  }],
  assets: {
    land: [{
      area: {
        type: Number,
        required: true
      },
      unit: {
        type: String,
        default: 'Acres'
      },
      location: {
        type: String,
        required: true
      },
      value: {
        type: Number,
        required: true
      },
      cropType: String,
      ownership: {
        type: String,
        enum: ['Owned', 'Leased', 'Shared'],
        default: 'Owned'
      },
      documentNumber: String,
      purchaseYear: Number,
      leaseExpiryDate: Date,
      irrigationSource: String,
      soilType: String
    }],
    equipment: [{
      name: {
        type: String,
        required: true
      },
      model: String,
      purchaseYear: Number,
      value: {
        type: Number,
        required: true
      },
      registrationNumber: String,
      insuranceExpiryDate: Date,
      maintenanceStatus: {
        type: String,
        enum: ['Excellent', 'Good', 'Fair', 'Poor'],
        default: 'Good'
      },
      fuelType: String
    }],
    livestock: [{
      type: {
        type: String,
        required: true
      },
      breed: String,
      count: {
        type: Number,
        required: true
      },
      value: {
        type: Number,
        required: true
      },
      purchaseYear: Number,
      insuranceStatus: {
        type: String,
        enum: ['Insured', 'Not Insured'],
        default: 'Not Insured'
      },
      healthStatus: {
        type: String,
        enum: ['Healthy', 'Fair', 'Poor'],
        default: 'Healthy'
      }
    }],
    structures: [{
      type: {
        type: String,
        required: true
      },
      capacity: String,
      value: {
        type: Number,
        required: true
      },
      constructionYear: Number,
      condition: {
        type: String,
        enum: ['Excellent', 'Good', 'Fair', 'Poor'],
        default: 'Good'
      }
    }]
  },
  yieldHistory: [{
    year: {
      type: Number,
      required: true
    },
    crop: {
      type: String,
      required: true
    },
    area: {
      type: Number,
      required: true
    },
    production: {
      type: Number,
      required: true
    },
    unit: {
      type: String,
      default: 'kg'
    },
    revenue: {
      type: Number,
      required: true
    },
    expenses: {
      type: Number,
      required: true
    },
    profit: {
      type: Number,
      required: true
    },
    yieldPerAcre: Number,
    marketPrice: Number,
    season: {
      type: String,
      enum: ['Kharif', 'Rabi', 'Zaid'],
      default: 'Kharif'
    },
    recordDate: {
      type: Date,
      default: Date.now
    }
  }],
  paymentHistory: [{
    date: {
      type: Date,
      required: true
    },
    amount: {
      type: Number,
      required: true
    },
    loanId: {
      type: String,
      required: true
    },
    status: {
      type: String,
      enum: ['Paid', 'Delayed', 'Missed', 'Upcoming'],
      required: true
    },
    dueDate: {
      type: Date,
      required: true
    },
    paidDate: Date,
    transactionId: String,
    paymentMethod: String,
    delayReason: String
  }],
  creditHistory: {
    cibilScore: Number,
    lastCibilUpdate: Date,
    creditUtilization: Number,
    accountAgeYears: Number,
    inquiriesLast6Months: Number,
    delinquencies: Number,
    creditMix: String,
    paymentHistoryRating: String,
    remarks: String
  },
  insuranceDetails: [{
    type: {
      type: String,
      required: true
    },
    provider: {
      type: String,
      required: true
    },
    coverageAmount: {
      type: Number,
      required: true
    },
    premium: {
      type: Number,
      required: true
    },
    startDate: {
      type: Date,
      required: true
    },
    endDate: {
      type: Date,
      required: true
    },
    crops: [String],
    equipment: [String],
    livestock: [String],
    policyNumber: String,
    status: {
      type: String,
      enum: ['Active', 'Expired', 'Cancelled', 'Pending'],
      default: 'Active'
    }
  }],
  subsidies: [{
    scheme: {
      type: String,
      required: true
    },
    amount: {
      type: Number,
      required: true
    },
    frequency: String,
    lastReceived: Date,
    nextExpected: Date,
    status: {
      type: String,
      enum: ['Active', 'Expired', 'Pending'],
      default: 'Active'
    },
    registrationNumber: String
  }],
  bankAccounts: [{
    bankName: {
      type: String,
      required: true
    },
    accountType: {
      type: String,
      required: true
    },
    accountNumber: {
      type: String,
      required: true
    },
    branch: String,
    ifscCode: String,
    balance: Number,
    lastTransaction: Date
  }],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
farmFinancialSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('FarmFinancial', farmFinancialSchema);
