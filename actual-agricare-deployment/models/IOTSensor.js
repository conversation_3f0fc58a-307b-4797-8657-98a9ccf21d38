const mongoose = require('mongoose');

const iotSensorSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  type: {
    type: String,
    required: true,
    enum: ['soil', 'weather', 'irrigation']
  },
  location: {
    type: String,
    required: true
  },
  ipAddress: {
    type: String,
    required: true,
    validate: {
      validator: function(v) {
        return /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(v);
      },
      message: props => `${props.value} is not a valid IP address!`
    }
  },
  port: {
    type: String,
    required: true
  },
  status: {
    type: String,
    required: true,
    enum: ['active', 'inactive', 'maintenance'],
    default: 'active'
  },
  batteryLevel: {
    type: Number,
    required: true,
    min: 0,
    max: 100,
    default: 100
  },
  lastUpdate: {
    type: Date,
    default: Date.now
  },
  parameters: {
    soilMoisture: { type: Boolean, default: true },
    temperature: { type: Boolean, default: true },
    humidity: { type: Boolean, default: true },
    ph: { type: Boolean, default: true },
    nitrogen: { type: Boolean, default: true },
    phosphorus: { type: Boolean, default: true },
    potassium: { type: Boolean, default: true }
  },
  readings: [{
    timestamp: { type: Date, default: Date.now },
    values: {
      soilMoisture: Number,
      temperature: Number,
      humidity: Number,
      ph: Number,
      nitrogen: Number,
      phosphorus: Number,
      potassium: Number
    }
  }],
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

// Index for faster queries
iotSensorSchema.index({ location: 1 });
iotSensorSchema.index({ type: 1 });
iotSensorSchema.index({ status: 1 });

// Method to update sensor readings
iotSensorSchema.methods.updateReadings = async function(readings) {
  this.readings.push({
    timestamp: new Date(),
    values: readings
  });
  
  // Keep only last 1000 readings
  if (this.readings.length > 1000) {
    this.readings = this.readings.slice(-1000);
  }
  
  this.lastUpdate = new Date();
  return this.save();
};

// Method to update battery level
iotSensorSchema.methods.updateBatteryLevel = async function(level) {
  this.batteryLevel = Math.max(0, Math.min(100, level));
  return this.save();
};

// Method to update status
iotSensorSchema.methods.updateStatus = async function(status) {
  if (['active', 'inactive', 'maintenance'].includes(status)) {
    this.status = status;
    return this.save();
  }
  throw new Error('Invalid status');
};

const IOTSensor = mongoose.model('IOTSensor', iotSensorSchema);

module.exports = IOTSensor; 