const mongoose = require('mongoose');

const weatherDataSchema = new mongoose.Schema({
  farmerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Farmer',
    required: true
  },
  type: {
    type: String,
    enum: ['current', 'forecast'],
    required: true
  },
  data: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  timestamp: {
    type: Date,
    required: true,
    // Convert to IST before saving
    set: function(date) {
      if (!(date instanceof Date)) {
        date = new Date(date);
      }
      return new Date(date.toLocaleString('en-US', { timeZone: 'Asia/Kolkata' }));
    }
  },
  createdAt: {
    type: Date,
    default: function() {
      return new Date(new Date().toLocaleString('en-US', { timeZone: 'Asia/Kolkata' }));
    }
  }
});

// Index for efficient querying
weatherDataSchema.index({ farmerId: 1, type: 1, timestamp: -1 });

// Pre-save middleware to ensure timestamps are in IST
weatherDataSchema.pre('save', function(next) {
  if (this.timestamp) {
    this.timestamp = new Date(this.timestamp.toLocaleString('en-US', { timeZone: 'Asia/Kolkata' }));
  }
  if (this.createdAt) {
    this.createdAt = new Date(this.createdAt.toLocaleString('en-US', { timeZone: 'Asia/Kolkata' }));
  }
  next();
});

const WeatherData = mongoose.model('WeatherData', weatherDataSchema);

module.exports = WeatherData; 