const express = require('express');
const router = express.Router();
const { Configuration, OpenAIApi } = require('openai');
const AIConversation = require('../models/AIConversation');
const auth = require('../middleware/auth');
const weatherService = require('../services/weatherService');

// Azure OpenAI configuration
const endpoint = process.env.AZURE_OPENAI_ENDPOINT;
const apiKey = process.env.AZURE_OPENAI_API_KEY;
const deploymentId = process.env.AZURE_DEPLOYMENT_NAME;

if (!endpoint || !apiKey || !deploymentId) {
  console.error('❌ Missing Azure OpenAI configuration');
  process.exit(1);
}

// Initialize OpenAI client with Azure configuration
let openai;
try {
  const configuration = new Configuration({
    apiKey: apiKey,
    basePath: `${endpoint}/openai/deployments/${deploymentId}`,
    baseOptions: {
      headers: {
        'api-key': api<PERSON>ey,
      },
      params: {
        'api-version': process.env.AZURE_API_VERSION || '2024-08-01-preview'
      }
    }
  });

  openai = new OpenAIApi(configuration);
  console.log('✅ Azure OpenAI client initialized successfully');
} catch (error) {
  console.error('❌ Failed to initialize Azure OpenAI client:', error);
  process.exit(1);
}

// Helper function to format farm data for the AI
const formatFarmData = async (farmData, userId) => {
  if (!farmData) return '';

  try {
    // Extract weather data from the request or fetch from service
    let weatherData;

    if (farmData.weather) {
      // Use the weather data from the request
      const current = farmData.weather.current || farmData.weather;
      const forecast = farmData.weather.forecast || [];

      weatherData = {
        current: {
          temperature: current.temperature,
          humidity: current.humidity,
          conditions: current.conditions || 'Unknown',
          windSpeed: current.windSpeed
        },
        forecast: Array.isArray(forecast) ? forecast.map(day => ({
          date: day.date,
          maxTemp: day.temperature?.max || day.maxTemp || day.temperature,
          minTemp: day.temperature?.min || day.minTemp || day.temperature,
          condition: day.conditions || day.condition || 'Unknown',
          chanceOfRain: day.precipitation > 0 ? `${day.precipitation}mm` : 'Low'
        })) : []
      };
    } else {
      // Fetch weather data from service if not provided
      weatherData = await weatherService.getFarmerWeather(userId);
    }

    // Format weather forecast
    const weatherForecast = weatherData.forecast && weatherData.forecast.length > 0 ?
      weatherData.forecast
        .map(day => `${day.date || 'Unknown'}: ${day.maxTemp || 'Unknown'}/${day.minTemp || 'Unknown'}, ${day.condition || 'Unknown'}, Rain chance: ${day.chanceOfRain || 'Unknown'}`)
        .join('\n') : 'No forecast data available';

    // Format weather bulletin if available
    const weatherBulletin = farmData.weather?.bulletin ?
      `\nWeather Bulletin:\n${farmData.weather.bulletin}` : '';

    return `
Current Farm Status:
- Weather: ${weatherData.current?.temperature || 'Unknown'}°C, Humidity: ${weatherData.current?.humidity || 'Unknown'}%
- Conditions: ${weatherData.current?.conditions || 'Unknown'}
- Soil Health: Moisture ${farmData.soilHealth?.moisture || farmData.soil?.moisture || 'Unknown'}%, pH ${farmData.soilHealth?.ph || farmData.soil?.ph || 'Unknown'}
- NPK Levels: N(${farmData.soilHealth?.nitrogen || farmData.soil?.nitrogen || 'Unknown'}%), P(${farmData.soilHealth?.phosphorus || farmData.soil?.phosphorus || 'Unknown'}%), K(${farmData.soilHealth?.potassium || farmData.soil?.potassium || 'Unknown'}%)
- Recent Issues: ${farmData.alerts?.map(alert => alert.message || alert.title).join(', ') || 'None'}

Weather Forecast for Next Days:
${weatherForecast}
${weatherBulletin}
    `;
  } catch (error) {
    console.error('Error formatting farm data:', error);
    // Fallback with minimal formatting
    return `
Current Farm Status:
- Weather: ${farmData.weather?.temperature || farmData.weather?.current?.temperature || 'Unknown'}°C
- Soil Health: Available in dashboard
- Recent Issues: ${farmData.alerts?.length ? 'Check alerts in dashboard' : 'None reported'}
    `;
  }
};

// System message to guide the AI's behavior
const systemMessage = {
  role: 'system',
  content: `You are AgriCare AI, an expert agricultural assistant. You provide advice on:
1. Weather insights and their impact on farming
2. Soil health analysis and recommendations
3. Pest detection and management strategies
4. Crop recommendations based on conditions
5. Best farming practices

Always provide practical, actionable advice based on the farm's current data.
Keep responses clear and concise, using farmer-friendly language.
When discussing measurements, use metric units and provide ranges when appropriate.
If you're unsure about something, acknowledge it and suggest consulting a local agricultural expert.

You can communicate in multiple Indian languages. When responding, use the same language as the user's query.
If the user writes in English, respond in English. If they write in Hindi, respond in Hindi, and so on.
Always maintain the same language throughout the conversation.`
};

// Error handling middleware
router.use((err, req, res, next) => {
  console.error('AI Route Error:', err);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: err.message
  });
});

// Get conversation history
router.get('/history/:userId', async (req, res) => {
  try {
    const conversations = await AIConversation.find({ userId: req.params.userId })
      .sort({ createdAt: -1 })
      .limit(50);

    res.json({
      success: true,
      conversations: conversations.map(conv => ({
        id: conv._id,
        messages: conv.messages,
        createdAt: conv.createdAt
      }))
    });
  } catch (error) {
    console.error('Error fetching conversation history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch conversation history',
      error: error.message
    });
  }
});

// Chat endpoint
router.post('/chat', auth, async (req, res) => {
  try {
    const { message, farmData, language } = req.body;

    if (!message) {
      return res.status(400).json({
        success: false,
        message: 'Message is required'
      });
    }

    // Get conversation history
    let conversation;
    try {
      conversation = await AIConversation.findOne({ userId: req.user.uid })
        .sort({ updatedAt: -1 });
    } catch (error) {
      console.error('Error fetching conversation:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch conversation history'
      });
    }

    // Format farm data for context
    const farmContext = await formatFarmData(farmData, req.user.uid);

    // Prepare messages array with farm context
    const messages = [
      {
        role: 'system',
        content: `${systemMessage.content}\n\n${farmContext}`
      },
      ...(conversation?.messages || []).slice(-5), // Keep last 5 messages for context
      { role: 'user', content: message }
    ];

    // Get AI response
    const completion = await openai.createChatCompletion({
      model: deploymentId,
      messages,
      temperature: 0.7,
      max_tokens: 500
    });

    const aiResponse = completion.data.choices[0].message.content;

    // Save conversation
    try {
      if (conversation) {
        conversation.messages.push(
          { role: 'user', content: message },
          { role: 'assistant', content: aiResponse }
        );
        conversation.updatedAt = new Date();
        await conversation.save();
      } else {
        await AIConversation.create({
          userId: req.user.uid,
          userRole: req.user.role || 'Farmer',
          messages: [
            { role: 'user', content: message },
            { role: 'assistant', content: aiResponse }
          ]
        });
      }
    } catch (error) {
      console.error('Error saving conversation:', error);
      // Don't fail the request if saving fails
    }

    res.json({
      success: true,
      response: aiResponse
    });
  } catch (error) {
    console.error('Chat error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process request',
      error: error.message
    });
  }
});

// Get recommendations
router.get('/recommendations/:userId', async (req, res) => {
  try {
    const { category, status } = req.query;
    const query = { userId: req.params.userId };

    if (category) {
      query['recommendations.category'] = category;
    }
    if (status) {
      query['recommendations.status'] = status;
    }

    const conversations = await AIConversation.find(query)
      .select('recommendations')
      .sort({ createdAt: -1 });

    const recommendations = conversations.flatMap(c => c.recommendations);

    res.json({
      success: true,
      recommendations
    });
  } catch (error) {
    console.error('Error fetching recommendations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch recommendations'
    });
  }
});

// Update speech-config endpoint to work without auth in development
router.get('/speech-config', auth, async (req, res) => {
  try {
    console.log('Getting speech config for user:', req.user.uid);
    console.log('Speech key available:', !!process.env.AZURE_SPEECH_KEY);
    console.log('Speech region available:', !!process.env.AZURE_SPEECH_REGION);
    console.log('Speech endpoint available:', !!process.env.REACT_APP_AZURE_SPEECH_ENDPOINT);

    if (!process.env.AZURE_SPEECH_KEY || !process.env.AZURE_SPEECH_REGION) {
      console.error('Missing Azure Speech credentials');
      return res.status(500).json({
        success: false,
        error: 'Speech service not configured'
      });
    }

    res.json({
      success: true,
      subscriptionKey: process.env.AZURE_SPEECH_KEY,
      region: process.env.AZURE_SPEECH_REGION,
      endpoint: process.env.REACT_APP_AZURE_SPEECH_ENDPOINT
    });
  } catch (error) {
    console.error('Error getting speech config:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get speech configuration'
    });
  }
});

// Text-to-speech endpoint
router.post('/text-to-speech', auth, async (req, res) => {
  try {
    const { text, language, voiceName, azureKey, azureRegion, azureEndpoint } = req.body;

    if (!text) {
      return res.status(400).json({
        success: false,
        error: 'Text is required'
      });
    }

    // Use the Azure REST API directly instead of the SDK
    const axios = require('axios');
    const speechKey = azureKey || process.env.AZURE_SPEECH_KEY || 'd133bd0fbec34843afefea5fcbbb1242';
    const speechRegion = azureRegion || process.env.AZURE_SPEECH_REGION || 'centralindia';
    const selectedVoice = voiceName || getVoiceNameForLanguage(language);

    // Prepare SSML
    const ssml = `
      <speak version='1.0' xml:lang='${language}' xmlns='http://www.w3.org/2001/10/synthesis'>
        <voice name='${selectedVoice}'>
          ${text}
        </voice>
      </speak>
    `;

    // Make request to Azure TTS REST API
    const response = await axios({
      method: 'post',
      url: `https://${speechRegion}.tts.speech.microsoft.com/cognitiveservices/v1`,
      headers: {
        'Ocp-Apim-Subscription-Key': speechKey,
        'Content-Type': 'application/ssml+xml',
        'X-Microsoft-OutputFormat': 'audio-16khz-32kbitrate-mono-mp3',
        'User-Agent': 'AgriCare-Backend'
      },
      data: ssml,
      responseType: 'arraybuffer'
    });

    // Set response headers
    res.setHeader('Content-Type', 'audio/mpeg');
    res.setHeader('Content-Length', response.data.length);

    // Send audio data
    res.send(response.data);
  } catch (error) {
    console.error('Error in text-to-speech endpoint:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Internal server error'
    });
  }
});

// Helper function to get voice name based on language
function getVoiceNameForLanguage(language) {
  const voiceMap = {
    'en-IN': 'en-US-JennyNeural',
    'hi-IN': 'hi-IN-SwaraNeural',
    'bn-IN': 'bn-IN-SumonaNeural',
    'gu-IN': 'gu-IN-NehaNeural',
    'kn-IN': 'kn-IN-ChitraNeural',
    'ml-IN': 'ml-IN-SobhaNeural',
    'mr-IN': 'mr-IN-AarohiNeural',
    'pa-IN': 'pa-IN-ManeetNeural',
    'ta-IN': 'ta-IN-PallaviNeural',
    'te-IN': 'te-IN-ShrutiNeural',
    'ur-IN': 'ur-IN-ZaraNeural',
    'or-IN': 'en-US-JennyNeural', // Fallback for Odia
    'as-IN': 'en-US-JennyNeural', // Fallback for Assamese
    'bho-IN': 'hi-IN-SwaraNeural', // Fallback to Hindi for Bhojpuri
    'ma-IN': 'en-US-JennyNeural', // Fallback for Maithili
    'sa-IN': 'en-US-JennyNeural', // Fallback for Sanskrit
    'ks-IN': 'en-US-JennyNeural', // Fallback for Kashmiri
    'mni-IN': 'en-US-JennyNeural', // Fallback for Manipuri
    'ne-IN': 'en-US-JennyNeural',
    'sd-IN': 'en-US-JennyNeural'
  };

  // Convert language code format if needed (e.g., 'en' to 'en-IN')
  const fullLangCode = language.includes('-') ? language : `${language}-IN`;
  return voiceMap[fullLangCode] || 'en-US-JennyNeural';
}

module.exports = router;