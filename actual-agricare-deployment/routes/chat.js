const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const { OpenAIClient, AzureKeyCredential } = require('@azure/openai');

// Initialize Azure OpenAI client
const client = new OpenAIClient(
  process.env.AZURE_OPENAI_ENDPOINT,
  new AzureKeyCredential(process.env.AZURE_OPENAI_API_KEY)
);

router.post('/', auth, async (req, res) => {
  try {
    const { message, farmData, language } = req.body;
    
    if (!message) {
      return res.status(400).json({
        success: false,
        message: 'Message is required'
      });
    }

    // Log the request details
    console.log('Chat request:', {
      message,
      hasFarmData: !!farmData,
      language,
      endpoint: process.env.AZURE_OPENAI_ENDPOINT,
      deployment: process.env.AZURE_DEPLOYMENT_NAME
    });

    // Create a system message that includes the context
    const systemMessage = `You are an AI agricultural assistant. 
    ${farmData ? `Current context:\nWeather: ${JSON.stringify(farmData.weather)}\nSoil Health: ${JSON.stringify(farmData.soilHealth)}\nMarket Data: ${JSON.stringify(farmData.marketData)}\nAlerts: ${JSON.stringify(farmData.alerts)}\nSchedule: ${JSON.stringify(farmData.schedule)}\n\n` : ''}
    Please provide helpful advice about farming and agriculture based on the current conditions and the user's question. 
    If the user asks about their farm's status, consider the weather conditions in your response.`;

    // Create the chat completion using Azure OpenAI
    const completion = await client.getChatCompletions(
      process.env.AZURE_DEPLOYMENT_NAME,
      [
        { role: "system", content: systemMessage },
        { role: "user", content: message }
      ],
      {
        temperature: 0.7,
        max_tokens: 500
      }
    );

    const aiResponse = completion.choices[0].message.content;

    res.json({
      success: true,
      response: aiResponse
    });
  } catch (error) {
    // Log detailed error information
    console.error('Chat error details:', {
      message: error.message,
      stack: error.stack,
      code: error.code,
      name: error.name,
      response: error.response?.data
    });
    
    res.status(500).json({
      success: false,
      message: 'An error occurred while processing your request',
      error: error.message
    });
  }
});

module.exports = router; 