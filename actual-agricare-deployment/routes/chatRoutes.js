const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/auth.middleware');
const chatController = require('../controllers/chatController');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Ensure upload directories exist
const imageUploadDir = 'uploads/chat-images/';
const audioUploadDir = 'uploads/chat-audio/';
const tempDir = 'uploads/temp/';

[imageUploadDir, audioUploadDir, tempDir].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Configure multer for handling image uploads
const imageStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, imageUploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'chat-image-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const imageUpload = multer({
  storage: imageStorage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: function (req, file, cb) {
    // Accept only images
    if (!file.originalname.match(/\.(jpg|jpeg|png|gif)$/)) {
      return cb(new Error('Only image files are allowed!'), false);
    }
    cb(null, true);
  }
});

// Configure multer for handling audio uploads
const audioStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, audioUploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'chat-audio-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const audioUpload = multer({
  storage: audioStorage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: function (req, file, cb) {
    // Accept only audio files
    if (!file.originalname.match(/\.(mp3|wav|m4a|ogg)$/)) {
      return cb(new Error('Only audio files are allowed!'), false);
    }
    cb(null, true);
  }
});

// TEMPORARY: Add a test route to check if the API is working
router.get('/test', (req, res) => {
  res.status(200).json({ success: true, message: 'Chat API is working!' });
});

// Get chat history for the authenticated user
router.get('/history', chatController.getChatHistory); // Removed protect middleware temporarily

// Search chat history
router.get('/search', chatController.searchChatHistory); // Removed protect middleware temporarily

// Get image analysis history
router.get('/analysis-history', chatController.getImageAnalysisHistory); // Removed protect middleware temporarily

// Save a new message
router.post('/message', chatController.saveMessage); // Removed protect middleware temporarily

// Process a message with Azure OpenAI
router.post('/process', chatController.processMessage); // Removed protect middleware temporarily

// Convert speech to text
router.post('/speech-to-text', audioUpload.single('audio'), chatController.speechToText); // Removed protect middleware temporarily

// Process image analysis
router.post('/analyze-image', imageUpload.single('image'), async (req, res) => { // Removed protect middleware temporarily
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Image is required'
      });
    }

    // Add the image URL to the request body
    console.log('File path:', req.file.path);
    req.body.imageUrl = `${req.protocol}://${req.get('host')}/${req.file.path.replace(/\\/g, '/')}`;

    // Call the controller
    await chatController.processImageAnalysis(req, res);
  } catch (error) {
    console.error('Error processing image:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process image',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
