const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');

// Mock satellite data service since the real one is missing
const mockSatelliteDataService = {
  async getNasaPowerData(coordinates) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Generate realistic mock data based on coordinates
    const lat = coordinates.lat;
    const lng = coordinates.lng;
    
    // Use coordinates to generate deterministic but varying data
    const latSeed = Math.sin(lat * 0.0174533) * 100;
    const lngSeed = Math.cos(lng * 0.0174533) * 100;
    const seedValue = (latSeed + lngSeed) / 2;
    
    return {
      soilMoisture: Math.max(10, Math.min(50, 25 + seedValue * 0.1)),
      temperature: 25 + (lat * -0.2) + (seedValue * 0.05),
      precipitation: Math.max(0, 5 + seedValue * 0.1),
      humidity: Math.max(30, Math.min(90, 60 + seedValue * 0.2)),
      coordinates: coordinates
    };
  }
};

// Process soil data from NASA POWER API simulation
function processSoilData(nasaData) {
  try {
    // Calculate derived NPK values based on soil moisture and temperature
    const nitrogen = 250 + (nasaData.soilMoisture * 0.8) + (nasaData.temperature * 0.5);
    const phosphorus = 40 + (nasaData.soilMoisture * 0.2) - (nasaData.temperature * 0.1);
    const potassium = 180 + (nasaData.soilMoisture * 0.5) + (nasaData.temperature * 0.2);
    
    // Calculate pH (approximation)
    const ph = 6.5 + ((nasaData.soilMoisture - 30) * 0.01) + ((nasaData.temperature - 25) * 0.005);
    
    // Calculate organic matter (approximation)
    const organicMatter = 3.0 + (nasaData.soilMoisture * 0.02);
    
    // Generate historical data for the past 7 days
    const history = [];
    const today = new Date();
    
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
      
      // Add some variation for historical data
      const variation = (Math.sin(i * 0.5) * 5);
      
      history.push({
        date: dateStr,
        moisture: parseFloat((nasaData.soilMoisture + variation).toFixed(1)),
        ph: parseFloat((ph + (variation * 0.01)).toFixed(1)),
        nitrogen: parseFloat((nitrogen + variation * 2).toFixed(1)),
        phosphorus: parseFloat((phosphorus + variation * 0.5).toFixed(1)),
        potassium: parseFloat((potassium + variation * 1.5).toFixed(1))
      });
    }

    return {
      moisture: parseFloat(nasaData.soilMoisture.toFixed(1)),
      ph: parseFloat(ph.toFixed(1)),
      nitrogen: parseFloat(nitrogen.toFixed(1)),
      phosphorus: parseFloat(phosphorus.toFixed(1)),
      potassium: parseFloat(potassium.toFixed(1)),
      organicMatter: parseFloat(organicMatter.toFixed(1)),
      temperature: parseFloat(nasaData.temperature.toFixed(1)),
      airTemperature: parseFloat((nasaData.temperature + 3).toFixed(1)),
      humidity: parseFloat(nasaData.humidity.toFixed(1)),
      lastUpdated: new Date().toISOString(),
      history: history,
      dataSource: 'Simulated NASA POWER API'
    };
  } catch (error) {
    console.error('Error processing soil data:', error);
    throw error;
  }
}

// Default recommendations
function getDefaultRecommendations() {
  return [
    'Monitor soil moisture levels regularly and maintain optimal range of 40-60%',
    'Apply balanced NPK fertilizer based on current soil nutrient levels',
    'Consider adding organic matter to improve soil structure and water retention',
    'Test soil pH periodically and adjust with lime or sulfur as needed',
    'Implement crop rotation to maintain soil health and prevent nutrient depletion'
  ];
}

// Get soil health data
router.get('/soil', auth, async (req, res) => {
  try {
    console.log('Fetching soil health data...');
    
    // Default coordinates (can be customized based on user location)
    const coordinates = {
      lat: 23.2599, // Bhopal, India
      lng: 77.4126
    };

    // Try to get user's coordinates from database if User model is available
    try {
      const User = require('../models/User');
      const user = await User.findById(req.user.id);
      if (user && user.location && user.location.coordinates) {
        coordinates.lng = user.location.coordinates[0];
        coordinates.lat = user.location.coordinates[1];
        console.log('Using user coordinates:', coordinates);
      }
    } catch (userError) {
      console.log('User model not available or user not found, using default coordinates');
    }

    // Generate simulated NASA POWER API data
    const nasaData = await mockSatelliteDataService.getNasaPowerData(coordinates);
    
    // Process the data
    const soilHealthData = processSoilData(nasaData);
    
    // Add recommendations
    soilHealthData.recommendations = getDefaultRecommendations();
    soilHealthData.recommendationsSource = 'System Generated';

    console.log('Successfully generated soil health data');
    
    res.json({
      success: true,
      data: soilHealthData
    });

  } catch (error) {
    console.error('Error fetching soil health data:', error);
    
    // Return fallback mock data if everything fails
    const fallbackData = {
      moisture: 35.2,
      ph: 6.8,
      nitrogen: 280.5,
      phosphorus: 45.3,
      potassium: 190.7,
      organicMatter: 2.8,
      temperature: 24.5,
      airTemperature: 28.2,
      humidity: 65.4,
      lastUpdated: new Date().toISOString(),
      history: [
        { date: '20241127', ph: 6.7, moisture: 32, nitrogen: 275, phosphorus: 43, potassium: 185 },
        { date: '20241128', ph: 6.8, moisture: 34, nitrogen: 278, phosphorus: 44, potassium: 187 },
        { date: '20241129', ph: 6.9, moisture: 36, nitrogen: 280, phosphorus: 45, potassium: 190 },
        { date: '20241130', ph: 6.8, moisture: 35, nitrogen: 282, phosphorus: 46, potassium: 192 },
        { date: '20241201', ph: 6.7, moisture: 33, nitrogen: 279, phosphorus: 45, potassium: 189 },
        { date: '20241202', ph: 6.8, moisture: 34, nitrogen: 281, phosphorus: 46, potassium: 191 },
        { date: '20241203', ph: 6.9, moisture: 35, nitrogen: 283, phosphorus: 47, potassium: 193 }
      ],
      dataSource: 'Fallback Mock Data',
      recommendations: getDefaultRecommendations(),
      recommendationsSource: 'Default',
      error: error.message
    };

    res.json({
      success: true,
      data: fallbackData
    });
  }
});

// Get weather data
router.get('/weather', auth, async (req, res) => {
  try {
    console.log('Fetching weather data...');
    
    // Generate realistic weather data
    const weatherData = {
      temperature: Math.round(25 + (Math.random() * 10) - 5), // 20-30°C
      humidity: Math.round(60 + (Math.random() * 20) - 10), // 50-70%
      rainfall: parseFloat((Math.random() * 5).toFixed(1)), // 0-5mm
      windSpeed: parseFloat((5 + Math.random() * 10).toFixed(1)), // 5-15 km/h
      pressure: Math.round(1010 + (Math.random() * 20) - 10), // 1000-1020 hPa
      forecast: ['Clear sky', 'Partly cloudy', 'Cloudy', 'Light rain', 'Sunny'][Math.floor(Math.random() * 5)],
      uvIndex: Math.round(3 + Math.random() * 7), // 3-10
      visibility: Math.round(8 + Math.random() * 4), // 8-12 km
      lastUpdated: new Date().toISOString()
    };

    res.json({
      success: true,
      data: weatherData
    });
  } catch (error) {
    console.error('Error fetching weather data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch weather data',
      error: error.message
    });
  }
});

// Get market data
router.get('/market', auth, async (req, res) => {
  try {
    console.log('Fetching market data...');
    
    const marketData = {
      crops: [
        {
          name: 'Wheat',
          currentPrice: 2200 + Math.round((Math.random() * 200) - 100),
          priceChange: parseFloat(((Math.random() * 6) - 3).toFixed(1)),
          trend: Math.random() > 0.5 ? 'up' : 'down',
          demand: ['High', 'Medium', 'Low'][Math.floor(Math.random() * 3)],
          unit: '₹/quintal'
        },
        {
          name: 'Rice',
          currentPrice: 3100 + Math.round((Math.random() * 300) - 150),
          priceChange: parseFloat(((Math.random() * 4) - 2).toFixed(1)),
          trend: Math.random() > 0.5 ? 'up' : 'down',
          demand: ['High', 'Medium', 'Low'][Math.floor(Math.random() * 3)],
          unit: '₹/quintal'
        },
        {
          name: 'Maize',
          currentPrice: 1800 + Math.round((Math.random() * 200) - 100),
          priceChange: parseFloat(((Math.random() * 3) - 1.5).toFixed(1)),
          trend: Math.random() > 0.5 ? 'up' : 'down',
          demand: ['High', 'Medium', 'Low'][Math.floor(Math.random() * 3)],
          unit: '₹/quintal'
        },
        {
          name: 'Soybean',
          currentPrice: 4200 + Math.round((Math.random() * 400) - 200),
          priceChange: parseFloat(((Math.random() * 5) - 2.5).toFixed(1)),
          trend: Math.random() > 0.5 ? 'up' : 'down',
          demand: ['High', 'Medium', 'Low'][Math.floor(Math.random() * 3)],
          unit: '₹/quintal'
        }
      ],
      marketTrends: [],
      lastUpdated: new Date().toISOString()
    };

    // Generate market trends from crops data
    marketData.marketTrends = marketData.crops.map(crop => ({
      label: `${crop.name} Price`,
      value: `${crop.unit.split('/')[0]}${crop.currentPrice}/${crop.unit.split('/')[1]}`,
      direction: crop.trend,
      change: `${crop.priceChange > 0 ? '+' : ''}${crop.priceChange}%`
    }));

    res.json({
      success: true,
      data: marketData
    });
  } catch (error) {
    console.error('Error fetching market data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch market data',
      error: error.message
    });
  }
});

// Get schedule data
router.get('/schedule', auth, async (req, res) => {
  try {
    console.log('Fetching schedule data...');
    
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const scheduleData = {
      tasks: [
        {
          id: 1,
          title: 'Morning Irrigation',
          type: 'irrigation',
          status: 'pending',
          date: today.toISOString(),
          time: '06:00',
          description: 'Water the wheat field - Zone A',
          priority: 'high',
          estimatedDuration: '2 hours'
        },
        {
          id: 2,
          title: 'Fertilizer Application',
          type: 'fertilization',
          status: 'scheduled',
          date: tomorrow.toISOString(),
          time: '08:00',
          description: 'Apply NPK fertilizer to rice field',
          priority: 'medium',
          estimatedDuration: '3 hours'
        },
        {
          id: 3,
          title: 'Pest Inspection',
          type: 'pest_control',
          status: 'completed',
          date: today.toISOString(),
          time: '14:00',
          description: 'Check for pest activity in maize field',
          priority: 'medium',
          estimatedDuration: '1 hour'
        },
        {
          id: 4,
          title: 'Soil Testing',
          type: 'monitoring',
          status: 'pending',
          date: tomorrow.toISOString(),
          time: '10:00',
          description: 'Collect soil samples for pH and nutrient analysis',
          priority: 'low',
          estimatedDuration: '1.5 hours'
        }
      ],
      upcomingTasks: 2,
      completedToday: 1,
      lastUpdated: new Date().toISOString()
    };

    res.json({
      success: true,
      data: scheduleData
    });
  } catch (error) {
    console.error('Error fetching schedule data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch schedule data',
      error: error.message
    });
  }
});

// Get alerts data
router.get('/alerts', auth, async (req, res) => {
  try {
    console.log('Fetching alerts data...');
    
    const alertsData = {
      alerts: [
        {
          id: 1,
          type: 'warning',
          title: 'Low Soil Moisture Detected',
          message: 'Soil moisture in Field A (Wheat) has dropped below 25%. Immediate irrigation recommended.',
          timestamp: new Date().toISOString(),
          severity: 'high',
          actionRequired: true,
          field: 'Field A'
        },
        {
          id: 2,
          type: 'info',
          title: 'Weather Update',
          message: 'Rain expected in the next 24-48 hours. Consider postponing irrigation.',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
          severity: 'medium',
          actionRequired: false,
          field: 'All Fields'
        },
        {
          id: 3,
          type: 'success',
          title: 'Fertilizer Application Complete',
          message: 'NPK fertilizer successfully applied to Field B (Rice). Next application due in 3 weeks.',
          timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
          severity: 'low',
          actionRequired: false,
          field: 'Field B'
        },
        {
          id: 4,
          type: 'warning',
          title: 'pH Level Alert',
          message: 'Soil pH in Field C has increased to 8.2. Consider applying sulfur to lower pH.',
          timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(), // 12 hours ago
          severity: 'medium',
          actionRequired: true,
          field: 'Field C'
        }
      ],
      unreadCount: 2,
      criticalAlerts: 1,
      lastUpdated: new Date().toISOString()
    };

    res.json({
      success: true,
      data: alertsData
    });
  } catch (error) {
    console.error('Error fetching alerts data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch alerts data',
      error: error.message
    });
  }
});

module.exports = router;

