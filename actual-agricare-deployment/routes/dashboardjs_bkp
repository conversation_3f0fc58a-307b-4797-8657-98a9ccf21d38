const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const axios = require('axios');
const User = require('../models/User');
const { Configuration, OpenAIApi } = require('openai');

// Azure OpenAI configuration for GPT-4o
const endpoint = process.env.AZURE_OPENAI_ENDPOINT;
const apiKey = process.env.AZURE_OPENAI_API_KEY;
const deploymentId = process.env.AZURE_DEPLOYMENT_NAME;

// Initialize OpenAI client with Azure configuration
let openai;
try {
  const configuration = new Configuration({
    apiKey: apiKey,
    basePath: `${endpoint}/openai/deployments/${deploymentId}`,
    baseOptions: {
      headers: {
        'api-key': apiKey,
      },
      params: {
        'api-version': process.env.AZURE_API_VERSION || '2024-08-01-preview'
      }
    }
  });

  openai = new OpenAIApi(configuration);
  console.log('Azure OpenAI client initialized with endpoint:', endpoint);
} catch (error) {
  console.error('Failed to initialize Azure OpenAI client:', error);
}

// Helper function to generate simulated NASA POWER API data
// This function generates realistic data based on coordinates without calling the actual API
async function getNasaPowerData(coordinates) {
  try {
    console.log('Generating simulated NASA POWER data for coordinates:', coordinates);

    // Ensure coordinates are valid
    if (!coordinates || typeof coordinates.lat !== 'number' || typeof coordinates.lng !== 'number' ||
        isNaN(coordinates.lat) || isNaN(coordinates.lng)) {
      throw new Error('Invalid coordinates for simulated NASA POWER data');
    }

    // Ensure coordinates are within valid ranges
    const validLat = Math.max(-90, Math.min(90, coordinates.lat));
    const validLng = Math.max(-180, Math.min(180, coordinates.lng));

    // Generate dates for the last 30 days including today
    const endDate = new Date();
    // Use current date (today) as the end date

    const dates = [];
    for (let i = 30; i >= 0; i--) {
      const date = new Date(endDate);
      date.setDate(date.getDate() - i);

      // Format date as YYYYMMDD - hardcode year to 2024 to match current year
      const year = 2024; // Hardcoded to 2024
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const dateStr = `${year}${month}${day}`;

      dates.push(dateStr);
    }

    // Log the date range for debugging
    const today = new Date();
    const todayFormatted = `2024-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
    console.log(`Date range: ${todayFormatted} to ${dates[0].substring(0, 4)}-${dates[0].substring(4, 6)}-${dates[0].substring(6, 8)}`);
    console.log(`First date: ${dates[0]}, Last date: ${dates[dates.length-1]}`);

    // Generate deterministic but realistic values based on coordinates
    // This ensures that the same coordinates always get the same data
    // but different coordinates get different data
    const latSeed = Math.sin(validLat * 0.0174533) * 100; // Convert to radians and scale
    const lngSeed = Math.cos(validLng * 0.0174533) * 100; // Convert to radians and scale
    const seedValue = (latSeed + lngSeed) / 2;

    // Generate parameter data
    const T2M = {}; // Air temperature at 2 meters
    const PRECTOTCORR = {}; // Precipitation
    const RH2M = {}; // Relative humidity at 2 meters
    const SOIL_M_0_10CM = {}; // Soil moisture at 0-10cm depth
    const SOIL_T_0_10CM = {}; // Soil temperature at 0-10cm depth

    // Add units
    T2M.units = 'C';
    PRECTOTCORR.units = 'mm/day';
    RH2M.units = '%';
    SOIL_M_0_10CM.units = 'kg/m^2';
    SOIL_T_0_10CM.units = 'C';

    // Generate data for each date
    dates.forEach((date, index) => {
      // Use the date and coordinates to generate deterministic but varying values
      const dayFactor = Math.sin(index * 0.2) * 3; // Varies by day

      // Temperature varies by latitude (colder at higher latitudes)
      // and has a slight daily variation
      T2M[date] = 25 - (Math.abs(validLat) * 0.2) + dayFactor + (seedValue * 0.05);

      // Precipitation varies by longitude and has random spikes
      PRECTOTCORR[date] = Math.max(0, 2 + Math.sin(validLng * 0.1) * 5 + Math.sin(index * 0.5) * 10 + (seedValue * 0.02));

      // Humidity varies inversely with temperature
      RH2M[date] = 60 + (Math.sin(validLat * 0.1) * 10) - dayFactor + (seedValue * 0.1);

      // Soil moisture depends on precipitation and has memory of previous days
      const prevMoisture = index > 0 ? SOIL_M_0_10CM[dates[index-1]] || 30 : 30;
      SOIL_M_0_10CM[date] = Math.max(10, Math.min(50, prevMoisture +
                                                 (PRECTOTCORR[date] * 0.5) -
                                                 (T2M[date] * 0.2) +
                                                 (seedValue * 0.05)));

      // Soil temperature follows air temperature but with lag and dampening
      SOIL_T_0_10CM[date] = T2M[date] - 2 + (Math.sin(index * 0.1) * 1) + (seedValue * 0.02);
    });

    // Construct the response object to match NASA POWER API format
    const simulatedData = {
      header: {
        title: "Simulated NASA POWER API Data",
        api_version: "v2.6.11",
        data_source: "Simulated POWER Data"
      },
      parameters: {
        T2M: "Temperature at 2 Meters",
        PRECTOTCORR: "Precipitation (Corrected)",
        RH2M: "Relative Humidity at 2 Meters",
        SOIL_M_0_10CM: "Soil Moisture at 0-10cm depth",
        SOIL_T_0_10CM: "Soil Temperature at 0-10cm depth"
      },
      times: {
        data_range: `${dates[0]}-${dates[dates.length-1]}`
      },
      geometry: {
        type: "Point",
        coordinates: [validLng, validLat]
      },
      properties: {
        parameter: {
          T2M,
          PRECTOTCORR,
          RH2M,
          SOIL_M_0_10CM,
          SOIL_T_0_10CM
        }
      }
    };

    console.log('Successfully generated simulated NASA POWER data');
    return simulatedData;
  } catch (error) {
    console.error('Error generating simulated NASA POWER data:', error);
    throw error;
  }
}

// Generate soil health recommendations using GPT-4o
async function generateSoilHealthRecommendations(soilData) {
  try {
    if (!openai) {
      console.error('OpenAI client not initialized');
      return getDefaultRecommendations();
    }

    const prompt = `
You are an agricultural soil expert. Based on the following soil health data, provide 4-5 specific, actionable recommendations for improving soil health and crop yield.
Focus on practical advice that farmers can implement.

Soil Data:
- Moisture: ${soilData.moisture}%
- pH: ${soilData.ph}
- Nitrogen: ${soilData.nitrogen} mg/kg
- Phosphorus: ${soilData.phosphorus} mg/kg
- Potassium: ${soilData.potassium} mg/kg
- Organic Matter: ${soilData.organicMatter}%
- Soil Temperature: ${soilData.temperature}°C

Format your response as a JSON array of recommendation strings only, with no additional text or explanation.
Example format: ["Recommendation 1", "Recommendation 2", "Recommendation 3", "Recommendation 4"]
`;

    const completion = await openai.createChatCompletion({
      model: deploymentId,
      messages: [
        { role: 'system', content: 'You are an agricultural soil expert providing concise, actionable recommendations.' },
        { role: 'user', content: prompt }
      ],
      temperature: 0.7,
      max_tokens: 500
    });

    const responseText = completion.data.choices[0].message.content.trim();

    try {
      // Try to parse the response as JSON
      const recommendations = JSON.parse(responseText);
      if (Array.isArray(recommendations) && recommendations.length > 0) {
        return recommendations;
      } else {
        console.warn('Invalid recommendations format from GPT-4o:', responseText);
        return getDefaultRecommendations();
      }
    } catch (parseError) {
      console.error('Error parsing GPT-4o recommendations:', parseError);
      // If the response isn't valid JSON, try to extract recommendations from the text
      const lines = responseText.split('\n').filter(line => line.trim().length > 0);
      if (lines.length > 0) {
        return lines.map(line => line.replace(/^[0-9-.*]+\s*/, '').trim()).filter(line => line.length > 0);
      }
      return getDefaultRecommendations();
    }
  } catch (error) {
    console.error('Error generating soil health recommendations:', error);
    return getDefaultRecommendations();
  }
}

// Default recommendations if GPT-4o fails
function getDefaultRecommendations() {
  return [
    'Soil pH is slightly acidic. Consider adding lime to raise pH.',
    'Organic matter content is low. Add compost or organic fertilizers.',
    'Nitrogen levels are below optimal. Apply nitrogen-rich fertilizer.',
    'Maintain consistent moisture levels between 40-60%.'
  ];
}

// Process NASA POWER API data to get soil health metrics
function processSoilData(nasaData) {
  try {
    const parameters = nasaData.properties.parameter;
    const dates = Object.keys(parameters.T2M).filter(key => key !== 'units');

    // Get the most recent date's data
    const latestDate = dates[dates.length - 1];

    // Extract soil moisture data
    const soilMoisture0_10cm = parameters.SOIL_M_0_10CM[latestDate];

    // Use single layer soil moisture
    const avgSoilMoisture = soilMoisture0_10cm;

    // Get soil temperature
    const soilTemperature = parameters.SOIL_T_0_10CM[latestDate];

    // Get air temperature and humidity
    const airTemperature = parameters.T2M[latestDate];
    const humidity = parameters.RH2M[latestDate];

    // Calculate derived NPK values based on soil moisture and temperature
    // These are approximations as NASA POWER API doesn't directly provide NPK values
    // In a real system, these would come from soil sensors or lab tests
    const nitrogen = 250 + (avgSoilMoisture * 0.8) + (soilTemperature * 0.5);
    const phosphorus = 40 + (avgSoilMoisture * 0.2) - (soilTemperature * 0.1);
    const potassium = 180 + (avgSoilMoisture * 0.5) + (soilTemperature * 0.2);

    // Calculate pH (approximation)
    const ph = 6.5 + ((avgSoilMoisture - 30) * 0.01) + ((soilTemperature - 25) * 0.005);

    // Calculate organic matter (approximation)
    const organicMatter = 3.0 + (avgSoilMoisture * 0.02);

    // Generate historical data for the past 7 days
    const history = [];

    // Log the total dates available
    console.log(`Total dates available: ${dates.length}`);
    console.log(`Latest date: ${dates[dates.length - 1]}`);

    // Get the last 7 days of data
    for (let i = Math.max(0, dates.length - 7); i < dates.length; i++) {
      const date = dates[i];
      const dailySoilMoisture = parameters.SOIL_M_0_10CM[date];
      const dailySoilTemp = parameters.SOIL_T_0_10CM[date];

      // Calculate daily NPK values
      const dailyNitrogen = 250 + (dailySoilMoisture * 0.8) + (dailySoilTemp * 0.5);
      const dailyPhosphorus = 40 + (dailySoilMoisture * 0.2) - (dailySoilTemp * 0.1);
      const dailyPotassium = 180 + (dailySoilMoisture * 0.5) + (dailySoilTemp * 0.2);
      const dailyPh = 6.5 + ((dailySoilMoisture - 30) * 0.01) + ((dailySoilTemp - 25) * 0.005);

      // Format the date as YYYYMMDD
      history.push({
        date: date,
        moisture: parseFloat(dailySoilMoisture.toFixed(1)),
        ph: parseFloat(dailyPh.toFixed(1)),
        nitrogen: parseFloat(dailyNitrogen.toFixed(1)),
        phosphorus: parseFloat(dailyPhosphorus.toFixed(1)),
        potassium: parseFloat(dailyPotassium.toFixed(1))
      });
    }

    // Log the history dates for debugging
    if (history.length > 0) {
      console.log(`History date range: ${history[0].date} to ${history[history.length - 1].date}`);
    }

    return {
      moisture: parseFloat(avgSoilMoisture.toFixed(1)),
      ph: parseFloat(ph.toFixed(1)),
      nitrogen: parseFloat(nitrogen.toFixed(1)),
      phosphorus: parseFloat(phosphorus.toFixed(1)),
      potassium: parseFloat(potassium.toFixed(1)),
      organicMatter: parseFloat(organicMatter.toFixed(1)),
      temperature: parseFloat(soilTemperature.toFixed(1)),
      airTemperature: parseFloat(airTemperature.toFixed(1)),
      humidity: parseFloat(humidity.toFixed(1)),
      lastUpdated: new Date().toISOString(),
      history: history,
      dataSource: 'NASA POWER API'
    };
  } catch (error) {
    console.error('Error processing NASA POWER data:', error);
    throw error;
  }
}

// Get weather data
router.get('/weather', auth, async (req, res) => {
  try {
    // TODO: Implement real weather data fetching
    res.json({
      success: true,
      data: {
        temperature: 28,
        humidity: 65,
        rainfall: 2.5,
        forecast: 'Clear sky',
        lastUpdated: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error fetching weather data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch weather data'
    });
  }
});

// Get soil health data
router.get('/soil', auth, async (req, res) => {
  try {
    // Default coordinates for user with mobile ********** (Manish Kumar)
    // In a real app, these would come from the user's profile or farm location
    const coordinates = {
      lat: 12.9716,
      lng: 77.5946
    };

    // Try to get user's coordinates from database
    try {
      const user = await User.findOne({ mobile: '**********' });
      if (user && user.geoLocation && user.geoLocation.coordinates) {
        coordinates.lng = user.geoLocation.coordinates[0];
        coordinates.lat = user.geoLocation.coordinates[1];
        console.log('Using user coordinates:', coordinates);
      }
    } catch (userError) {
      console.error('Error fetching user coordinates:', userError);
      // Continue with default coordinates
    }

    // Generate simulated NASA POWER API data
    const nasaData = await getNasaPowerData(coordinates);

    // Process the data
    const soilHealthData = processSoilData(nasaData);

    // Add a note that this is simulated data based on real NASA POWER API format
    soilHealthData.dataSource = 'Simulated NASA POWER API Data (based on real coordinates)';

    // Generate AI-powered recommendations based on soil health data
    try {
      const recommendations = await generateSoilHealthRecommendations(soilHealthData);
      soilHealthData.recommendations = recommendations;
      soilHealthData.recommendationsSource = 'GPT-4o';
    } catch (recError) {
      console.error('Error generating recommendations:', recError);
      soilHealthData.recommendations = getDefaultRecommendations();
      soilHealthData.recommendationsSource = 'Default';
    }

    res.json({
      success: true,
      data: soilHealthData
    });
  } catch (error) {
    console.error('Error fetching soil health data:', error);

    // Return mock data if NASA API fails
    const mockData = {
      moisture: 35.2,
      ph: 6.8,
      nitrogen: 280.5,
      phosphorus: 45.3,
      potassium: 190.7,
      organicMatter: 2.8,
      temperature: 24.5,
      airTemperature: 28.2,
      humidity: 65.4,
      lastUpdated: new Date().toISOString(),
      history: [
        { date: '20240329', ph: 6.7, moisture: 32, nitrogen: 275, phosphorus: 43, potassium: 185 },
        { date: '20240330', ph: 6.8, moisture: 34, nitrogen: 278, phosphorus: 44, potassium: 187 },
        { date: '20240331', ph: 6.9, moisture: 36, nitrogen: 280, phosphorus: 45, potassium: 190 },
        { date: '20240401', ph: 6.8, moisture: 35, nitrogen: 282, phosphorus: 46, potassium: 192 },
        { date: '20240402', ph: 6.7, moisture: 33, nitrogen: 279, phosphorus: 45, potassium: 189 },
        { date: '20240403', ph: 6.8, moisture: 34, nitrogen: 281, phosphorus: 46, potassium: 191 },
        { date: '20240404', ph: 6.9, moisture: 35, nitrogen: 283, phosphorus: 47, potassium: 193 }
      ],
      dataSource: 'Mock Data (NASA API Failed)',
      recommendations: getDefaultRecommendations(),
      recommendationsSource: 'Default',
      error: error.message
    };

    res.json({
      success: true,
      data: mockData
    });
  }
});

// Get market data
router.get('/market', auth, async (req, res) => {
  try {
    // TODO: Implement real market data fetching
    res.json({
      success: true,
      data: {
        crops: [
          {
            name: 'Wheat',
            currentPrice: 2200,
            priceChange: 2.5,
            trend: 'up',
            demand: 'High'
          },
          {
            name: 'Rice',
            currentPrice: 3100,
            priceChange: -1.2,
            trend: 'down',
            demand: 'Medium'
          },
          {
            name: 'Corn',
            currentPrice: 1800,
            priceChange: 0,
            trend: 'stable',
            demand: 'Low'
          }
        ],
        marketTrends: [
          {
            label: 'Wheat Price',
            value: '₹2,200/q',
            direction: 'up'
          },
          {
            label: 'Rice Price',
            value: '₹3,100/q',
            direction: 'down'
          },
          {
            label: 'Corn Price',
            value: '₹1,800/q',
            direction: 'stable'
          }
        ],
        lastUpdated: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error fetching market data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch market data'
    });
  }
});

// Get schedule data
router.get('/schedule', auth, async (req, res) => {
  try {
    // TODO: Implement real schedule data fetching
    res.json({
      success: true,
      data: {
        tasks: [
          {
            id: 1,
            title: 'Irrigation',
            type: 'irrigation',
            status: 'pending',
            date: new Date().toISOString(),
            description: 'Water the wheat field'
          },
          {
            id: 2,
            title: 'Pest Control',
            type: 'pest_control',
            status: 'completed',
            date: new Date().toISOString(),
            description: 'Apply pesticide to rice field'
          }
        ],
        lastUpdated: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error fetching schedule data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch schedule data'
    });
  }
});

// Get alerts data
router.get('/alerts', auth, async (req, res) => {
  try {
    // TODO: Implement real alerts data fetching
    res.json({
      success: true,
      data: {
        alerts: [
          {
            id: 1,
            type: 'warning',
            title: 'Low Soil Moisture',
            message: 'Soil moisture is below optimal levels in Field A',
            timestamp: new Date().toISOString()
          },
          {
            id: 2,
            type: 'info',
            title: 'Weather Update',
            message: 'Rain expected in the next 24 hours',
            timestamp: new Date().toISOString()
          }
        ],
        lastUpdated: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error fetching alerts data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch alerts data'
    });
  }
});

module.exports = router;