const express = require('express');
const router = express.Router();
const axios = require('axios');
const quaminSearchService = require('../services/quaminSearchService');

// Cache for storing search results
const searchCache = {
  data: {},
  timestamp: {},
  expirationTime: 30 * 60 * 1000 // 30 minutes
};

// External data routes
router.get('/search', async (req, res) => {
  try {
    const { query, category, state, region, forceRefresh = false } = req.query;

    if (!query) {
      return res.status(400).json({ success: false, message: 'Search query is required' });
    }

    // Determine region from state if provided but region not specified
    let searchRegion = region;
    if (!searchRegion && state) {
      const southIndianStates = ['Tamil Nadu', 'Kerala', 'Karnataka', 'Andhra Pradesh', 'Telangana'];
      const northIndianStates = ['Punjab', 'Haryana', 'Uttar Pradesh', 'Uttarakhand', 'Delhi', 'Rajasthan', 'Himachal Pradesh', 'Jammu and Kashmir'];

      if (southIndianStates.includes(state)) {
        searchRegion = 'South India';
      } else if (northIndianStates.includes(state)) {
        searchRegion = 'North India';
      }
    }

    // Create a cache key based on the query parameters
    const cacheKey = `${query}-${category || ''}-${state || ''}-${searchRegion || ''}`;

    // Check if we have cached data that's still valid
    if (!forceRefresh &&
        searchCache.data[cacheKey] &&
        (Date.now() - searchCache.timestamp[cacheKey] < searchCache.expirationTime)) {
      console.log(`Using cached search results for: ${query}`);
      return res.status(200).json({
        success: true,
        query,
        category,
        state,
        region: searchRegion,
        results: searchCache.data[cacheKey],
        fromCache: true
      });
    }

    console.log(`Searching external data for: ${query}, category: ${category}, state: ${state}, region: ${searchRegion}`);

    // Try to get data from Quamin Search with region support
    const quaminSearchResults = await quaminSearchService.searchAgricultureKnowledge(query, searchRegion);

    if (quaminSearchResults && quaminSearchResults.length > 0 && !quaminSearchResults[0].isError) {
      console.log(`Got ${quaminSearchResults.length} results from Quamin Search`);

      // Transform Quamin Search results to match the expected format
      const transformedResults = quaminSearchResults.map(result => {
        // Ensure URL is valid and working
        let url = result.url;
        if (!url || url === '#' || !isValidUrl(url)) {
          // Provide a valid fallback URL based on the source and region
          if (result.source && result.source.toLowerCase().includes('icar')) {
            url = 'https://icar.gov.in';
          } else if (result.source && result.source.toLowerCase().includes('rice')) {
            url = 'https://nrri.icar.gov.in';
          } else if (result.source && result.source.toLowerCase().includes('agriculture')) {
            url = 'https://agricoop.gov.in';
          } else if (result.source && result.source.toLowerCase().includes('market')) {
            url = 'https://agmarknet.gov.in';
          } else if (searchRegion === 'South India') {
            // Region-specific URLs for South India
            if (result.state === 'Tamil Nadu') {
              url = 'https://tnagrisnet.tn.gov.in';
            } else if (result.state === 'Kerala') {
              url = 'https://keralaagriculture.gov.in';
            } else if (result.state === 'Karnataka') {
              url = 'http://raitamitra.kar.nic.in';
            } else if (result.state === 'Andhra Pradesh') {
              url = 'https://www.apagrisnet.gov.in';
            } else if (result.state === 'Telangana') {
              url = 'https://agri.telangana.gov.in';
            } else {
              url = 'https://farmer.gov.in/southindia';
            }
          } else if (searchRegion === 'North India') {
            // Region-specific URLs for North India
            if (result.state === 'Punjab') {
              url = 'https://agripb.gov.in';
            } else if (result.state === 'Haryana') {
              url = 'https://agriharyana.gov.in';
            } else if (result.state === 'Uttar Pradesh') {
              url = 'http://upagripardarshi.gov.in';
            } else if (result.state === 'Rajasthan') {
              url = 'https://agriculture.rajasthan.gov.in';
            } else {
              url = 'https://farmer.gov.in/northindia';
            }
          } else {
            url = 'https://farmer.gov.in';
          }
        }

        return {
          title: result.title,
          source: result.source || 'Quamin AI Search',
          url: url,
          snippet: result.content,
          date: result.lastUpdated || new Date().toISOString().split('T')[0],
          category: result.category || 'general',
          confidence: result.confidence || 0.8,
          highlights: result.highlights || null,
          state: result.state || state,
          region: result.region || searchRegion
        };
      });

      // Cache the results
      searchCache.data[cacheKey] = transformedResults;
      searchCache.timestamp[cacheKey] = Date.now();

      return res.status(200).json({
        success: true,
        query,
        category,
        state,
        region: searchRegion,
        results: transformedResults,
        source: 'Quamin AI Search'
      });
    }

    // If Quamin Search didn't return results, try to get data from the India Agriculture API
    console.log('Falling back to India Agriculture API');

    try {
      // Define states based on region for filtering
      const southIndianStates = ['Tamil Nadu', 'Kerala', 'Karnataka', 'Andhra Pradesh', 'Telangana'];
      const northIndianStates = ['Punjab', 'Haryana', 'Uttar Pradesh', 'Uttarakhand', 'Delhi', 'Rajasthan', 'Himachal Pradesh', 'Jammu and Kashmir'];

      // Fetch data from the India Agriculture API
      const apiUrl = 'https://api.data.gov.in/resource/9ef84268-d588-465a-a308-a864a43d0070';
      const apiKey = process.env.AGMARKNET_API_KEY || process.env.INDIA_AGRI_API_KEY || '579b464db66ec23bdd000001cdd3946e44ce4aad7209ff7b23ac571b';

      const response = await axios.get(apiUrl, {
        params: {
          'api-key': apiKey,
          format: 'json',
          limit: 100,
          offset: 0
        },
        timeout: 10000 // 10 second timeout
      });

      if (response.data && response.data.records && Array.isArray(response.data.records)) {
        console.log(`Got ${response.data.records.length} records from India Agriculture API`);

        // Filter records based on query, category, state, and region
        let filteredRecords = response.data.records;

        // Filter by state if provided
        if (state) {
          filteredRecords = filteredRecords.filter(record =>
            record.state && record.state.toLowerCase().includes(state.toLowerCase())
          );
        }
        // Filter by region if provided or derived from state
        else if (searchRegion) {
          if (searchRegion === 'South India') {
            filteredRecords = filteredRecords.filter(record =>
              southIndianStates.includes(record.state)
            );
            console.log(`Filtered to ${filteredRecords.length} records from South India`);
          } else if (searchRegion === 'North India') {
            filteredRecords = filteredRecords.filter(record =>
              northIndianStates.includes(record.state)
            );
            console.log(`Filtered to ${filteredRecords.length} records from North India`);
          }
        }

        // Extract keywords from the query
        const keywords = query.toLowerCase().split(/\s+/).filter(term =>
          !['in', 'the', 'and', 'or', 'of', 'for', 'south', 'north', 'india', 'indian'].includes(term)
        );

        // Filter records that match any of the keywords
        if (keywords.length > 0) {
          filteredRecords = filteredRecords.filter(record => {
            const recordText = `${record.state} ${record.district} ${record.market} ${record.commodity}`.toLowerCase();
            return keywords.some(keyword => recordText.includes(keyword));
          });
          console.log(`Further filtered to ${filteredRecords.length} records based on query terms`);
        }

        // If we still have too many records, limit to the most relevant ones
        if (filteredRecords.length > 15) {
          filteredRecords = filteredRecords.slice(0, 15);
        }

        if (filteredRecords.length > 0) {
          // Transform the records to match the expected format
          const transformedResults = filteredRecords.map(record => {
            // Determine region based on state
            let recordRegion = null;
            if (southIndianStates.includes(record.state)) {
              recordRegion = 'South India';
            } else if (northIndianStates.includes(record.state)) {
              recordRegion = 'North India';
            }

            // Create a valid URL for the commodity
            let commodityUrl;
            const commodity = record.commodity.toLowerCase();

            if (commodity.includes('rice')) {
              commodityUrl = 'https://farmer.gov.in/cropstaticsrice.aspx';
            } else if (commodity.includes('wheat')) {
              commodityUrl = 'https://farmer.gov.in/cropstaticswheat.aspx';
            } else if (commodity.includes('pulse') || commodity.includes('gram') || commodity.includes('tur')) {
              commodityUrl = 'https://farmer.gov.in/croppulses.aspx';
            } else if (commodity.includes('onion') || commodity.includes('potato') || commodity.includes('tomato')) {
              commodityUrl = 'https://farmer.gov.in/cropstaticsvegetable.aspx';
            } else {
              commodityUrl = 'https://agmarknet.gov.in/PriceAndArrivals/commoditywise.aspx';
            }

            return {
              title: `${record.commodity} in ${record.state}, ${record.district}`,
              source: 'India Agriculture Data Portal',
              url: commodityUrl,
              snippet: `Market: ${record.market}. Modal Price: ₹${record.modal_price} per quintal. Arrival Date: ${record.arrival_date}`,
              date: record.arrival_date || new Date().toISOString().split('T')[0],
              category: 'market-prices',
              price: parseFloat(record.modal_price) / 100, // Convert from quintal to kg
              state: record.state,
              district: record.district,
              market: record.market,
              region: recordRegion || searchRegion
            };
          });

          // Cache the results
          searchCache.data[cacheKey] = transformedResults;
          searchCache.timestamp[cacheKey] = Date.now();

          return res.status(200).json({
            success: true,
            query,
            category,
            state,
            region: searchRegion,
            results: transformedResults,
            source: 'India Agriculture API'
          });
        }
      }
    } catch (apiError) {
      console.error('Error fetching data from India Agriculture API:', apiError);
      // Continue to fallback data
    }

    // If all API calls fail, use fallback data
    console.log('Using fallback data for external search');

    // Try to get data from Agmarknet directly as a last resort
    try {
      console.log('Trying to fetch data from Agmarknet directly');

      // Use HTTPS instead of HTTP to avoid mixed content issues
      const agmarknetUrl = 'https://agmarknet.gov.in/PriceAndArrivals/CommodityDailyStateWise_Latest.aspx';

      const response = await axios.get(agmarknetUrl, {
        timeout: 10000
      });

      if (response.data) {
        console.log('Successfully fetched data from Agmarknet');

        // Extract relevant information from the HTML response
        const commodities = quaminSearchService.extractCommoditiesFromHTML(response.data, searchRegion);

        if (commodities && commodities.length > 0) {
          const transformedResults = commodities.map(commodity => ({
            title: commodity.name,
            source: 'Agmarknet',
            url: 'https://agmarknet.gov.in/PriceAndArrivals/CommodityDailyStateWise_Latest.aspx',
            snippet: `${commodity.name} - Price: ₹${commodity.price}/kg. ${commodity.description}`,
            date: new Date().toISOString().split('T')[0],
            category: 'market-prices',
            price: commodity.price,
            state: commodity.state,
            region: commodity.region
          }));

          // Cache the results
          searchCache.data[cacheKey] = transformedResults;
          searchCache.timestamp[cacheKey] = Date.now();

          return res.status(200).json({
            success: true,
            query,
            category,
            state,
            region: searchRegion,
            results: transformedResults,
            source: 'Agmarknet'
          });
        }
      }
    } catch (agmarknetError) {
      console.error('Error fetching from Agmarknet:', agmarknetError.message);
    }

    // If all data sources fail, generate region-specific fallback data
    console.log('All data sources failed, generating region-specific fallback data');

    // Use the Quamin Search service's fallback data generator
    const fallbackResults = searchRegion
      ? quaminSearchService.generateRegionSpecificFallbackData(query, searchRegion)
      : generateFallbackResults(query, category, state);

    // Cache the fallback results
    searchCache.data[cacheKey] = fallbackResults;
    searchCache.timestamp[cacheKey] = Date.now();

    res.status(200).json({
      success: true,
      query,
      category,
      state,
      region: searchRegion,
      results: fallbackResults,
      source: 'Quamin AI Search'
    });
  } catch (error) {
    console.error('Error searching external data:', error);
    res.status(500).json({ success: false, message: 'Failed to search external data', error: error.message });
  }
});

// Get external data categories
router.get('/categories', (req, res) => {
  try {
    const categories = [
      { id: 'cultivation', name: 'Cultivation Techniques' },
      { id: 'disease-management', name: 'Disease Management' },
      { id: 'pest-control', name: 'Pest Control' },
      { id: 'organic-farming', name: 'Organic Farming' },
      { id: 'market-prices', name: 'Market Prices' },
      { id: 'soil-management', name: 'Soil Management' },
      { id: 'irrigation', name: 'Irrigation Methods' },
      { id: 'crop-rotation', name: 'Crop Rotation' },
      { id: 'fertilizers', name: 'Fertilizers' },
      { id: 'government-schemes', name: 'Government Schemes' }
    ];

    res.status(200).json({
      success: true,
      categories
    });
  } catch (error) {
    console.error('Error getting external data categories:', error);
    res.status(500).json({ success: false, message: 'Failed to get categories', error: error.message });
  }
});

// Get external data sources
router.get('/sources', (req, res) => {
  try {
    const sources = [
      { id: 'icar', name: 'Indian Council of Agricultural Research', url: 'https://icar.org.in' },
      { id: 'nrri', name: 'National Rice Research Institute', url: 'https://nrri.icar.gov.in' },
      { id: 'agricoop', name: 'Ministry of Agriculture & Farmers Welfare', url: 'https://agricoop.gov.in' },
      { id: 'agmarknet', name: 'Agricultural Marketing Information Network', url: 'https://agmarknet.gov.in' },
      { id: 'nbsslup', name: 'National Bureau of Soil Survey and Land Use Planning', url: 'https://nbsslup.icar.gov.in' },
      { id: 'iari', name: 'Indian Agricultural Research Institute', url: 'https://iari.res.in' },
      { id: 'kvk', name: 'Krishi Vigyan Kendra', url: 'https://kvk.icar.gov.in' },
      { id: 'nabard', name: 'National Bank for Agriculture and Rural Development', url: 'https://www.nabard.org' },
      { id: 'fci', name: 'Food Corporation of India', url: 'https://fci.gov.in' },
      { id: 'apeda', name: 'Agricultural and Processed Food Products Export Development Authority', url: 'https://apeda.gov.in' }
    ];

    res.status(200).json({
      success: true,
      sources
    });
  } catch (error) {
    console.error('Error getting external data sources:', error);
    res.status(500).json({ success: false, message: 'Failed to get sources', error: error.message });
  }
});

// Helper function to generate fallback results based on the query
function generateFallbackResults(query, category, state, region = null) {
  // Define states based on region for filtering
  const southIndianStates = ['Tamil Nadu', 'Kerala', 'Karnataka', 'Andhra Pradesh', 'Telangana'];
  const northIndianStates = ['Punjab', 'Haryana', 'Uttar Pradesh', 'Uttarakhand', 'Delhi', 'Rajasthan', 'Himachal Pradesh', 'Jammu and Kashmir'];

  // Determine region from state if provided but region not specified
  if (!region && state) {
    if (southIndianStates.includes(state)) {
      region = 'South India';
    } else if (northIndianStates.includes(state)) {
      region = 'North India';
    }
  }

  // Base set of results with valid, working links
  let baseResults = [];

  // Region-specific results
  if (region === 'South India') {
    baseResults = [
      {
        title: 'Rice Cultivation in South India',
        source: 'Tamil Nadu Agricultural University',
        url: 'https://tnau.ac.in/research/rice',
        snippet: 'Comprehensive guide to rice cultivation techniques specific to South Indian climatic conditions.',
        date: '2023-05-15',
        category: 'cultivation',
        region: 'South India',
        state: 'Tamil Nadu'
      },
      {
        title: 'Coconut Farming in Kerala',
        source: 'Kerala Agricultural University',
        url: 'https://kau.in/research/coconut',
        snippet: 'Best practices for coconut cultivation in Kerala, including disease management and yield improvement.',
        date: '2023-04-10',
        category: 'cultivation',
        region: 'South India',
        state: 'Kerala'
      },
      {
        title: 'Coffee Plantation Management in Karnataka',
        source: 'Coffee Board of India',
        url: 'https://www.indiacoffee.org',
        snippet: 'Guidelines for coffee plantation management in the Western Ghats region of Karnataka.',
        date: '2023-03-22',
        category: 'plantation-management',
        region: 'South India',
        state: 'Karnataka'
      },
      {
        title: 'Spice Cultivation in South India',
        source: 'Spices Board India',
        url: 'https://www.indianspices.com',
        snippet: 'Information on cultivation of various spices like cardamom, pepper, and turmeric in South Indian states.',
        date: '2023-02-18',
        category: 'spices',
        region: 'South India',
        state: 'Kerala'
      },
      {
        title: 'Mango Varieties of Andhra Pradesh',
        source: 'Andhra Pradesh Horticulture Department',
        url: 'https://www.apagrisnet.gov.in',
        snippet: 'Details about various mango varieties grown in Andhra Pradesh and their market potential.',
        date: '2023-06-05',
        category: 'horticulture',
        region: 'South India',
        state: 'Andhra Pradesh'
      }
    ];
  } else if (region === 'North India') {
    baseResults = [
      {
        title: 'Wheat Cultivation in Punjab',
        source: 'Punjab Agricultural University',
        url: 'https://pau.edu',
        snippet: 'Advanced techniques for wheat cultivation in Punjab, including variety selection and irrigation management.',
        date: '2023-04-12',
        category: 'cultivation',
        region: 'North India',
        state: 'Punjab'
      },
      {
        title: 'Basmati Rice Production in Haryana',
        source: 'Haryana Agricultural University',
        url: 'https://hau.ac.in',
        snippet: 'Guidelines for growing premium quality basmati rice in Haryana with export potential.',
        date: '2023-03-18',
        category: 'cultivation',
        region: 'North India',
        state: 'Haryana'
      },
      {
        title: 'Sugarcane Farming in Uttar Pradesh',
        source: 'UP Sugarcane Research Institute',
        url: 'https://upcane.gov.in',
        snippet: 'Best practices for sugarcane cultivation in Uttar Pradesh, including variety selection and disease management.',
        date: '2023-05-20',
        category: 'cash-crops',
        region: 'North India',
        state: 'Uttar Pradesh'
      },
      {
        title: 'Apple Cultivation in Himachal Pradesh',
        source: 'Himachal Pradesh Horticulture Department',
        url: 'https://hphorticulture.nic.in',
        snippet: 'Comprehensive guide to apple cultivation in the hilly regions of Himachal Pradesh.',
        date: '2023-02-15',
        category: 'horticulture',
        region: 'North India',
        state: 'Himachal Pradesh'
      },
      {
        title: 'Mustard Cultivation in Rajasthan',
        source: 'Rajasthan Agricultural Research Institute',
        url: 'https://agriculture.rajasthan.gov.in',
        snippet: 'Techniques for growing high-yield mustard crops in the arid conditions of Rajasthan.',
        date: '2023-01-25',
        category: 'oilseeds',
        region: 'North India',
        state: 'Rajasthan'
      }
    ];
  } else {
    // Generic results for all India
    baseResults = [
      {
        title: 'Wheat Cultivation Techniques',
        source: 'Indian Council of Agricultural Research',
        url: 'https://icar.gov.in/content/wheat',
        snippet: 'Modern techniques for wheat cultivation in India, including soil preparation, seed selection, and irrigation methods.',
        date: '2023-01-15',
        category: 'cultivation',
        region: 'All India'
      },
      {
        title: 'Rice Disease Management',
        source: 'National Rice Research Institute',
        url: 'https://nrri.icar.gov.in',
        snippet: 'Comprehensive guide to identifying and managing common rice diseases in tropical climates.',
        date: '2023-03-22',
        category: 'disease-management',
        region: 'All India'
      },
      {
        title: 'Organic Farming Certification Process',
        source: 'Ministry of Agriculture & Farmers Welfare',
        url: 'https://agricoop.gov.in',
        snippet: 'Step-by-step guide to obtaining organic farming certification for Indian farmers.',
        date: '2023-02-10',
        category: 'organic-farming',
        region: 'All India'
      },
      {
        title: 'Market Prices for Agricultural Commodities',
        source: 'Agricultural Marketing Information Network',
        url: 'https://agmarknet.gov.in',
        snippet: 'Daily updates on market prices for various agricultural commodities across different states in India.',
        date: '2023-04-01',
        category: 'market-prices',
        region: 'All India'
      },
      {
        title: 'Soil Health Management Practices',
        source: 'National Bureau of Soil Survey and Land Use Planning',
        url: 'https://www.nbsslup.in',
        snippet: 'Best practices for maintaining and improving soil health for sustainable agriculture.',
        date: '2023-01-30',
        category: 'soil-management',
        region: 'All India'
      }
    ];
  }

  // Filter results based on category if provided
  let filteredResults = [...baseResults];

  if (category) {
    filteredResults = filteredResults.filter(result => result.category === category);
  }

  // Filter results based on query keywords
  if (query) {
    const keywords = query.toLowerCase().split(/\s+/).filter(term =>
      !['in', 'the', 'and', 'or', 'of', 'for', 'south', 'north', 'india', 'indian'].includes(term)
    );

    if (keywords.length > 0) {
      filteredResults = filteredResults.filter(result => {
        const resultText = `${result.title} ${result.snippet}`.toLowerCase();
        return keywords.some(keyword => resultText.includes(keyword));
      });
    }
  }

  // Add state-specific information if state is provided but not already included
  if (state && !filteredResults.some(result => result.state === state)) {
    filteredResults = filteredResults.map(result => ({
      ...result,
      stateRelevance: `This information is particularly relevant for farmers in ${state}.`,
      title: result.title.includes(state) ? result.title : result.title + ` for ${state}`,
      state: result.state || state
    }));
  }

  // If no results match the filters, return all results
  if (filteredResults.length === 0) {
    filteredResults = baseResults;
  }

  // Ensure all results have a source attribute set to Quamin AI Search
  filteredResults = filteredResults.map(result => ({
    ...result,
    source: result.source || 'Quamin AI Search'
  }));

  return filteredResults;
}

// Helper function to check if a URL is valid
function isValidUrl(url) {
  try {
    // Check if the URL is valid
    new URL(url);

    // Check if the URL has a valid protocol (http or https)
    return url.startsWith('http://') || url.startsWith('https://');
  } catch (error) {
    return false;
  }
}

module.exports = router;
