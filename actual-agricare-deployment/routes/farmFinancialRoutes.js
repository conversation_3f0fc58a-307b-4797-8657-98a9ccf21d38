const express = require('express');
const router = express.Router();
const {
  getFarmerFinancialData,
  getYieldHistory,
  getPaymentHistory,
  getLoanDetails,
  updateFinancialData,
  addLoan,
  addYieldRecord,
  addPaymentRecord,
  generateFinancialReport
} = require('../controllers/farmFinancialController');

// Get financial data for a specific farmer
router.get('/farmer/:farmerId', getFarmerFinancialData);

// Get yield history with filters
router.get('/yield-history', getYieldHistory);

// Get payment history with filters
router.get('/payment-history', getPaymentHistory);

// Get loan details with filters
router.get('/loans', getLoanDetails);

// Create or update financial data for a farmer
router.put('/farmer/:farmerId', updateFinancialData);

// Add a new loan
router.post('/farmer/:farmerId/loans', addLoan);

// Add a new yield record
router.post('/farmer/:farmerId/yield', addYieldRecord);

// Add a payment record
router.post('/farmer/:farmerId/payment', addPaymentRecord);

// Generate financial report
router.get('/farmer/:farmerId/report', generateFinancialReport);

module.exports = router;
