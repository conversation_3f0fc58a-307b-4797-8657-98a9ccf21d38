const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const multer = require('multer');
const Farmer = require('../models/Farmer');
const mongoose = require('mongoose');
const Farm = require('../models/Farm');
const FarmData = require('../models/FarmData');

// Configure multer for handling file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/');
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + '.' + file.originalname.split('.').pop());
  }
});

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  }
});

// Get farmer statistics
router.get('/stats', auth, async (req, res) => {
  try {
    console.log('Fetching farmer statistics...');
    
    // Use Promise.all for parallel execution
    const [totalFarmers, recentFarmers] = await Promise.all([
      Farmer.countDocuments(),
      Farmer.find()
        .sort({ createdAt: -1 })
        .limit(10)
    ]);

    console.log(`Found ${totalFarmers} total farmers and ${recentFarmers.length} recent farmers`);

    res.json({
      success: true,
      data: {
        totalFarmers,
        activeAlerts: 0, // Placeholder - implement actual alerts count
        pendingTasks: 0, // Placeholder - implement actual tasks count
        recentOnboarding: recentFarmers.length
      }
    });
  } catch (error) {
    console.error('Error fetching farmer statistics:', {
      message: error.message,
      stack: error.stack
    });
    
    res.status(500).json({
      success: false,
      message: 'Failed to fetch farmer statistics'
    });
  }
});

// Get recently onboarded farmers
router.get('/recent', auth, async (req, res) => {
  try {
    console.log('Fetching recently onboarded farmers...');
    
    const recentFarmers = await Farmer.find()
      .select('name mobile state district createdAt')
      .sort({ createdAt: -1 })
      .limit(10);

    console.log(`Found ${recentFarmers.length} recent farmers`);
    
    res.json({
      success: true,
      data: {
        farmers: recentFarmers
      }
    });
  } catch (error) {
    console.error('Error fetching recent farmers:', {
      message: error.message,
      stack: error.stack
    });
    
    res.status(500).json({
      success: false,
      message: 'Failed to fetch recent farmers'
    });
  }
});

// Get alerts statistics
router.get('/alerts/stats', auth, async (req, res) => {
  try {
    console.log('Fetching alerts statistics...');
    
    // For now, return placeholder data
    res.json({
      success: true,
      data: {
        totalAlerts: 0,
        criticalAlerts: 0,
        warningAlerts: 0,
        infoAlerts: 0
      }
    });
  } catch (error) {
    console.error('Error fetching alerts statistics:', {
      message: error.message,
      stack: error.stack
    });
    
    res.status(500).json({
      success: false,
      message: 'Failed to fetch alerts statistics'
    });
  }
});

// Create a new farmer
router.post('/', auth, upload.single('photo'), async (req, res) => {
  try {
    // Log the complete request data
    console.log('Complete farmer onboarding request:', {
      body: req.body,
      file: req.file,
      headers: req.headers
    });

    const farmerData = {
      ...req.body,
      photo: req.file ? req.file.path : null,
      geoLocation: req.body.geoLocation ? JSON.parse(req.body.geoLocation) : null
    };

    // Log the processed farmer data
    console.log('Processed farmer data:', {
      ...farmerData,
      photo: farmerData.photo ? 'Photo path exists' : 'No photo',
      geoLocation: farmerData.geoLocation ? 'Location data exists' : 'No location'
    });

    // Check for existing farmer with same Aadhar or PAN
    const existingFarmer = await Farmer.findOne({
      $or: [
        { aadharNumber: farmerData.aadharNumber },
        { panNumber: farmerData.panNumber }
      ]
    });

    if (existingFarmer) {
      console.error('Duplicate farmer found:', {
        aadharNumber: existingFarmer.aadharNumber === farmerData.aadharNumber,
        panNumber: existingFarmer.panNumber === farmerData.panNumber
      });
      return res.status(400).json({
        success: false,
        message: 'A farmer with this Aadhar number or PAN number already exists'
      });
    }

    // Create new farmer instance and validate
    const farmer = new Farmer(farmerData);
    const validationError = farmer.validateSync();
    if (validationError) {
      console.error('Validation error:', validationError);
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: Object.values(validationError.errors).map(err => err.message)
      });
    }

    // Save the farmer
    await farmer.save();
    console.log('Farmer saved successfully:', farmer._id);

    res.status(201).json({
      success: true,
      message: 'Farmer onboarded successfully',
      data: farmer
    });
  } catch (error) {
    console.error('Detailed error in farmer creation:', {
      message: error.message,
      name: error.name,
      code: error.code,
      stack: error.stack,
      keyPattern: error.keyPattern,
      keyValue: error.keyValue
    });

    if (error.name === 'ValidationError') {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: Object.values(error.errors).map(err => err.message)
      });
    }

    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Duplicate entry found',
        field: Object.keys(error.keyPattern)[0]
      });
    }

    res.status(500).json({
      success: false,
      message: error.message || 'Failed to onboard farmer'
    });
  }
});

// Get all farmers with detailed error handling
router.get('/', auth, async (req, res) => {
  try {
    console.log('Fetching all farmers...');
    const farmers = await Farmer.find({})
      .select('-__v')
      .sort({ createdAt: -1 });

    console.log(`Found ${farmers.length} farmers`);
    
    res.json({
      success: true,
      count: farmers.length,
      data: farmers
    });
  } catch (error) {
    console.error('Error fetching farmers:', {
      message: error.message,
      stack: error.stack
    });
    
    res.status(500).json({
      success: false,
      message: 'Failed to fetch farmers',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Get a single farmer with enhanced error handling
router.get('/:id', auth, async (req, res) => {
  try {
    console.log(`Fetching farmer with ID: ${req.params.id}`);
    
    const farmer = await Farmer.findById(req.params.id).select('-__v');
    if (!farmer) {
      console.log(`No farmer found with ID: ${req.params.id}`);
      return res.status(404).json({
        success: false,
        message: 'Farmer not found'
      });
    }

    console.log(`Successfully retrieved farmer: ${farmer._id}`);
    res.json({
      success: true,
      data: farmer
    });
  } catch (error) {
    console.error('Error fetching farmer details:', {
      id: req.params.id,
      error: error.message,
      stack: error.stack
    });
    
    if (error.name === 'CastError') {
      return res.status(400).json({
        success: false,
        message: 'Invalid farmer ID format'
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Failed to fetch farmer details',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Search farmers by mobile number or ID
router.get('/search', auth, async (req, res) => {
  try {
    const { query } = req.query;
    
    if (!query) {
      return res.status(400).json({
        success: false,
        message: 'Search query is required'
      });
    }

    console.log('Searching farmers with query:', query);

    // Search by mobile number or ID
    const farmers = await Farmer.find({
      $or: [
        { mobile: { $regex: query, $options: 'i' } },
        { _id: mongoose.isValidObjectId(query) ? query : null }
      ]
    }).select('-__v');

    console.log(`Found ${farmers.length} farmers matching query`);

    res.json({
      success: true,
      count: farmers.length,
      data: farmers
    });
  } catch (error) {
    console.error('Error searching farmers:', {
      message: error.message,
      stack: error.stack
    });
    
    res.status(500).json({
      success: false,
      message: 'Failed to search farmers',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Get farmer dashboard with critical alerts
router.get('/:id/dashboard', auth, async (req, res) => {
  try {
    console.log(`Fetching dashboard for farmer ID: ${req.params.id}`);
    
    const farmer = await Farmer.findById(req.params.id).select('-__v');
    if (!farmer) {
      console.log(`No farmer found with ID: ${req.params.id}`);
      return res.status(404).json({
        success: false,
        message: 'Farmer not found'
      });
    }

    // Get all farms associated with the farmer
    const farms = await Farm.find({ farmerId: farmer._id });
    
    // Get all critical alerts from all farms
    const criticalAlerts = [];
    for (const farm of farms) {
      const farmData = await FarmData.find({ 
        farmId: farm._id,
        'alerts.type': 'Critical'
      }).sort({ timestamp: -1 }).limit(10);
      
      farmData.forEach(data => {
        data.alerts.forEach(alert => {
          if (alert.type === 'Critical') {
            criticalAlerts.push({
              farmId: farm._id,
              farmName: farm.farmerName,
              alert: alert
            });
          }
        });
      });
    }

    // Sort alerts by timestamp
    criticalAlerts.sort((a, b) => b.alert.timestamp - a.alert.timestamp);

    console.log(`Successfully retrieved dashboard for farmer: ${farmer._id}`);
    res.json({
      success: true,
      data: {
        farmer,
        farms,
        criticalAlerts
      }
    });
  } catch (error) {
    console.error('Error fetching farmer dashboard:', {
      id: req.params.id,
      error: error.message,
      stack: error.stack
    });
    
    if (error.name === 'CastError') {
      return res.status(400).json({
        success: false,
        message: 'Invalid farmer ID format'
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Failed to fetch farmer dashboard',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Farmer login endpoint
router.post('/login', async (req, res) => {
  try {
    const { phoneNumber } = req.body;
    
    if (!phoneNumber) {
      return res.status(400).json({
        success: false,
        message: 'Phone number is required'
      });
    }

    // Find farmer by phone number
    let farmer = await Farmer.findOne({ mobile: phoneNumber });
    
    // If farmer doesn't exist, create a new one with default location
    if (!farmer) {
      farmer = await Farmer.create({
        name: `Farmer${phoneNumber.slice(-4)}`,
        mobile: phoneNumber,
        aadharNumber: '123456789012', // Temporary Aadhar number
        panNumber: '**********', // Temporary PAN number
        state: 'Maharashtra',
        district: 'Pune',
        farmSize: '1-2 acres',
        cropType: 'Wheat',
        irrigationStatus: 'Well',
        geoLocation: {
          type: 'Point',
          coordinates: [78.9629, 20.5937] // Default to India's coordinates
        }
      });
    }

    // Return farmer data with MongoDB ObjectId
    res.json({
      success: true,
      data: {
        id: farmer._id.toString(), // Convert ObjectId to string
        name: farmer.name,
        mobile: farmer.mobile,
        role: 'Farmer'
      }
    });
  } catch (error) {
    console.error('Farmer login error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to login'
    });
  }
});

// Helper function to normalize phone number
const normalizePhoneNumber = (phoneNumber) => {
    // Remove any non-digit characters
    const digits = phoneNumber.replace(/\D/g, '');
    
    // If it starts with country code (e.g., 91 for India), return as is
    if (digits.length > 10) {
        return digits; // Return just the digits for comparison
    }
    
    // Otherwise, assume Indian number and add 91
    return `91${digits}`;
};

// Verify farmer
router.post('/verify', async (req, res) => {
    try {
        const { phoneNumber } = req.body;

        if (!phoneNumber) {
            return res.status(400).json({
                success: false,
                message: 'Phone number is required'
            });
        }

        // Normalize the phone number for consistent comparison
        const normalizedInputNumber = normalizePhoneNumber(phoneNumber);
        console.log('Normalized input phone number:', normalizedInputNumber);

        // Find farmer and normalize their stored number for comparison
        const farmers = await Farmer.find();
        const farmer = farmers.find(f => normalizePhoneNumber(f.mobile) === normalizedInputNumber);
        console.log('Found farmer:', farmer);

        if (!farmer) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized: Not a registered farmer'
            });
        }

        res.json({
            success: true,
            message: 'Farmer verified successfully',
            farmer: {
                id: farmer._id,
                name: farmer.name,
                phoneNumber: farmer.mobile
            }
        });

    } catch (error) {
        console.error('Error verifying farmer:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to verify farmer'
        });
    }
});

module.exports = router; 