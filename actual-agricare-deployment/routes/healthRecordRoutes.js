const express = require('express');
const router = express.Router();
const { 
  getAllHealthRecords, 
  getLivestockHealthRecords, 
  getVetHealthRecords, 
  createHealthRecord, 
  updateHealthRecord, 
  deleteHealthRecord,
  generateHealthReport
} = require('../controllers/healthRecordController');

// Get all health records
router.get('/', getAllHealthRecords);

// Get health records for a specific livestock
router.get('/livestock/:livestockId', getLivestockHealthRecords);

// Get health records created by a specific veterinarian
router.get('/vet/:vetId', getVetHealthRecords);

// Create a new health record
router.post('/', createHealthRecord);

// Update a health record
router.put('/:id', updateHealthRecord);

// Delete a health record
router.delete('/:id', deleteHealthRecord);

// Generate health report for a specific livestock
router.get('/report/:livestockId', generateHealthReport);

module.exports = router;
