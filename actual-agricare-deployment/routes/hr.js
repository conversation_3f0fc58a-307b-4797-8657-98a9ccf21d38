const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

// HR Schema
const hrSchema = new mongoose.Schema({
    name: { type: String, required: true },
    email: { type: String, required: true, unique: true },
    phoneNumber: { type: String, required: true, unique: true },
    password: { type: String, required: true },
    createdAt: { type: Date, default: Date.now }
});

const HR = mongoose.model('HR', hrSchema);

// The secret key that allows HR signup (in production, this should be in environment variables)
const ADMIN_SECRET_KEY = "agricare_admin_2024";

// HR Signup route
router.post('/signup', async (req, res) => {
    console.log('Received HR signup request:', {
        ...req.body,
        password: '[REDACTED]',
        secretKey: '[REDACTED]'
    });

    try {
        const { name, email, phoneNumber, password, secretKey } = req.body;

        // Input validation
        if (!name || !email || !phoneNumber || !password || !secretKey) {
            console.log('Missing required fields');
            return res.status(400).json({
                success: false,
                message: 'All fields are required'
            });
        }

        // Verify admin secret key
        if (secretKey !== ADMIN_SECRET_KEY) {
            console.log('Invalid admin secret key provided');
            return res.status(403).json({
                success: false,
                message: 'Invalid admin secret key'
            });
        }

        // Check if email already exists
        const existingEmail = await HR.findOne({ email });
        if (existingEmail) {
            console.log('Email already registered:', email);
            return res.status(400).json({
                success: false,
                message: 'Email already registered'
            });
        }

        // Check if phone number already exists
        const existingPhone = await HR.findOne({ phoneNumber });
        if (existingPhone) {
            console.log('Phone number already registered:', phoneNumber);
            return res.status(400).json({
                success: false,
                message: 'Phone number already registered'
            });
        }

        // Hash password
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(password, salt);

        // Create new HR account
        const hr = new HR({
            name,
            email,
            phoneNumber,
            password: hashedPassword
        });

        await hr.save();
        console.log('HR account created successfully:', {
            name,
            email,
            phoneNumber
        });

        res.status(201).json({
            success: true,
            message: 'HR account created successfully'
        });

    } catch (error) {
        console.error('HR Signup Error:', error);
        
        // Handle MongoDB validation errors
        if (error.name === 'ValidationError') {
            return res.status(400).json({
                success: false,
                message: Object.values(error.errors).map(err => err.message).join(', ')
            });
        }

        // Handle MongoDB duplicate key errors
        if (error.code === 11000) {
            const field = Object.keys(error.keyPattern)[0];
            return res.status(400).json({
                success: false,
                message: `${field} already exists`
            });
        }

        res.status(500).json({
            success: false,
            message: 'Failed to create HR account'
        });
    }
});

// HR Login route
router.post('/login', async (req, res) => {
    console.log('Received HR login request:', {
        email: req.body.email,
        password: '[REDACTED]'
    });

    try {
        const { email, password } = req.body;

        // Input validation
        if (!email || !password) {
            return res.status(400).json({
                success: false,
                message: 'Email and password are required'
            });
        }

        // Find HR by email
        const hr = await HR.findOne({ email });
        if (!hr) {
            console.log('Invalid login attempt - email not found:', email);
            return res.status(401).json({
                success: false,
                message: 'Invalid credentials'
            });
        }

        // Verify password
        const isValidPassword = await bcrypt.compare(password, hr.password);
        if (!isValidPassword) {
            console.log('Invalid login attempt - incorrect password for:', email);
            return res.status(401).json({
                success: false,
                message: 'Invalid credentials'
            });
        }

        console.log('HR login successful:', {
            id: hr._id,
            email: hr.email
        });

        res.json({
            success: true,
            hr: {
                id: hr._id,
                name: hr.name,
                email: hr.email,
                phoneNumber: hr.phoneNumber
            }
        });

    } catch (error) {
        console.error('HR Login Error:', error);
        res.status(500).json({
            success: false,
            message: 'Login failed'
        });
    }
});

module.exports = router; 