const express = require('express');
const router = express.Router();
const { body } = require('express-validator');
const iotController = require('../controllers/iotController');
const auth = require('../middleware/auth');
const roleCheck = require('../middleware/roleCheck');

// Validation middleware
const sensorValidation = [
  body('name').trim().notEmpty().withMessage('Name is required'),
  body('type').isIn(['soil', 'weather', 'irrigation']).withMessage('Invalid sensor type'),
  body('location').trim().notEmpty().withMessage('Location is required'),
  body('ipAddress').isIP().withMessage('Invalid IP address'),
  body('port').trim().notEmpty().withMessage('Port is required'),
  body('parameters').isObject().withMessage('Parameters must be an object'),
  body('parameters.soilMoisture').isBoolean().withMessage('Soil moisture must be a boolean'),
  body('parameters.temperature').isBoolean().withMessage('Temperature must be a boolean'),
  body('parameters.humidity').isBoolean().withMessage('Humidity must be a boolean'),
  body('parameters.ph').isBoolean().withMessage('pH must be a boolean'),
  body('parameters.nitrogen').isBoolean().withMessage('Nitrogen must be a boolean'),
  body('parameters.phosphorus').isBoolean().withMessage('Phosphorus must be a boolean'),
  body('parameters.potassium').isBoolean().withMessage('Potassium must be a boolean')
];

const readingsValidation = [
  body('readings').isObject().withMessage('Readings must be an object'),
  body('readings.soilMoisture').optional().isFloat({ min: 0, max: 100 }).withMessage('Invalid soil moisture value'),
  body('readings.temperature').optional().isFloat({ min: -40, max: 80 }).withMessage('Invalid temperature value'),
  body('readings.humidity').optional().isFloat({ min: 0, max: 100 }).withMessage('Invalid humidity value'),
  body('readings.ph').optional().isFloat({ min: 0, max: 14 }).withMessage('Invalid pH value'),
  body('readings.nitrogen').optional().isFloat({ min: 0 }).withMessage('Invalid nitrogen value'),
  body('readings.phosphorus').optional().isFloat({ min: 0 }).withMessage('Invalid phosphorus value'),
  body('readings.potassium').optional().isFloat({ min: 0 }).withMessage('Invalid potassium value')
];

// Routes
router.get('/', auth, roleCheck(['territory_manager']), iotController.getAllSensors);
router.get('/:id', auth, roleCheck(['territory_manager']), iotController.getSensor);
router.post('/', auth, roleCheck(['territory_manager']), sensorValidation, iotController.createSensor);
router.put('/:id', auth, roleCheck(['territory_manager']), sensorValidation, iotController.updateSensor);
router.delete('/:id', auth, roleCheck(['territory_manager']), iotController.deleteSensor);

// Sensor data routes
router.post('/:id/readings', auth, roleCheck(['territory_manager']), readingsValidation, iotController.updateReadings);
router.put('/:id/battery', auth, roleCheck(['territory_manager']), 
  body('batteryLevel').isFloat({ min: 0, max: 100 }).withMessage('Invalid battery level'),
  iotController.updateBatteryLevel
);
router.get('/:id/readings', auth, roleCheck(['territory_manager']), iotController.getReadingsHistory);
router.get('/:id/stats', auth, roleCheck(['territory_manager']), iotController.getSensorStats);

module.exports = router; 