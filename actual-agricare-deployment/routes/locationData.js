const express = require('express');
const router = express.Router();
const SoilData = require('../models/SoilData');
const auth = require('../middleware/auth');
const satelliteDataService = require('../services/satelliteDataService');
const mongoose = require('mongoose');



// Debug middleware for soil-data routes
router.use('/soil-data', (req, res, next) => {
  console.log('🔍 Soil Data Request:', {
    method: req.method,
    path: req.path,
    query: req.query,
    headers: {
      authorization: req.headers.authorization ? 'Bearer [REDACTED]' : 'none',
      'content-type': req.headers['content-type']
    },
    body: req.body
  });
  next();
});

/**
 * @route POST /api/location/soil-data
 * @desc Store soil data from various sources with satellite auto-fetch
 * @access Private
 */
router.post('/soil-data', auth, async (req, res) => {
  console.log('📝 POST /soil-data - Request received');
  try {
    const { location, dataSource, coordinates, soilData, labInfo, iotInfo, dataQuality, notes } = req.body;
    console.log('📝 POST /soil-data - Request body:', { location, dataSource, coordinates });

    // Validate required fields
    if (!dataSource) {
      return res.status(400).json({
        success: false,
        message: 'dataSource is required'
      });
    }

    let processedSoilData;

    // Handle satellite data source - auto-fetch from APIs
    if (dataSource === 'satellite') {
      if (!coordinates?.lat || !coordinates?.lng) {
        return res.status(400).json({
          success: false,
          message: 'Coordinates are required for satellite data source'
        });
      }

      try {
        // Fetch from satellite service
        const satelliteData = await satelliteDataService.fetchNasaPowerData(coordinates, location);
        
        // Convert satellite data to soil data format
        processedSoilData = {
          nitrogen: 280 + (satelliteData.soilMoisture * 2),
          phosphorus: 45 + (satelliteData.soilTemperature * 0.5),
          potassium: 190 + (satelliteData.precipitation * 1.5),
          ph: 6.5 + ((satelliteData.soilMoisture - 25) * 0.02),
          organicMatter: 3.2 + (satelliteData.soilMoisture * 0.03),
          moisture: satelliteData.soilMoisture,
          temperature: satelliteData.soilTemperature,
          precipitation: satelliteData.precipitation,
          humidity: satelliteData.humidity
        };
      } catch (error) {
        console.error('Satellite data fetch error:', error);
        return res.status(502).json({
          success: false,
          message: 'Failed to fetch satellite data',
          error: error.message
        });
      }
    } else {
      // Use provided soil data for manual/lab/iot sources
      if (!soilData) {
        return res.status(400).json({
          success: false,
          message: 'soilData is required for non-satellite sources'
        });
      }
      processedSoilData = soilData;
    }

    // Generate interpretations
    const interpretation = generateInterpretations(processedSoilData);
    
    // Generate recommendations
    const recommendations = generateRecommendations(processedSoilData, interpretation);
    
    // Generate suitable crops
    const suitableCrops = generateSuitableCrops(processedSoilData, interpretation);

    // Create new soil data record
    const newSoilData = new SoilData({
      location: location || {
        coordinates: coordinates,
        level: 'coordinates'
      },
      dataSource,
      labInfo: dataSource === 'lab_testing' ? labInfo : undefined,
      iotInfo: dataSource === 'iot' ? iotInfo : undefined,
      soilData: processedSoilData,
      dataQuality,
      interpretation,
      recommendations,
      suitableCrops,
      createdBy: req.user.id,
      notes
    });

    await newSoilData.save();

    res.status(201).json({
      success: true,
      message: 'Soil data saved successfully',
      data: newSoilData
    });

  } catch (error) {
    console.error('❌ POST /soil-data - Error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to save soil data',
      error: error.message
    });
  }
});

/**
 * @route GET /api/location/soil-data
 * @desc Get soil data for a specific location
 * @access Private
 */
router.get('/soil-data', auth, async (req, res) => {
  console.log('🔍 GET /soil-data - Request received');
  try {
    const { latitude, longitude, state, district, block, village, plot, dataSource } = req.query;
    console.log('🔍 GET /soil-data - Query params:', { latitude, longitude, state, district, block, village, plot, dataSource });

    let query = { createdBy: req.user.id, isActive: true };
    console.log('🔍 GET /soil-data - User ID:', req.user.id);

    // Build location query
    if (latitude && longitude) {
      const lat = parseFloat(latitude);
      const lng = parseFloat(longitude);
      
      if (isNaN(lat) || isNaN(lng)) {
        console.log('❌ GET /soil-data - Invalid coordinates:', { latitude, longitude });
        return res.status(400).json({
          success: false,
          message: 'Invalid coordinates'
        });
      }

      console.log('🔍 GET /soil-data - Searching with coordinates:', { lat, lng });

      // Find data within 1km radius
      query['location.coordinates'] = {
        $near: {
          $geometry: { type: 'Point', coordinates: [lng, lat] },
          $maxDistance: 1000
        }
      };

      // Remove createdBy from query in development mode
      if (process.env.NODE_ENV === 'development') {
        delete query.createdBy;
      }

      // If no stored data found, fetch from satellite
      const storedData = await SoilData.find(query)
        .sort({ createdAt: -1 })
        .limit(10)
        .populate('createdBy', 'name email')
        .populate('verifiedBy', 'name email')
        .lean();

      console.log('🔍 GET /soil-data - Found stored data:', storedData.length);

      if (storedData.length === 0) {
        console.log('🔍 GET /soil-data - No stored data, fetching from satellite');
        try {
          // Fetch real-time satellite data
          const satelliteData = await satelliteDataService.fetchSoilData(lat, lng);
          console.log('🔍 GET /soil-data - Satellite data fetched:', satelliteData);
          
          // Create new soil data record with satellite data
          // Create a proper ObjectId for development user
          const createdById = req.user.id === 'development-user-id'
            ? new mongoose.Types.ObjectId()
            : req.user.id;

          const interpretation = generateInterpretations(satelliteData);
          const newSoilData = new SoilData({
            location: {
              coordinates: { lat, lng },
              level: 'coordinates'
            },
            dataSource: 'satellite',
            soilData: satelliteData,
            createdBy: createdById,
            dataQuality: 'high',
            interpretation: interpretation,
            recommendations: generateRecommendations(satelliteData, interpretation),
            suitableCrops: generateSuitableCrops(satelliteData, interpretation),
            notes: 'Real data from NASA POWER and SoilGrids APIs'
          });

          await newSoilData.save();
          console.log('🔍 GET /soil-data - New satellite data saved');
          
          return res.status(200).json({
            success: true,
            data: {
              current: newSoilData,
              historical: [],
              totalRecords: 1,
              dataSource: 'satellite'
            }
          });
        } catch (satelliteError) {
          console.error('❌ GET /soil-data - Satellite data fetch error:', satelliteError);
          return res.status(404).json({
            success: false,
            message: 'No soil data available for this location. Satellite APIs are currently unavailable.',
            error: 'SATELLITE_API_ERROR'
          });
        }
      }

      // Return stored data
      const latestRecord = storedData[0];
      const historicalData = storedData.slice(1, 6);

      console.log('🔍 GET /soil-data - Returning stored data');
      return res.status(200).json({
        success: true,
        data: {
          current: latestRecord,
          historical: historicalData,
          totalRecords: storedData.length,
          dataSource: 'database'
        }
      });
    } else {
      // Build hierarchical location query for non-coordinate based search
      if (state) query['location.state'] = state;
      if (district) query['location.district'] = district;
      if (block) query['location.block'] = block;
      if (village) query['location.village'] = village;
      if (plot) query['location.plot'] = plot;

      // Filter by data source if specified
      if (dataSource) {
        query.dataSource = dataSource;
      }

      const soilDataRecords = await SoilData.find(query)
        .sort({ createdAt: -1 })
        .limit(10)
        .populate('createdBy', 'name email')
        .populate('verifiedBy', 'name email')
        .lean();

      if (soilDataRecords.length === 0) {
        return res.status(200).json({
          success: true,
          data: null,
          message: 'No soil data found for the specified location'
        });
      }

      const latestRecord = soilDataRecords[0];
      const historicalData = soilDataRecords.slice(1, 6);

      return res.status(200).json({
        success: true,
        data: {
          current: latestRecord,
          historical: historicalData,
          totalRecords: soilDataRecords.length,
          dataSource: 'database'
        }
      });
    }
  } catch (error) {
    console.error('❌ GET /soil-data - Error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch soil data',
      error: error.message
    });
  }
});

/**
 * @route GET /api/location/districts
 * @desc Get districts by state or all states and districts
 * @access Public
 */
router.get('/districts', async (req, res) => {
  try {
    const districtsByState = {
      'Madhya Pradesh': ['Bhopal', 'Indore', 'Jabalpur', 'Gwalior', 'Ujjain'],
      'Punjab': ['Ludhiana', 'Amritsar', 'Jalandhar', 'Patiala', 'Bathinda'],
      'Karnataka': ['Bangalore Urban', 'Mysore', 'Hubli-Dharwad', 'Mangalore', 'Belgaum'],
      'Tamil Nadu': ['Chennai', 'Coimbatore', 'Madurai', 'Tiruchirappalli', 'Salem'],
      'Uttar Pradesh': ['Lucknow', 'Kanpur', 'Agra', 'Varanasi', 'Allahabad'],
      'Maharashtra': ['Mumbai', 'Pune', 'Nagpur', 'Nashik', 'Aurangabad'],
      'West Bengal': ['Kolkata', 'Howrah', 'Durgapur', 'Asansol', 'Siliguri'],
      'Gujarat': ['Ahmedabad', 'Surat', 'Vadodara', 'Rajkot', 'Bhavnagar'],
      'Rajasthan': ['Jaipur', 'Jodhpur', 'Kota', 'Bikaner', 'Udaipur'],
      'Haryana': ['Gurgaon', 'Faridabad', 'Panipat', 'Ambala', 'Yamunanagar']
    };

    const { state } = req.query;

    if (state && districtsByState[state]) {
      return res.status(200).json({
        success: true,
        state,
        districts: districtsByState[state]
      });
    }

    const states = Object.keys(districtsByState);
    const districts = Object.values(districtsByState).flat();

    res.status(200).json({
      success: true,
      states,
      districts
    });
  } catch (error) {
    console.error('Error fetching districts:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch districts',
      error: error.message
    });
  }
});

/**
 * @route GET /api/location/blocks
 * @desc Get blocks by district
 * @access Public
 */
router.get('/blocks', async (req, res) => {
  try {
    const { district } = req.query;

    if (!district) {
      return res.status(400).json({
        success: false,
        message: 'District parameter is required'
      });
    }

    const knownBlocks = {
      'Bhopal': ['Berasia', 'Huzur', 'Phanda', 'Kolar', 'Misrod'],
      'Indore': ['Mhow', 'Depalpur', 'Sanwer', 'Hatod', 'Rau'],
      'Ludhiana': ['Ludhiana East', 'Ludhiana West', 'Jagraon', 'Khanna', 'Samrala'],
      'Bangalore Urban': ['Anekal', 'Bangalore North', 'Bangalore South', 'Bangalore East', 'Yelahanka'],
      'Chennai': ['Egmore', 'Adyar', 'Mylapore', 'T. Nagar', 'Anna Nagar']
    };

    const blocks = knownBlocks[district] || [
      `${district} North`,
      `${district} South`,
      `${district} East`,
      `${district} West`,
      `${district} Central`
    ];

    res.status(200).json({
      success: true,
      district,
      blocks
    });
  } catch (error) {
    console.error('Error fetching blocks:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch blocks',
      error: error.message
    });
  }
});

/**
 * @route GET /api/location/villages
 * @desc Get villages by district and block
 * @access Public
 */
router.get('/villages', async (req, res) => {
  try {
    const { district, block } = req.query;

    if (!district || !block) {
      return res.status(400).json({
        success: false,
        message: 'District and block parameters are required'
      });
    }

    const knownVillages = {
      'Berasia': ['Sukhi Sewania', 'Ratua Ratanpur', 'Jhiri', 'Kararia', 'Chanderi'],
      'Huzur': ['Bairagarh', 'Kokta', 'Misrod', 'Bhauri', 'Bawadia Kalan'],
      'Ludhiana East': ['Giaspura', 'Dhandari Kalan', 'Mundian Kalan', 'Bhamian Kalan', 'Jamalpur'],
      'Bangalore North': ['Yelahanka', 'Jakkur', 'Amruthahalli', 'Hebbal', 'Kodigehalli'],
      'Egmore': ['Chetpet', 'Kilpauk', 'Purasawalkam', 'Periamet', 'Vepery']
    };

    const villages = knownVillages[block] || [
      `${block} Village 1`,
      `${block} Village 2`,
      `${block} Village 3`,
      `${block} Village 4`,
      `${block} Village 5`
    ];

    res.status(200).json({
      success: true,
      district,
      block,
      villages
    });
  } catch (error) {
    console.error('Error fetching villages:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch villages',
      error: error.message
    });
  }
});

/**
 * @route GET /api/location/plots
 * @desc Get plots by district, block, and village
 * @access Public
 */
router.get('/plots', async (req, res) => {
  try {
    const { district, block, village } = req.query;

    if (!district || !block || !village) {
      return res.status(400).json({
        success: false,
        message: 'District, block, and village parameters are required'
      });
    }

    const knownPlots = {
      'Sukhi Sewania': ['Plot 101', 'Plot 102', 'Plot 103', 'Plot 104', 'Plot 105'],
      'Bairagarh': ['Plot 201', 'Plot 202', 'Plot 203', 'Plot 204', 'Plot 205'],
      'Giaspura': ['Plot 301', 'Plot 302', 'Plot 303', 'Plot 304', 'Plot 305'],
      'Yelahanka': ['Plot 401', 'Plot 402', 'Plot 403', 'Plot 404', 'Plot 405'],
      'Chetpet': ['Plot 501', 'Plot 502', 'Plot 503', 'Plot 504', 'Plot 505']
    };

    const baseNumber = Math.floor(Math.random() * 900) + 100;
    const plots = knownPlots[village] || [
      `Plot ${baseNumber + 1}`,
      `Plot ${baseNumber + 2}`,
      `Plot ${baseNumber + 3}`,
      `Plot ${baseNumber + 4}`,
      `Plot ${baseNumber + 5}`
    ];

    res.status(200).json({
      success: true,
      district,
      block,
      village,
      plots
    });
  } catch (error) {
    console.error('Error fetching plots:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch plots',
      error: error.message
    });
  }
});

// Keep all existing helper functions
function generateInterpretations(soilData) {
  const interpretations = {};

  // Only interpret data that's actually available from real APIs
  if (soilData.nitrogen !== null && soilData.nitrogen !== undefined) {
    interpretations.nitrogen = interpretNutrientLevel(soilData.nitrogen, 'nitrogen');
  }

  // Skip phosphorus and potassium since they're not available from SoilGrids v2.0
  // if (soilData.phosphorus !== null && soilData.phosphorus !== undefined) {
  //   interpretations.phosphorus = interpretNutrientLevel(soilData.phosphorus, 'phosphorus');
  // }
  // if (soilData.potassium !== null && soilData.potassium !== undefined) {
  //   interpretations.potassium = interpretNutrientLevel(soilData.potassium, 'potassium');
  // }

  if (soilData.ph !== null && soilData.ph !== undefined) {
    interpretations.ph = interpretPHLevel(soilData.ph);
  }

  if (soilData.organicMatter !== null && soilData.organicMatter !== undefined) {
    interpretations.organicMatter = interpretOrganicMatter(soilData.organicMatter);
  }

  if (soilData.moisture !== null && soilData.moisture !== undefined) {
    interpretations.moisture = interpretMoisture(soilData.moisture);
  }

  return interpretations;
}

function interpretNutrientLevel(value, nutrient) {
  const ranges = {
    nitrogen: [
      { max: 150, level: 'Very Low', description: 'Severe deficiency, immediate supplementation required' },
      { max: 250, level: 'Low', description: 'Deficient, supplementation recommended' },
      { max: 350, level: 'Medium', description: 'Adequate for most crops' },
      { max: 450, level: 'High', description: 'Abundant, no supplementation needed' },
      { max: Infinity, level: 'Very High', description: 'Excessive, may cause imbalances' }
    ],
    phosphorus: [
      { max: 20, level: 'Very Low', description: 'Severe deficiency, immediate supplementation required' },
      { max: 40, level: 'Low', description: 'Deficient, supplementation recommended' },
      { max: 60, level: 'Medium', description: 'Adequate for most crops' },
      { max: 80, level: 'High', description: 'Abundant, no supplementation needed' },
      { max: Infinity, level: 'Very High', description: 'Excessive, may cause imbalances' }
    ],
    potassium: [
      { max: 100, level: 'Very Low', description: 'Severe deficiency, immediate supplementation required' },
      { max: 175, level: 'Low', description: 'Deficient, supplementation recommended' },
      { max: 250, level: 'Medium', description: 'Adequate for most crops' },
      { max: 325, level: 'High', description: 'Abundant, no supplementation needed' },
      { max: Infinity, level: 'Very High', description: 'Excessive, may cause imbalances' }
    ]
  };

  const range = ranges[nutrient];
  if (!range) return { level: 'Unknown', description: 'Could not determine level' };

  for (const r of range) {
    if (value <= r.max) {
      return { level: r.level, description: r.description };
    }
  }

  return { level: 'Unknown', description: 'Could not determine level' };
}

function interpretPHLevel(ph) {
  if (ph < 5.5) return { level: 'Acidic', description: 'Too acidic for most crops. Consider liming to raise pH.' };
  if (ph < 6.0) return { level: 'Moderately Acidic', description: 'Slightly acidic, suitable for acid-loving crops.' };
  if (ph < 7.5) return { level: 'Neutral', description: 'Ideal pH range for most crops.' };
  if (ph < 8.5) return { level: 'Moderately Alkaline', description: 'Slightly alkaline, monitor for nutrient availability.' };
  return { level: 'Alkaline', description: 'Too alkaline for most crops. Consider amendments to lower pH.' };
}

function interpretOrganicMatter(value) {
  if (value < 1.0) return { level: 'Very Low', description: 'Severely depleted, add organic amendments immediately.' };
  if (value < 2.0) return { level: 'Low', description: 'Insufficient, add compost or other organic matter.' };
  if (value < 4.0) return { level: 'Medium', description: 'Adequate for most crops, maintain with good practices.' };
  if (value < 6.0) return { level: 'High', description: 'Good organic matter content, excellent soil health.' };
  return { level: 'Very High', description: 'Excellent organic matter content, focus on maintenance.' };
}

function interpretMoisture(value) {
  if (value < 10) return { level: 'Very Dry', description: 'Immediate irrigation needed.' };
  if (value < 20) return { level: 'Dry', description: 'Irrigation recommended soon.' };
  if (value < 30) return { level: 'Moderate', description: 'Adequate moisture for most crops.' };
  if (value < 40) return { level: 'Moist', description: 'Good moisture level, monitor for changes.' };
  return { level: 'Wet', description: 'Excessive moisture, improve drainage if persistent.' };
}

function generateRecommendations(soilData, interpretation) {
  const recommendations = [];

  // Only provide recommendations for data that's actually available from real APIs
  if (interpretation.nitrogen && (interpretation.nitrogen.level === 'Low' || interpretation.nitrogen.level === 'Very Low')) {
    recommendations.push({
      category: 'Nitrogen Management',
      description: 'Apply nitrogen-rich fertilizers or plant nitrogen-fixing legumes',
      priority: 'High',
      implementation: 'Apply before planting season or as side dressing'
    });
  }

  // Skip phosphorus and potassium since they're not available from SoilGrids v2.0
  // if (interpretation.phosphorus && (interpretation.phosphorus.level === 'Low' || interpretation.phosphorus.level === 'Very Low')) {
  //   recommendations.push({
  //     category: 'Phosphorus Management',
  //     description: 'Add phosphorus fertilizers to improve root development and flowering',
  //     priority: 'Medium',
  //     implementation: 'Apply during soil preparation'
  //   });
  // }

  if (interpretation.ph && interpretation.ph.level === 'Acidic') {
    recommendations.push({
      category: 'pH Management',
      description: 'Apply lime to raise soil pH and improve nutrient availability',
      priority: 'High',
      implementation: 'Apply 2-3 months before planting'
    });
  } else if (interpretation.ph && interpretation.ph.level === 'Alkaline') {
    recommendations.push({
      category: 'pH Management',
      description: 'Apply sulfur or organic matter to lower soil pH',
      priority: 'High',
      implementation: 'Apply during soil preparation'
    });
  }

  if (interpretation.organicMatter && (interpretation.organicMatter.level === 'Low' || interpretation.organicMatter.level === 'Very Low')) {
    recommendations.push({
      category: 'Organic Matter',
      description: 'Increase organic matter by adding compost, manure, or cover crops',
      priority: 'Medium',
      implementation: 'Apply annually during soil preparation'
    });
  }

  if (interpretation.moisture && (interpretation.moisture.level === 'Very Dry' || interpretation.moisture.level === 'Dry')) {
    recommendations.push({
      category: 'Water Management',
      description: 'Implement irrigation system or improve water retention with mulching',
      priority: 'High',
      implementation: 'Install before dry season'
    });
  } else if (interpretation.moisture && interpretation.moisture.level === 'Wet') {
    recommendations.push({
      category: 'Drainage',
      description: 'Improve drainage to prevent waterlogging and root diseases',
      priority: 'High',
      implementation: 'Install drainage systems before rainy season'
    });
  }

  if (recommendations.length === 0) {
    recommendations.push({
      category: 'General',
      description: 'Maintain current soil management practices and monitor regularly',
      priority: 'Low',
      implementation: 'Ongoing monitoring and standard practices'
    });
  }

  return recommendations;
}

function generateSuitableCrops(soilData, interpretation) {
  const crops = [
    { name: 'Wheat', baseScore: 75, phRange: [6.0, 7.5] },
    { name: 'Rice', baseScore: 80, phRange: [5.5, 7.0] },
    { name: 'Maize', baseScore: 70, phRange: [6.0, 7.5] },
    { name: 'Soybean', baseScore: 75, phRange: [6.0, 7.0] },
    { name: 'Cotton', baseScore: 65, phRange: [5.8, 8.0] },
    { name: 'Sugarcane', baseScore: 70, phRange: [6.0, 7.5] }
  ];

  return crops.map(crop => {
    let score = crop.baseScore;
    let reasons = [];

    // Adjust score based on pH (only if pH data is available)
    if (soilData.ph && crop.phRange) {
      if (soilData.ph >= crop.phRange[0] && soilData.ph <= crop.phRange[1]) {
        score += 10;
        reasons.push('Optimal pH range');
      } else {
        score -= 15;
        reasons.push('pH adjustment needed');
      }
    }

    // Adjust score based on nitrogen (only if nitrogen data is available)
    if (interpretation.nitrogen) {
      if (interpretation.nitrogen.level === 'High' || interpretation.nitrogen.level === 'Medium') {
        score += 5;
        reasons.push('Good nitrogen levels');
      } else {
        score -= 10;
        reasons.push('Nitrogen supplementation needed');
      }
    }

    // Adjust score based on organic matter (only if organic matter data is available)
    if (interpretation.organicMatter) {
      if (interpretation.organicMatter.level === 'High' || interpretation.organicMatter.level === 'Medium') {
        score += 5;
        reasons.push('Good organic matter');
      }
    }

    score = Math.max(0, Math.min(100, score));

    // Determine suitability based on score
    let suitability = 'Poor';
    if (score >= 80) suitability = 'Excellent';
    else if (score >= 65) suitability = 'Good';
    else if (score >= 50) suitability = 'Moderate';

    // Determine yield potential
    let yield = 'Low';
    if (score >= 80) yield = 'High';
    else if (score >= 65) yield = 'Medium';

    return {
      name: crop.name,
      suitability: suitability,
      reason: reasons.length > 0 ? reasons.join(', ') : 'Based on available soil data',
      yield: yield
    };
  }).sort((a, b) => {
    const scoreA = a.suitability === 'Excellent' ? 4 : a.suitability === 'Good' ? 3 : a.suitability === 'Moderate' ? 2 : 1;
    const scoreB = b.suitability === 'Excellent' ? 4 : b.suitability === 'Good' ? 3 : b.suitability === 'Moderate' ? 2 : 1;
    return scoreB - scoreA;
  });
}

// Keep existing PUT and DELETE routes
router.put('/soil-data/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const soilDataRecord = await SoilData.findOne({
      _id: id,
      createdBy: req.user.id
    });

    if (!soilDataRecord) {
      return res.status(404).json({
        success: false,
        message: 'Soil data record not found'
      });
    }

    Object.assign(soilDataRecord, updateData);
    soilDataRecord.updatedAt = new Date();

    if (updateData.soilData) {
      soilDataRecord.interpretation = generateInterpretations(soilDataRecord.soilData);
      soilDataRecord.recommendations = generateRecommendations(soilDataRecord.soilData, soilDataRecord.interpretation);
      soilDataRecord.suitableCrops = generateSuitableCrops(soilDataRecord.soilData, soilDataRecord.interpretation);
    }

    await soilDataRecord.save();

    res.status(200).json({
      success: true,
      message: 'Soil data updated successfully',
      data: soilDataRecord
    });

  } catch (error) {
    console.error('Error updating soil data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update soil data',
      error: error.message
    });
  }
});

router.delete('/soil-data/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    const soilDataRecord = await SoilData.findOneAndUpdate(
      { _id: id, createdBy: req.user.id },
      { isActive: false, updatedAt: new Date() },
      { new: true }
    );

    if (!soilDataRecord) {
      return res.status(404).json({
        success: false,
        message: 'Soil data record not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Soil data deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting soil data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete soil data',
      error: error.message
    });
  }
});

// Export the router
module.exports = router;

