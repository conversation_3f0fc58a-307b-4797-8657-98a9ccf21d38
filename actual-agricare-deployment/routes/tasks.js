const express = require('express');
const router = express.Router();
const Task = require('../models/Task');
const auth = require('../middleware/auth');

// Get all tasks for the territory manager
router.get('/', auth, async (req, res) => {
    try {
        const tasks = await Task.find({ territoryManager: req.user._id })
            .populate('relatedFarmers', 'name location')
            .sort({ due: 1 });
        res.json({ success: true, data: tasks });
    } catch (error) {
        console.error('Error fetching tasks:', error);
        res.status(500).json({ success: false, error: 'Failed to fetch tasks' });
    }
});

// Create a new task
router.post('/', auth, async (req, res) => {
    try {
        const task = new Task({
            ...req.body,
            territoryManager: req.user._id
        });
        await task.save();
        res.status(201).json({ success: true, data: task });
    } catch (error) {
        console.error('Error creating task:', error);
        res.status(400).json({ success: false, error: 'Failed to create task' });
    }
});

// Update a task
router.put('/:id', auth, async (req, res) => {
    try {
        const task = await Task.findOneAndUpdate(
            { _id: req.params.id, territoryManager: req.user._id },
            { ...req.body, updatedAt: Date.now() },
            { new: true }
        );
        if (!task) {
            return res.status(404).json({ success: false, error: 'Task not found' });
        }
        res.json({ success: true, data: task });
    } catch (error) {
        console.error('Error updating task:', error);
        res.status(400).json({ success: false, error: 'Failed to update task' });
    }
});

// Delete a task
router.delete('/:id', auth, async (req, res) => {
    try {
        const task = await Task.findOneAndDelete({
            _id: req.params.id,
            territoryManager: req.user._id
        });
        if (!task) {
            return res.status(404).json({ success: false, error: 'Task not found' });
        }
        res.json({ success: true, data: task });
    } catch (error) {
        console.error('Error deleting task:', error);
        res.status(500).json({ success: false, error: 'Failed to delete task' });
    }
});

// Toggle task status
router.patch('/:id/toggle', auth, async (req, res) => {
    try {
        const task = await Task.findOne({
            _id: req.params.id,
            territoryManager: req.user._id
        });
        if (!task) {
            return res.status(404).json({ success: false, error: 'Task not found' });
        }
        task.status = task.status === 'completed' ? 'pending' : 'completed';
        task.updatedAt = Date.now();
        await task.save();
        res.json({ success: true, data: task });
    } catch (error) {
        console.error('Error toggling task status:', error);
        res.status(400).json({ success: false, error: 'Failed to toggle task status' });
    }
});

module.exports = router; 