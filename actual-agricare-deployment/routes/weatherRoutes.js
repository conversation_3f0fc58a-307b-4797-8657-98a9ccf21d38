import express from 'express';
import {
  getCurrentWeather,
  getWeatherForecast,
  getWeatherBulletin
} from '../controllers/weatherController.js';

const router = express.Router();

const { weatherController } = require('../controllers/weatherDataController');

// Get current weather by farmer ID (path parameter)
router.get('/current/:farmerId', (req, res, next) => {
  req.query.farmerId = req.params.farmerId;
  getCurrentWeather(req, res, next);
});

// Get current weather by farmer ID (query parameter)
router.get('/current', getCurrentWeather);

// Get weather forecast by farmer ID (path parameter)
router.get('/forecast/:farmerId', (req, res, next) => {
  req.query.farmerId = req.params.farmerId;
  getWeatherForecast(req, res, next);
});

// Get weather forecast by farmer ID (query parameter)
router.get('/forecast', getWeatherForecast);

// Get weather bulletin by farmer ID (path parameter)
router.get('/bulletin/:farmerId', (req, res, next) => {
  req.query.farmerId = req.params.farmerId;
  getWeatherBulletin(req, res, next);
});

// Get weather bulletin by farmer ID (query parameter)
router.get('/bulletin', getWeatherBulletin);

// Get personalized weather for farmer by ID (query parameter)
router.get('/by-farmer', (req, res, next) => {
  const farmerId = req.query.farmerId;
  if (!farmerId) {
    return res.status(400).json({ success: false, error: 'Farmer ID is required' });
  }
  getCurrentWeather(req, res, next);
});

export default router;

