const mongoose = require('mongoose');
const Farm = require('../models/Farm');
require('dotenv').config();

const createTestFarm = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/agricare');
    console.log('Connected to MongoDB');

    const testFarm = {
      farmerId: '67ea3c0701918d4c7822366b',
      farmerName: 'Test Farmer',
      state: 'Karnataka',
      district: 'Bangalore',
      location: {
        type: 'Point',
        coordinates: [77.5946, 12.9716] // Bangalore coordinates
      },
      farmSize: '5 acres',
      cropType: 'Wheat',
      irrigationStatus: 'Well'
    };

    const existingFarm = await Farm.findOne({ farmerId: testFarm.farmerId });
    
    if (existingFarm) {
      console.log('Updating existing farm...');
      const result = await Farm.updateOne(
        { farmerId: testFarm.farmerId },
        { 
          $set: {
            state: testFarm.state,
            district: testFarm.district,
            location: testFarm.location
          }
        }
      );
      console.log('Farm updated successfully:', result);
    } else {
      await Farm.create(testFarm);
      console.log('Test farm created successfully');
    }

    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error('Error:', error);
  }
};

createTestFarm(); 