/**
 * Health Check Script for AgriCare
 * 
 * This script checks the health of the MongoDB connection and verifies
 * that essential collections exist and contain data.
 * 
 * Usage: node health-check.js
 */

require('dotenv').config();
const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
  console.error('❌ MONGODB_URI environment variable is not set');
  process.exit(1);
}

// Essential collections that should exist and have data
const ESSENTIAL_COLLECTIONS = [
  'users',
  'farms',
  'farmers',
  'livestock',
  'crops',
  'weather'
];

// Connect to MongoDB
async function connectToMongoDB() {
  try {
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      retryWrites: false
    });
    console.log('✅ Connected to MongoDB');
    return true;
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error.message);
    return false;
  }
}

// Check if collections exist and have data
async function checkCollections() {
  try {
    console.log('🔄 Checking collections...');
    
    const collections = await mongoose.connection.db.listCollections().toArray();
    const collectionNames = collections.map(c => c.name);
    
    console.log(`📊 Found ${collections.length} collections: ${collectionNames.join(', ')}`);
    
    const results = {
      total: collections.length,
      essential: 0,
      missing: [],
      empty: []
    };
    
    // Check each essential collection
    for (const collection of ESSENTIAL_COLLECTIONS) {
      if (collectionNames.includes(collection)) {
        results.essential++;
        
        // Check if collection has data
        const count = await mongoose.connection.db.collection(collection).countDocuments();
        console.log(`📊 Collection '${collection}' has ${count} documents`);
        
        if (count === 0) {
          results.empty.push(collection);
        }
      } else {
        results.missing.push(collection);
      }
    }
    
    return results;
  } catch (error) {
    console.error('❌ Error checking collections:', error.message);
    return {
      error: error.message,
      total: 0,
      essential: 0,
      missing: ESSENTIAL_COLLECTIONS,
      empty: []
    };
  }
}

// Generate a health report
async function generateHealthReport(collectionResults) {
  const report = {
    timestamp: new Date().toISOString(),
    mongodb: {
      connection: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',
      database: mongoose.connection.name,
      collections: {
        total: collectionResults.total,
        essential: {
          expected: ESSENTIAL_COLLECTIONS.length,
          found: collectionResults.essential,
          missing: collectionResults.missing,
          empty: collectionResults.empty
        }
      }
    },
    system: {
      node: process.version,
      platform: process.platform,
      memory: process.memoryUsage(),
      uptime: process.uptime()
    }
  };
  
  // Determine overall health status
  if (mongoose.connection.readyState !== 1) {
    report.status = 'critical';
    report.message = 'MongoDB connection failed';
  } else if (collectionResults.missing.length > 0) {
    report.status = 'warning';
    report.message = `Missing essential collections: ${collectionResults.missing.join(', ')}`;
  } else if (collectionResults.empty.length > 0) {
    report.status = 'warning';
    report.message = `Empty essential collections: ${collectionResults.empty.join(', ')}`;
  } else {
    report.status = 'healthy';
    report.message = 'All systems operational';
  }
  
  // Save report to file
  const reportDir = path.join(__dirname, '../logs');
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }
  
  const reportPath = path.join(reportDir, `health-report-${new Date().toISOString().replace(/:/g, '-')}.json`);
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  console.log(`📝 Health report saved to ${reportPath}`);
  
  return report;
}

// Main function
async function main() {
  try {
    const connected = await connectToMongoDB();
    if (!connected) {
      process.exit(1);
    }
    
    const collectionResults = await checkCollections();
    const report = await generateHealthReport(collectionResults);
    
    console.log('\n📊 Health Report Summary:');
    console.log(`Status: ${report.status}`);
    console.log(`Message: ${report.message}`);
    console.log(`Essential Collections: ${report.mongodb.collections.essential.found}/${report.mongodb.collections.essential.expected}`);
    
    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('✅ Disconnected from MongoDB');
    
    // Exit with appropriate code
    if (report.status === 'critical') {
      process.exit(1);
    } else if (report.status === 'warning') {
      process.exit(2);
    } else {
      process.exit(0);
    }
  } catch (error) {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
  }
}

// Run the main function
main();
