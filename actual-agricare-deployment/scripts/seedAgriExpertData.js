const mongoose = require('mongoose');
const { AgriExpert, AgriInstitution, ResearchInstitute } = require('../models/AgriExpert');
const User = require('../models/user.model');

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/agricare');
    console.log('✅ MongoDB Connected for seeding');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Sample data
const sampleExperts = [
  {
    name: 'Dr. <PERSON><PERSON>',
    designation: 'Senior Agricultural Scientist',
    specialization: 'Crop Management',
    email: '<EMAIL>',
    phone: '+91-**********',
    state: 'Punjab',
    district: 'Ludhiana',
    achievements: ['Best Researcher Award 2023', 'Published 50+ papers'],
    experience: 15,
    qualifications: [
      { degree: 'Ph.D. in Agriculture', institution: 'Punjab Agricultural University', year: 2008 }
    ],
    languages: ['English', 'Hindi', 'Punjabi'],
    status: 'Active'
  },
  {
    name: 'Dr. <PERSON><PERSON>',
    designation: 'Soil Health Expert',
    specialization: 'Soil Management',
    email: '<EMAIL>',
    phone: '+91-**********',
    state: 'Haryana',
    district: 'Karnal',
    achievements: ['Soil Health Pioneer', 'National Award Winner'],
    experience: 12,
    qualifications: [
      { degree: 'Ph.D. in Soil Science', institution: 'Indian Agricultural Research Institute', year: 2011 }
    ],
    languages: ['English', 'Hindi'],
    status: 'Active'
  },
  {
    name: 'Dr. Amit Patel',
    designation: 'Plant Pathologist',
    specialization: 'Plant Disease Management',
    email: '<EMAIL>',
    phone: '+91-**********',
    state: 'Gujarat',
    district: 'Anand',
    achievements: ['Disease Control Expert', 'Research Excellence Award'],
    experience: 10,
    qualifications: [
      { degree: 'Ph.D. in Plant Pathology', institution: 'Anand Agricultural University', year: 2013 }
    ],
    languages: ['English', 'Hindi', 'Gujarati'],
    status: 'Active'
  }
];

const sampleInstitutions = [
  {
    name: 'Punjab Agricultural University',
    type: 'University',
    location: 'Ludhiana, Punjab',
    state: 'Punjab',
    district: 'Ludhiana',
    established: '1962',
    departments: ['Agronomy', 'Plant Breeding', 'Soil Science', 'Horticulture'],
    researchAreas: ['Crop Improvement', 'Sustainable Agriculture', 'Precision Farming'],
    contact: {
      phone: '+91-161-2401960',
      email: '<EMAIL>',
      website: 'www.pau.edu',
      address: 'Ludhiana, Punjab 141004'
    },
    accreditation: [
      { body: 'NAAC', grade: 'A++', year: 2020 }
    ],
    facilities: ['Research Labs', 'Experimental Fields', 'Library', 'Hostels'],
    studentCapacity: 5000,
    facultyCount: 300,
    status: 'Active'
  },
  {
    name: 'Indian Agricultural Research Institute',
    type: 'Research Center',
    location: 'New Delhi',
    state: 'Delhi',
    district: 'New Delhi',
    established: '1905',
    departments: ['Genetics', 'Plant Pathology', 'Entomology', 'Agronomy'],
    researchAreas: ['Crop Genetics', 'Biotechnology', 'Climate Change'],
    contact: {
      phone: '+91-11-********',
      email: '<EMAIL>',
      website: 'www.iari.res.in',
      address: 'Pusa, New Delhi 110012'
    },
    accreditation: [
      { body: 'ICAR', grade: 'A+', year: 2021 }
    ],
    facilities: ['Advanced Labs', 'Greenhouses', 'Gene Bank', 'Museum'],
    studentCapacity: 1000,
    facultyCount: 200,
    status: 'Active'
  }
];

const sampleResearchInstitutes = [
  {
    name: 'Central Rice Research Institute',
    location: 'Cuttack, Odisha',
    state: 'Odisha',
    district: 'Cuttack',
    established: '1946',
    focus: ['Rice Research', 'Crop Improvement', 'Sustainable Agriculture'],
    parentOrganization: 'Indian Council of Agricultural Research (ICAR)',
    contact: {
      phone: '+91-671-2367757',
      email: '<EMAIL>',
      website: 'www.crri.nic.in',
      address: 'Cuttack, Odisha 753006'
    },

    status: 'Active'
  }
];

// Seed function
const seedData = async () => {
  try {
    await connectDB();

    // Create a test TM user
    let testUser = await User.findOne({ phoneNumber: 'tm_test_user' });
    if (!testUser) {
      testUser = await User.create({
        firebaseUid: 'test_tm_uid_123',
        phoneNumber: 'tm_test_user',
        name: 'Test TM User',
        email: '<EMAIL>',
        role: 'TM'
      });
      console.log('✅ Test TM user created');
    }

    // Clear existing data
    await AgriExpert.deleteMany({});
    await AgriInstitution.deleteMany({});
    await ResearchInstitute.deleteMany({});
    console.log('🗑️ Cleared existing data');

    // Add sample experts
    const expertsWithUser = sampleExperts.map(expert => ({
      ...expert,
      createdBy: testUser._id
    }));
    await AgriExpert.insertMany(expertsWithUser);
    console.log(`✅ Added ${sampleExperts.length} experts`);

    // Add sample institutions
    const institutionsWithUser = sampleInstitutions.map(institution => ({
      ...institution,
      createdBy: testUser._id
    }));
    await AgriInstitution.insertMany(institutionsWithUser);
    console.log(`✅ Added ${sampleInstitutions.length} institutions`);

    // Add sample research institutes
    const researchInstitutesWithUser = sampleResearchInstitutes.map(institute => ({
      ...institute,
      createdBy: testUser._id
    }));
    await ResearchInstitute.insertMany(researchInstitutesWithUser);
    console.log(`✅ Added ${sampleResearchInstitutes.length} research institutes`);

    console.log('🎉 Sample data seeded successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error seeding data:', error);
    process.exit(1);
  }
};

// Run the seed function
seedData();
