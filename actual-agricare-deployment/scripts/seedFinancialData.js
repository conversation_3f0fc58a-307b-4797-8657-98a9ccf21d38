const mongoose = require('mongoose');
const FarmFinancial = require('../models/FarmFinancial');
const User = require('../models/User');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Connect to MongoDB
const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/agricare';
console.log('Connecting to MongoDB at:', mongoUri);

mongoose.connect(mongoUri, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => console.log('MongoDB Connected'))
.catch(err => console.log(err));

// Generate mock financial data for a farmer
const generateMockFinancialData = (farmerId) => {
  const currentYear = new Date().getFullYear();

  return {
    farmerId,
    creditScore: {
      score: 720,
      scoreCategory: 'Good',
      lastUpdated: new Date('2024-05-15')
    },
    loans: [
      {
        type: 'Crop Loan',
        amount: 200000,
        remainingAmount: 120000,
        interestRate: 7.5,
        startDate: new Date('2023-06-10'),
        endDate: new Date('2026-06-10'),
        status: 'Active',
        bankName: 'State Bank of India',
        emiAmount: 6500,
        emiStatus: 'On Time',
        purpose: 'Wheat cultivation',
        collateral: 'Land (3 acres)',
        guarantor: 'Rajesh Kumar (Brother)',
        loanOfficer: 'Mr. Sharma',
        branchCode: 'SBI-AMR-1205'
      },
      {
        type: 'Equipment Loan',
        amount: 500000,
        remainingAmount: 350000,
        interestRate: 8.2,
        startDate: new Date('2022-03-15'),
        endDate: new Date('2027-03-15'),
        status: 'Active',
        bankName: 'Punjab National Bank',
        emiAmount: 12000,
        emiStatus: 'Delayed',
        purpose: 'Tractor purchase',
        collateral: 'Existing equipment',
        guarantor: 'Suresh Singh (Father)',
        loanOfficer: 'Mr. Patel',
        branchCode: 'PNB-AMR-0305'
      },
      {
        type: 'Kisan Credit Card',
        amount: 150000,
        remainingAmount: 75000,
        interestRate: 4.0,
        startDate: new Date('2024-01-10'),
        endDate: new Date('2025-01-10'),
        status: 'Active',
        bankName: 'NABARD',
        emiAmount: 0,
        emiStatus: 'Revolving Credit',
        purpose: 'Seasonal expenses',
        collateral: 'None',
        guarantor: 'None',
        loanOfficer: 'Ms. Gupta',
        branchCode: 'NABARD-AMR-0112'
      }
    ],
    assets: {
      land: [
        {
          area: 5.5,
          unit: 'Acres',
          location: 'Amritsar, Punjab',
          value: 2500000,
          cropType: 'Wheat',
          ownership: 'Owned',
          documentNumber: 'AMR-LD-2345-A',
          purchaseYear: 2015,
          irrigationSource: 'Canal',
          soilType: 'Alluvial'
        },
        {
          area: 3.2,
          unit: 'Acres',
          location: 'Amritsar, Punjab',
          value: 1500000,
          cropType: 'Rice',
          ownership: 'Leased',
          documentNumber: 'AMR-LD-3456-B',
          leaseExpiryDate: new Date('2026-05-30'),
          irrigationSource: 'Tube Well',
          soilType: 'Clay Loam'
        },
        {
          area: 2.8,
          unit: 'Acres',
          location: 'Tarn Taran, Punjab',
          value: 1200000,
          cropType: 'Sugarcane',
          ownership: 'Owned',
          documentNumber: 'TT-LD-4567-C',
          purchaseYear: 2018,
          irrigationSource: 'Drip Irrigation',
          soilType: 'Sandy Loam'
        }
      ],
      equipment: [
        {
          name: 'Tractor',
          model: 'Mahindra 575 DI',
          purchaseYear: 2022,
          value: 650000,
          registrationNumber: 'PB-02-AB-1234',
          insuranceExpiryDate: new Date('2025-03-15'),
          maintenanceStatus: 'Good',
          fuelType: 'Diesel'
        },
        {
          name: 'Harvester',
          model: 'John Deere X350R',
          purchaseYear: 2023,
          value: 850000,
          registrationNumber: 'PB-02-CD-5678',
          insuranceExpiryDate: new Date('2025-06-20'),
          maintenanceStatus: 'Excellent',
          fuelType: 'Diesel'
        },
        {
          name: 'Rotavator',
          model: 'Sonalika 42 Blade',
          purchaseYear: 2023,
          value: 120000,
          registrationNumber: 'N/A',
          insuranceExpiryDate: new Date('2025-04-10'),
          maintenanceStatus: 'Good',
          fuelType: 'N/A'
        },
        {
          name: 'Sprayer',
          model: 'HTP 4-Stroke',
          purchaseYear: 2024,
          value: 35000,
          registrationNumber: 'N/A',
          insuranceExpiryDate: null,
          maintenanceStatus: 'Excellent',
          fuelType: 'Petrol'
        }
      ],
      livestock: [
        {
          type: 'Cow',
          breed: 'Holstein',
          count: 5,
          value: 250000,
          purchaseYear: 2022,
          insuranceStatus: 'Insured',
          healthStatus: 'Healthy'
        },
        {
          type: 'Buffalo',
          breed: 'Murrah',
          count: 3,
          value: 180000,
          purchaseYear: 2023,
          insuranceStatus: 'Insured',
          healthStatus: 'Healthy'
        }
      ],
      structures: [
        {
          type: 'Storage Silo',
          capacity: '50 tons',
          value: 120000,
          constructionYear: 2021,
          condition: 'Good'
        },
        {
          type: 'Barn',
          capacity: '200 sq meters',
          value: 350000,
          constructionYear: 2020,
          condition: 'Good'
        }
      ]
    },
    yieldHistory: [
      {
        year: 2022,
        crop: 'Wheat',
        area: 5.5,
        production: 22000,
        unit: 'kg',
        revenue: 440000,
        expenses: 180000,
        profit: 260000,
        yieldPerAcre: 4000,
        marketPrice: 20,
        season: 'Rabi',
        recordDate: new Date('2022-04-15')
      },
      {
        year: 2023,
        crop: 'Wheat',
        area: 5.5,
        production: 24000,
        unit: 'kg',
        revenue: 504000,
        expenses: 195000,
        profit: 309000,
        yieldPerAcre: 4364,
        marketPrice: 21,
        season: 'Rabi',
        recordDate: new Date('2023-04-20')
      },
      {
        year: 2023,
        crop: 'Rice',
        area: 3.2,
        production: 12800,
        unit: 'kg',
        revenue: 384000,
        expenses: 150000,
        profit: 234000,
        yieldPerAcre: 4000,
        marketPrice: 30,
        season: 'Kharif',
        recordDate: new Date('2023-11-10')
      },
      {
        year: 2024,
        crop: 'Rice',
        area: 3.2,
        production: 13500,
        unit: 'kg',
        revenue: 432000,
        expenses: 160000,
        profit: 272000,
        yieldPerAcre: 4219,
        marketPrice: 32,
        season: 'Kharif',
        recordDate: new Date('2024-11-05')
      }
    ],
    paymentHistory: [
      {
        date: new Date('2024-04-15'),
        amount: 6500,
        loanId: 'L001',
        status: 'Paid',
        dueDate: new Date('2024-04-15'),
        transactionId: 'TXN-45678',
        paymentMethod: 'Bank Transfer'
      },
      {
        date: new Date('2024-03-15'),
        amount: 6500,
        loanId: 'L001',
        status: 'Paid',
        dueDate: new Date('2024-03-15'),
        transactionId: 'TXN-34567',
        paymentMethod: 'Bank Transfer'
      },
      {
        date: new Date('2024-04-25'),
        amount: 12000,
        loanId: 'L002',
        status: 'Delayed',
        dueDate: new Date('2024-04-15'),
        paidDate: new Date('2024-04-25'),
        transactionId: 'TXN-56789',
        paymentMethod: 'Cash',
        delayReason: 'Market payment delay'
      },
      {
        date: new Date('2024-05-15'),
        amount: 6500,
        loanId: 'L001',
        status: 'Upcoming',
        dueDate: new Date('2024-05-15')
      }
    ],
    creditHistory: {
      cibilScore: 750,
      lastCibilUpdate: new Date('2024-04-01'),
      creditUtilization: 65,
      accountAgeYears: 5,
      inquiriesLast6Months: 1,
      delinquencies: 0,
      creditMix: 'Good',
      paymentHistoryRating: 'Excellent',
      remarks: 'Good repayment history with minimal delays'
    },
    insuranceDetails: [
      {
        type: 'Crop Insurance',
        provider: 'Agriculture Insurance Company',
        coverageAmount: 300000,
        premium: 15000,
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
        crops: ['Wheat', 'Rice'],
        policyNumber: 'AIC-CR-12345',
        status: 'Active'
      },
      {
        type: 'Equipment Insurance',
        provider: 'National Insurance',
        coverageAmount: 500000,
        premium: 25000,
        startDate: new Date('2024-02-15'),
        endDate: new Date('2025-02-14'),
        equipment: ['Tractor', 'Harvester'],
        policyNumber: 'NI-EQ-67890',
        status: 'Active'
      },
      {
        type: 'Livestock Insurance',
        provider: 'United India Insurance',
        coverageAmount: 200000,
        premium: 10000,
        startDate: new Date('2024-03-10'),
        endDate: new Date('2025-03-09'),
        livestock: ['Cow', 'Buffalo'],
        policyNumber: 'UII-LS-54321',
        status: 'Active'
      }
    ],
    subsidies: [
      {
        scheme: 'PM-KISAN',
        amount: 6000,
        frequency: 'Annual',
        lastReceived: new Date('2024-02-10'),
        nextExpected: new Date('2025-02-10'),
        status: 'Active',
        registrationNumber: 'PMKS-12345-6789'
      },
      {
        scheme: 'Fertilizer Subsidy',
        amount: 8500,
        frequency: 'Seasonal',
        lastReceived: new Date('2024-03-15'),
        nextExpected: new Date('2024-09-15'),
        status: 'Active',
        registrationNumber: 'FERT-45678-9012'
      }
    ],
    bankAccounts: [
      {
        bankName: 'State Bank of India',
        accountType: 'Savings',
        accountNumber: 'XXXX5678',
        branch: 'Amritsar Main',
        ifscCode: 'SBIN0001234',
        balance: 85000,
        lastTransaction: new Date('2024-05-10')
      },
      {
        bankName: 'Punjab National Bank',
        accountType: 'Current',
        accountNumber: 'XXXX9012',
        branch: 'Amritsar Agricultural Branch',
        ifscCode: 'PUNB0056789',
        balance: 120000,
        lastTransaction: new Date('2024-05-12')
      }
    ]
  };
};

// Seed the database with mock financial data
const seedDatabase = async () => {
  try {
    // Clear existing financial data
    await FarmFinancial.deleteMany({});
    console.log('Cleared existing financial data');

    // Get all farmers
    let farmers = [];
    try {
      farmers = await User.find({ role: 'farmer' });
    } catch (error) {
      console.log('Error finding farmers:', error.message);
    }

    if (farmers.length === 0) {
      console.log('No farmers found. Creating a mock farmer for demonstration.');

      // Create a mock farmer with a fixed ID for demonstration
      farmers = [{
        _id: '64f5e5e5e5e5e5e5e5e5e5e5',
        name: 'Mock Farmer',
        email: '<EMAIL>',
        role: 'farmer'
      }];
    }

    console.log(`Found ${farmers.length} farmers. Generating financial data...`);

    // Create financial data for each farmer
    const financialDataPromises = farmers.map(async (farmer) => {
      const mockData = generateMockFinancialData(farmer._id);
      return FarmFinancial.create(mockData);
    });

    await Promise.all(financialDataPromises);

    console.log(`Successfully seeded financial data for ${farmers.length} farmers`);
    process.exit(0);
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
};

// Run the seed function
seedDatabase();
