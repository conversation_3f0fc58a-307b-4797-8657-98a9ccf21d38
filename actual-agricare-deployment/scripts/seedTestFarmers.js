const mongoose = require('mongoose');
const Farmer = require('../models/Farmer');
const Farm = require('../models/Farm');
require('dotenv').config();

async function seedTestFarmers() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/agricare');
    console.log('Connected to MongoDB');

    // IDs used in the frontend
    const testFarmerIds = [
      '67dbeb95072698d242a3f415', // Default ID used in Weather.jsx
      '67ea3c0701918d4c7822366b'  // ID from error logs
    ];

    for (const id of testFarmerIds) {
      // Check if farmer already exists
      const existingFarmer = await Farmer.findById(id);
      if (existingFarmer) {
        console.log(`Farmer with ID ${id} already exists`);
        continue;
      }

      // Create farmer with specific ID
      const farmer = new Farmer({
        _id: id,
        name: `Test Farmer ${id.substring(0, 6)}`,
        mobile: `9${Math.floor(Math.random() * 1000000000)}`,
        aadharNumber: `${Math.floor(Math.random() * 1000000000000)}`,
        panNumber: `ABCDE${Math.floor(Math.random() * 10000)}F`,
        state: 'Karnataka',
        district: 'Bangalore Rural',
        farmSize: '5 acres',
        cropType: 'Mixed',
        irrigationStatus: 'Well',
        geoLocation: {
          type: 'Point',
          coordinates: [77.5946, 12.9716] // Bangalore coordinates
        }
      });

      await farmer.save();
      console.log(`Created test farmer with ID: ${id}`);

      // Create farm for this farmer
      const farm = await Farm.create({
        farmerId: farmer._id,
        farmerName: farmer.name,
        state: farmer.state,
        district: farmer.district,
        location: {
          type: 'Point',
          coordinates: farmer.geoLocation.coordinates
        },
        farmSize: farmer.farmSize,
        cropType: farmer.cropType,
        irrigationStatus: farmer.irrigationStatus
      });
      console.log(`Created farm for farmer ${id}:`, farm._id);
    }

    console.log('Test farmers seeding completed');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding test farmers:', error);
    process.exit(1);
  }
}

seedTestFarmers();
