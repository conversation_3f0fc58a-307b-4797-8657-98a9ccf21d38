const axios = require('axios');

class SatelliteDataService {
  constructor() {
    this.soilGridsEndpoint = 'https://rest.isric.org/soilgrids/v2.0/properties/query';
    this.nasaPowerEndpoint = 'https://power.larc.nasa.gov/api/temporal/daily/point';
    this.openWeatherEndpoint = 'https://api.openweathermap.org/data/2.5/weather';

    // Simple in-memory cache to avoid repeated API calls
    this.cache = new Map();
    this.cacheTimeout = 30 * 60 * 1000; // 30 minutes

    this.regionCoordinates = {
      'Madhya Pradesh': { lat: 22.9734, lng: 78.6569 },
      'Punjab': { lat: 30.7415, lng: 76.7754 },
      'Haryana': { lat: 29.0588, lng: 76.0856 },
      'Uttar Pradesh': { lat: 26.8467, lng: 80.9462 },
      'Tamil Nadu': { lat: 11.1271, lng: 78.6569 },
      'Karnataka': { lat: 15.3173, lng: 75.7139 },
      'Kerala': { lat: 10.8505, lng: 76.2711 },
      'West Bengal': { lat: 22.9868, lng: 87.8550 },
      'Gujarat': { lat: 22.2587, lng: 71.1924 },
      'Maharashtra': { lat: 19.7515, lng: 75.7139 }
    };
  }

  _getCacheKey(lat, lng, type) {
    return `${type}_${lat.toFixed(4)}_${lng.toFixed(4)}`;
  }

  _getCachedData(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    return null;
  }

  _setCachedData(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  async fetchSoilData(lat, lng) {
    try {
      // Check cache first
      const cacheKey = this._getCacheKey(lat, lng, 'soil');
      const cachedData = this._getCachedData(cacheKey);
      if (cachedData) {
        console.log('🔍 Using cached soil data');
        return cachedData;
      }

      // Fetch data from multiple APIs in parallel
      const [soilGridsData, nasaData, realTimeWeather] = await Promise.all([
        this._getSoilGridsData(lat, lng),
        this._getNasaData(lat, lng),
        this._getRealTimeWeatherData(lat, lng)
      ]);

      // Combine and process the data
      // Note: SoilGrids v2.0 doesn't provide phosphorus and potassium data
      const result = {
        // From SoilGrids (real data)
        nitrogen: soilGridsData.nitrogen || null,
        ph: soilGridsData.ph || null,
        organicMatter: soilGridsData.organicMatter || null,
        sand: soilGridsData.sand || null,
        clay: soilGridsData.clay || null,
        silt: soilGridsData.silt || null,

        // From Real-time Weather API (if available) or NASA POWER (historical data)
        moisture: realTimeWeather?.moisture || nasaData.moisture || null,
        temperature: realTimeWeather?.temperature || nasaData.temperature || null,
        precipitation: realTimeWeather?.precipitation || nasaData.precipitation || null,
        humidity: realTimeWeather?.humidity || nasaData.humidity || null,
        soilTemperature: nasaData.soilTemperature || null, // NASA POWER only

        // Additional real-time weather info
        weatherCondition: realTimeWeather?.weatherCondition || null,
        moistureLevel: realTimeWeather?.moistureLevel || null,
        isRealTimeWeather: realTimeWeather?.isRealTime || false,

        // Not available from current APIs - set to null
        phosphorus: null,
        potassium: null,

        lastUpdated: new Date().toISOString(),
        dataSource: realTimeWeather?.isRealTime
          ? 'SoilGrids, NASA POWER & Real-time Weather APIs'
          : 'NASA POWER & SoilGrids APIs (Real Data)'
      };

      // Cache the result
      this._setCachedData(cacheKey, result);

      return result;
    } catch (error) {
      console.error('Error fetching satellite data:', error);
      throw new Error(`Satellite data fetch failed: ${error.message}`);
    }
  }

  async _getSoilGridsData(lat, lng) {
    try {
      // Priority order: get the most important properties first
      const priorityProperties = ['phh2o', 'nitrogen', 'soc']; // Most important
      const secondaryProperties = ['sand', 'clay']; // Secondary (silt can be calculated)

      const soilData = {};

      // Fetch priority properties first (these are most critical for analysis)
      for (const property of priorityProperties) {
        try {
          const response = await axios.get(this.soilGridsEndpoint, {
            params: {
              lat,
              lon: lng,
              property: property,
              depth: '0-5cm',
              value: 'mean'
            },
            timeout: 10000
          });

          if (response.data?.properties?.layers?.[0]?.depths?.[0]?.values?.mean) {
            const value = response.data.properties.layers[0].depths[0].values.mean;

            switch (property) {
              case 'phh2o':
                soilData.ph = value / 10; // Convert from pH*10 to pH
                break;
              case 'nitrogen':
                soilData.nitrogen = value / 100; // Convert from cg/kg to g/kg
                break;
              case 'soc':
                soilData.organicMatter = value / 10; // Convert from dg/kg to g/kg
                break;
            }
          }

          // Small delay to respect rate limits
          await new Promise(resolve => setTimeout(resolve, 1000));

        } catch (propertyError) {
          console.warn(`Failed to fetch ${property} from SoilGrids:`, propertyError.message);
          if (propertyError.response?.status === 429) {
            console.warn(`Rate limited for ${property}, skipping secondary properties`);
            return soilData; // Return what we have so far
          }
        }
      }

      // Try to fetch secondary properties if we haven't hit rate limits
      for (const property of secondaryProperties) {
        try {
          const response = await axios.get(this.soilGridsEndpoint, {
            params: {
              lat,
              lon: lng,
              property: property,
              depth: '0-5cm',
              value: 'mean'
            },
            timeout: 8000 // Shorter timeout for secondary properties
          });

          if (response.data?.properties?.layers?.[0]?.depths?.[0]?.values?.mean) {
            const value = response.data.properties.layers[0].depths[0].values.mean;

            switch (property) {
              case 'sand':
                soilData.sand = value / 10; // Convert from g/kg*10 to %
                break;
              case 'clay':
                soilData.clay = value / 10; // Convert from g/kg*10 to %
                break;
            }
          }

          // Small delay between requests
          await new Promise(resolve => setTimeout(resolve, 1000));

        } catch (propertyError) {
          console.warn(`Failed to fetch ${property} from SoilGrids:`, propertyError.message);
          if (propertyError.response?.status === 429) {
            console.warn(`Rate limited for ${property}, stopping further requests`);
            break; // Stop trying more properties
          }
        }
      }

      // Calculate silt if we have sand and clay
      if (soilData.sand && soilData.clay) {
        soilData.silt = Math.max(0, 100 - soilData.sand - soilData.clay);
      }

      return soilData;
    } catch (error) {
      console.error('SoilGrids API error:', error);
      throw new Error(`SoilGrids API error: ${error.message}`);
    }
  }

  async _getNasaData(lat, lng) {
    try {
      // Get data for the last 7 days
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 7);

      const response = await axios.get(this.nasaPowerEndpoint, {
        params: {
          parameters: 'PRECTOTCORR,RH2M,T2M,TS',
          community: 'AG', // Required parameter for agricultural community
          latitude: lat,
          longitude: lng,
          start: startDate.toISOString().split('T')[0].replace(/-/g, ''),
          end: endDate.toISOString().split('T')[0].replace(/-/g, ''),
          format: 'JSON'
        },
        timeout: 10000
      });

      if (!response.data?.properties?.parameter) {
        throw new Error('Invalid response from NASA POWER API');
      }

      const params = response.data.properties.parameter;
      return {
        moisture: this._calculateAverage(params.PRECTOTCORR), // Precipitation as moisture indicator
        temperature: this._calculateAverage(params.T2M),
        precipitation: this._calculateAverage(params.PRECTOTCORR),
        humidity: this._calculateAverage(params.RH2M),
        soilTemperature: this._calculateAverage(params.TS)
      };
    } catch (error) {
      console.error('NASA POWER API error:', error);
      throw new Error(`NASA POWER API error: ${error.message}`);
    }
  }

  _calculateAverage(data) {
    if (!data) return null;
    const values = Object.values(data).filter(val => val !== -999.0);
    if (values.length === 0) return null;
    return values.reduce((acc, val) => acc + val, 0) / values.length;
  }

  async _getRealTimeWeatherData(lat, lng) {
    try {
      // Try to get real-time weather data from OpenWeatherMap (free tier)
      // Note: This requires an API key, but we'll provide fallback to NASA POWER
      const apiKey = process.env.OPENWEATHER_API_KEY;

      if (!apiKey) {
        console.log('No OpenWeatherMap API key found, using NASA POWER historical data');
        return null;
      }

      const response = await axios.get(this.openWeatherEndpoint, {
        params: {
          lat,
          lon: lng,
          appid: apiKey,
          units: 'metric'
        },
        timeout: 5000
      });

      if (response.data) {
        const weather = response.data;
        const currentConditions = weather.weather?.[0]?.main?.toLowerCase() || '';

        // Estimate soil moisture based on current weather conditions
        let moistureLevel = 'Moderate';
        let moistureValue = 25; // Default moderate moisture

        if (currentConditions.includes('rain') || currentConditions.includes('drizzle') || currentConditions.includes('thunderstorm')) {
          moistureLevel = 'Wet';
          moistureValue = 60;
        } else if (currentConditions.includes('snow')) {
          moistureLevel = 'Wet';
          moistureValue = 55;
        } else if (currentConditions.includes('clear') || currentConditions.includes('sun')) {
          moistureLevel = 'Dry';
          moistureValue = 15;
        } else if (currentConditions.includes('cloud')) {
          moistureLevel = 'Moderate';
          moistureValue = 30;
        }

        return {
          moisture: moistureValue,
          moistureLevel: moistureLevel,
          temperature: weather.main?.temp || null,
          humidity: weather.main?.humidity || null,
          precipitation: weather.rain?.['1h'] || weather.snow?.['1h'] || 0,
          weatherCondition: weather.weather?.[0]?.description || 'Unknown',
          isRealTime: true
        };
      }
    } catch (error) {
      console.warn('Failed to get real-time weather data:', error.message);
    }

    return null;
  }

  async getSoilNPKData(region, forceRefresh = false) {
    try {
      const coordinates = this.regionCoordinates[region];
      if (!coordinates) {
        throw new Error(`No coordinates available for region: ${region}`);
      }

      // Fetch data for the region's central coordinates
      const soilData = await this.fetchSoilData(coordinates.lat, coordinates.lng);
      
      return {
        region,
        ...soilData,
        lastUpdated: new Date().toISOString(),
        dataSource: 'NASA POWER & SoilGrids APIs'
      };
    } catch (error) {
      console.error('Error fetching soil NPK data:', error);
      // Return fallback data for the region
      return {
        region,
        nitrogen: 280,
        phosphorus: 45,
        potassium: 190,
        ph: 6.5,
        organicMatter: 3.2,
        moisture: 25,
        temperature: 28,
        precipitation: 0,
        humidity: 65,
        lastUpdated: new Date().toISOString(),
        dataSource: 'Fallback Data (Service Error)'
      };
    }
  }
}

module.exports = new SatelliteDataService();

