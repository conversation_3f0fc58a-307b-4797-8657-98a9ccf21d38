import axios from 'axios';

const SENTINEL_API_URL = 'https://services.sentinel-hub.com/ogc/wms/INSTANCE_ID';
const INSTANCE_ID = process.env.REACT_APP_SENTINEL_INSTANCE_ID;
const API_KEY = process.env.REACT_APP_SENTINEL_API_KEY;

export const fetchPlotImagery = async (coordinates, options = {}) => {
  try {
    const params = {
      service: 'WMS',
      request: 'GetMap',
      layers: 'TRUE_COLOR', // or your custom layer
      format: 'image/png',
      width: 512,
      height: 512,
      srs: 'EPSG:4326',
      bbox: `${coordinates.lng-0.01},${coordinates.lat-0.01},${coordinates.lng+0.01},${coordinates.lat+0.01}`,
      time: '2023-01-01/2023-12-31',
      maxcc: 20, // max cloud coverage
      ...options
    };

    const response = await axios.get(SENTINEL_API_URL, {
      params,
      headers: { 'Authorization': `Bearer ${API_KEY}` },
      responseType: 'arraybuffer'
    });

    return `data:image/png;base64,${Buffer.from(response.data, 'binary').toString('base64')}`;
  } catch (error) {
    console.error('Sentinel API Error:', error);
    throw new Error('Failed to fetch satellite imagery');
  }
};

