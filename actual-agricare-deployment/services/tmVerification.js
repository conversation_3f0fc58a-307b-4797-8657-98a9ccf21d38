const admin = require('firebase-admin');

const verifyTerritoryManager = async (phoneNumber) => {
    try {
        const db = admin.firestore();
        const tmRef = db.collection('territory_managers');
        
        // Check if the phone number exists in approved TMs
        const tmDoc = await tmRef.where('phoneNumber', '==', phoneNumber).get();
        
        if (tmDoc.empty) {
            return {
                isVerified: false,
                message: 'Phone number not registered as Territory Manager'
            };
        }

        const tmData = tmDoc.docs[0].data();
        
        // Check if TM is approved
        if (!tmData.isApproved) {
            return {
                isVerified: false,
                message: 'Territory Manager approval pending'
            };
        }

        // Check if TM is active
        if (!tmData.isActive) {
            return {
                isVerified: false,
                message: 'Territory Manager account is inactive'
            };
        }

        return {
            isVerified: true,
            tmData: {
                id: tmDoc.docs[0].id,
                name: tmData.name,
                region: tmData.region,
                phoneNumber: tmData.phoneNumber
            }
        };
    } catch (error) {
        console.error('Error verifying Territory Manager:', error);
        throw new Error('Failed to verify Territory Manager');
    }
};

module.exports = {
    verifyTerritoryManager
}; 