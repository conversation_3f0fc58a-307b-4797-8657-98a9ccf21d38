const axios = require('axios');
const mongoose = require('mongoose');
const NodeCache = require('node-cache');
const WeatherData = require('../models/WeatherData');
const Farm = require('../models/Farm');
const Farmer = require('../models/Farmer');

// Cache weather data for 2 minutes (reduced from 5 minutes)
const weatherCache = new NodeCache({ stdTTL: 120 });

// Helper function to convert UTC to IST and format as DD/MM/YYYY, hh:mm A
function formatIST(date) {
  try {
    const inputDate = date instanceof Date ? date : new Date(date);
    if (isNaN(inputDate.getTime())) {
      console.error('Invalid date provided:', date);
      return new Date().toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' });
    }

    return inputDate.toLocaleString('en-IN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
      timeZone: 'Asia/Kolkata'
    }).replace(',', ',');
  } catch (error) {
    console.error('Error formatting date:', error);
    return new Date().toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' });
  }
}

// Helper function to convert UTC timestamp to IST Date
function convertToIST(timestamp) {
  try {
    const date = new Date(timestamp * 1000);
    return new Date(date.toLocaleString('en-US', { timeZone: 'Asia/Kolkata' }));
  } catch (error) {
    console.error('Error converting timestamp to IST:', error);
    return new Date();
  }
}

// Helper function to format weather data consistently
function formatWeatherData(data) {
  return {
    temperature: parseFloat(data.main.temp.toFixed(1)),
    feelsLike: parseFloat(data.main.feels_like.toFixed(1)),
    humidity: Math.round(data.main.humidity),
    windSpeed: parseFloat((data.wind.speed).toFixed(2)), // Store in m/s, convert to km/h in frontend
    conditions: data.weather[0].description,
    icon: data.weather[0].icon,
    precipitation: data.rain ? parseFloat(data.rain['1h'] || data.rain['3h'] || 0).toFixed(1) : 0,
    pressure: data.main.pressure,
    visibility: data.visibility,
    cloudiness: data.clouds.all
  };
}

class WeatherService {
  constructor() {
    this.apiKey = process.env.OPENWEATHER_API_KEY || '********************************';
    this.baseUrl = 'https://api.openweathermap.org/data/2.5';
  }

  async getFarmerLocation(farmerId) {
    try {
      // Try to find by ID first
      let farmer = null;

      if (mongoose.Types.ObjectId.isValid(farmerId)) {
        farmer = await Farmer.findById(farmerId);
      }

      // If not found by ID, try by mobile number
      if (!farmer) {
        farmer = await Farmer.findOne({ mobile: farmerId });
      }

      // If still not found, try with a + prefix for international format
      if (!farmer && !farmerId.startsWith('+')) {
        farmer = await Farmer.findOne({ mobile: `+${farmerId}` });
      }

      if (!farmer) {
        console.error(`Farmer not found with ID/phone: ${farmerId}`);
        throw new Error(`Farmer not found with ID/phone: ${farmerId}`);
      }

      if (!farmer.geoLocation || !farmer.geoLocation.coordinates || farmer.geoLocation.coordinates.length !== 2) {
        console.error(`Invalid location data for farmer: ${farmer._id}`);

        // Use default coordinates for Bangalore as fallback
        return {
          latitude: 12.9716,
          longitude: 77.5946,
          isDefault: true
        };
      }

      return {
        latitude: farmer.geoLocation.coordinates[1],
        longitude: farmer.geoLocation.coordinates[0],
        isDefault: false
      };
    } catch (error) {
      console.error('Error getting farmer location:', error);

      // Use default coordinates for Bangalore as fallback
      return {
        latitude: 12.9716,
        longitude: 77.5946,
        isDefault: true
      };
    }
  }

  async getCurrentWeather(farmerId) {
    try {
      const cacheKey = `current_${farmerId}`;
      const cachedData = weatherCache.get(cacheKey);

      if (cachedData) {
        return cachedData;
      }

      const location = await this.getFarmerLocation(farmerId);

      // If we're using default location, add a note to the cache key
      const finalCacheKey = location.isDefault ? `${cacheKey}_default` : cacheKey;

      // Check if we have cached data for the default location
      if (location.isDefault) {
        const defaultCachedData = weatherCache.get(finalCacheKey);
        if (defaultCachedData) {
          return defaultCachedData;
        }
      }

      if (!this.apiKey) {
        throw new Error('OpenWeather API key is not configured');
      }

      const response = await axios.get(`${this.baseUrl}/weather`, {
        params: {
          lat: location.latitude,
          lon: location.longitude,
          appid: this.apiKey,
          units: 'metric'
        }
      });

      const currentTime = convertToIST(response.data.dt);
      const weatherData = {
        ...formatWeatherData(response.data),
        lastUpdated: formatIST(currentTime),
        sunrise: formatIST(convertToIST(response.data.sys.sunrise)),
        sunset: formatIST(convertToIST(response.data.sys.sunset)),
        timezone: 'Asia/Kolkata',
        rawTimestamp: response.data.dt
      };

      // Cache the data
      weatherCache.set(finalCacheKey, weatherData);

      // Save to database
      try {
        await WeatherData.create({
          farmerId,
          type: 'current',
          data: weatherData,
          timestamp: currentTime
        });
      } catch (dbError) {
        console.error('Error saving weather data to database:', dbError);
        // Continue even if database save fails
      }

      return weatherData;
    } catch (error) {
      console.error('Error fetching current weather:', error);
      weatherCache.del(finalCacheKey); // Clear cache on error

      // Return mock data as fallback
      const mockWeatherData = {
        temperature: 28.5,
        feelsLike: 30.2,
        humidity: 65,
        windSpeed: 3.5,
        conditions: 'scattered clouds',
        icon: '03d',
        precipitation: 0,
        pressure: 1012,
        visibility: 10000,
        cloudiness: 40,
        lastUpdated: formatIST(new Date()),
        sunrise: formatIST(new Date(new Date().setHours(6, 30, 0, 0))),
        sunset: formatIST(new Date(new Date().setHours(18, 30, 0, 0))),
        timezone: 'Asia/Kolkata',
        rawTimestamp: Math.floor(Date.now() / 1000),
        isMockData: true
      };

      return mockWeatherData;
    }
  }

  async getForecast(farmerId) {
    try {
      const cacheKey = `forecast_${farmerId}`;
      const cachedData = weatherCache.get(cacheKey);

      if (cachedData) {
        return cachedData;
      }

      const location = await this.getFarmerLocation(farmerId);

      // If we're using default location, add a note to the cache key
      const finalCacheKey = location.isDefault ? `${cacheKey}_default` : cacheKey;

      // Check if we have cached data for the default location
      if (location.isDefault) {
        const defaultCachedData = weatherCache.get(finalCacheKey);
        if (defaultCachedData) {
          return defaultCachedData;
        }
      }

      if (!this.apiKey) {
        throw new Error('OpenWeather API key is not configured');
      }

      const response = await axios.get(`${this.baseUrl}/forecast`, {
        params: {
          lat: location.latitude,
          lon: location.longitude,
          appid: this.apiKey,
          units: 'metric'
        }
      });

      // Group forecast data by day in IST
      const dailyForecasts = {};

      // Get current date in IST for consecutive day calculation
      const currentDate = new Date();
      const currentIST = new Date(currentDate.toLocaleString('en-US', { timeZone: 'Asia/Kolkata' }));

      response.data.list.forEach(item => {
        const forecastDate = convertToIST(item.dt);
        const date = formatIST(forecastDate).split(',')[0];

        if (!dailyForecasts[date]) {
          dailyForecasts[date] = {
            date,
            timestamp: forecastDate,
            temperature: {
              max: parseFloat(item.main.temp_max.toFixed(1)),
              min: parseFloat(item.main.temp_min.toFixed(1)),
              avg: parseFloat(item.main.temp.toFixed(1))
            },
            humidity: Math.round(item.main.humidity),
            windSpeed: parseFloat(item.wind.speed.toFixed(2)),
            conditions: item.weather[0].description,
            icon: item.weather[0].icon,
            precipitation: item.rain ? parseFloat(item.rain['3h'] || 0).toFixed(1) : 0,
            rawTimestamp: item.dt
          };
        } else {
          const forecast = dailyForecasts[date];
          forecast.temperature.max = Math.max(
            forecast.temperature.max,
            parseFloat(item.main.temp_max.toFixed(1))
          );
          forecast.temperature.min = Math.min(
            forecast.temperature.min,
            parseFloat(item.main.temp_min.toFixed(1))
          );
          forecast.temperature.avg = parseFloat(
            ((forecast.temperature.max + forecast.temperature.min) / 2).toFixed(1)
          );
          if (item.rain) {
            forecast.precipitation = (parseFloat(forecast.precipitation) +
              parseFloat(item.rain['3h'] || 0)).toFixed(1);
          }
        }
      });

      // Sort forecast data by timestamp to ensure chronological order
      const forecastData = Object.values(dailyForecasts)
        .sort((a, b) => a.timestamp - b.timestamp)
        .slice(0, 7)
        .map((day, index) => {
          // Create two different date objects
          // 1. For consecutive days (5-Day Forecast)
          const dailyForecastDay = new Date(currentIST);
          dailyForecastDay.setDate(currentIST.getDate() + index);

          // 2. For monthly forecast (Weather Forecast)
          const monthlyForecastDay = new Date(currentIST);
          monthlyForecastDay.setMonth(currentIST.getMonth() + index);
          monthlyForecastDay.setDate(currentIST.getDate()); // Keep the same day of month

          return {
            ...day,
            // Daily forecast date (for 5-Day Forecast)
            date: dailyForecastDay.toLocaleDateString('en-IN', {
              day: 'numeric',
              month: 'short',
              timeZone: 'Asia/Kolkata'
            }),
            // Monthly forecast date (for Weather Forecast)
            monthlyDate: monthlyForecastDay.toLocaleDateString('en-IN', {
              day: 'numeric',
              month: 'short',
              timeZone: 'Asia/Kolkata'
            }),
            // Store the actual date objects for frontend processing
            actualDate: dailyForecastDay,
            actualMonthlyDate: monthlyForecastDay,
            // Daily forecast day of week
            dayOfWeek: dailyForecastDay.toLocaleDateString('en-IN', {
              weekday: 'long',
              timeZone: 'Asia/Kolkata'
            }),
            // Monthly forecast day of week
            monthlyDayOfWeek: monthlyForecastDay.toLocaleDateString('en-IN', {
              weekday: 'long',
              timeZone: 'Asia/Kolkata'
            }),
            temperature: {
              ...day.temperature,
              avg: parseFloat(((day.temperature.max + day.temperature.min) / 2).toFixed(1))
            }
          };
        });

      // Cache the data
      weatherCache.set(finalCacheKey, forecastData);

      // Save to database
      try {
        await WeatherData.create({
          farmerId,
          type: 'forecast',
          data: { forecast: forecastData },
          timestamp: convertToIST(response.data.list[0].dt)
        });
      } catch (dbError) {
        console.error('Error saving forecast data to database:', dbError);
        // Continue even if database save fails
      }

      return forecastData;
    } catch (error) {
      console.error('Error fetching forecast:', error);
      weatherCache.del(finalCacheKey); // Clear cache on error

      // Return mock forecast data as fallback
      const currentDate = new Date();
      const mockForecastData = [];

      // Generate 7 days of mock forecast data
      for (let i = 0; i < 7; i++) {
        const forecastDate = new Date(currentDate);
        forecastDate.setDate(currentDate.getDate() + i);

        // Daily forecast date (for 5-Day Forecast)
        const dailyForecastDay = new Date(forecastDate);

        // Monthly forecast date (for Weather Forecast)
        const monthlyForecastDay = new Date(forecastDate);
        monthlyForecastDay.setMonth(forecastDate.getMonth() + i);

        mockForecastData.push({
          timestamp: forecastDate.getTime(),
          temperature: {
            min: 24 + Math.random() * 2,
            max: 30 + Math.random() * 3,
            avg: 27 + Math.random() * 2
          },
          humidity: 60 + Math.floor(Math.random() * 20),
          windSpeed: 2 + Math.random() * 3,
          conditions: ['clear sky', 'few clouds', 'scattered clouds', 'broken clouds'][Math.floor(Math.random() * 4)],
          icon: ['01d', '02d', '03d', '04d'][Math.floor(Math.random() * 4)],
          precipitation: Math.random() < 0.3 ? (Math.random() * 5).toFixed(1) : '0.0',
          date: dailyForecastDay.toLocaleDateString('en-IN', {
            day: 'numeric',
            month: 'short',
            timeZone: 'Asia/Kolkata'
          }),
          monthlyDate: monthlyForecastDay.toLocaleDateString('en-IN', {
            day: 'numeric',
            month: 'short',
            timeZone: 'Asia/Kolkata'
          }),
          actualDate: dailyForecastDay,
          actualMonthlyDate: monthlyForecastDay,
          dayOfWeek: dailyForecastDay.toLocaleDateString('en-IN', {
            weekday: 'long',
            timeZone: 'Asia/Kolkata'
          }),
          isMockData: true
        });
      }

      return mockForecastData;
    }
  }

  async getWeatherBulletin(farmerId) {
    try {
      // Try to get current weather and forecast
      let current, forecast;

      try {
        [current, forecast] = await Promise.all([
          this.getCurrentWeather(farmerId),
          this.getForecast(farmerId)
        ]);
      } catch (dataError) {
        console.error('Error fetching weather data for bulletin:', dataError);
        // If we can't get the data, use mock data
        current = {
          temperature: 28.5,
          feelsLike: 30.2,
          humidity: 65,
          windSpeed: 3.5,
          conditions: 'scattered clouds',
          icon: '03d',
          precipitation: 0,
          pressure: 1012,
          visibility: 10000,
          cloudiness: 40,
          lastUpdated: formatIST(new Date()),
          sunrise: formatIST(new Date(new Date().setHours(6, 30, 0, 0))),
          sunset: formatIST(new Date(new Date().setHours(18, 30, 0, 0))),
          timezone: 'Asia/Kolkata',
          rawTimestamp: Math.floor(Date.now() / 1000),
          isMockData: true
        };

        // Generate mock forecast data
        forecast = [];
        const currentDate = new Date();
        for (let i = 0; i < 7; i++) {
          const forecastDate = new Date(currentDate);
          forecastDate.setDate(currentDate.getDate() + i);

          forecast.push({
            timestamp: forecastDate.getTime(),
            temperature: {
              min: 24 + Math.random() * 2,
              max: 30 + Math.random() * 3,
              avg: 27 + Math.random() * 2
            },
            humidity: 60 + Math.floor(Math.random() * 20),
            windSpeed: 2 + Math.random() * 3,
            conditions: ['clear sky', 'few clouds', 'scattered clouds', 'broken clouds'][Math.floor(Math.random() * 4)],
            icon: ['01d', '02d', '03d', '04d'][Math.floor(Math.random() * 4)],
            precipitation: Math.random() < 0.3 ? (Math.random() * 5).toFixed(1) : '0.0',
            isMockData: true
          });
        }
      }

      // Generate the bulletin text
      const bulletinText = this.generateWeatherBulletin(current, forecast);

      return bulletinText;
    } catch (error) {
      console.error('Error generating weather bulletin:', error);

      // Return a generic bulletin as fallback
      return `Weather Bulletin for ${formatIST(new Date()).split(',')[0]}\n\n` +
        'Current Conditions: Partly cloudy with moderate temperatures.\n' +
        'Temperature: 28°C\n' +
        'Humidity: 65%\n' +
        'Wind Speed: 3.5 m/s\n\n' +
        'Agricultural Recommendations:\n' +
        '• Maintain regular irrigation schedule\n' +
        '• Monitor for pest activity\n' +
        '• Consider light fertilizer application\n\n' +
        'Weekly Weather Outlook: Expect partly cloudy conditions with occasional light showers.\n' +
        'Note: This is a generic bulletin as we could not generate a personalized one at this time.';
    }
  }

  generateWeatherBulletin(current, forecast) {
    try {
      const today = new Date();
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const tomorrowForecast = forecast.find(
        day => new Date(day.timestamp).toDateString() === tomorrow.toDateString()
      );

      let bulletin = `Weather Bulletin for ${formatIST(today).split(',')[0]}\n\n`;
      bulletin += `Current Conditions:\n`;
      bulletin += `- Temperature: ${current.temperature.toFixed(2)}°C\n`;
      bulletin += `- Humidity: ${current.humidity}%\n`;
      bulletin += `- Wind Speed: ${current.windSpeed.toFixed(2)} m/s\n`;
      bulletin += `- Conditions: ${current.conditions}\n\n`;

      if (tomorrowForecast) {
        bulletin += `Tomorrow's Forecast:\n`;
        bulletin += `- Temperature: ${tomorrowForecast.temperature.min.toFixed(2)}°C to ${tomorrowForecast.temperature.max.toFixed(2)}°C\n`;
        bulletin += `- Conditions: ${tomorrowForecast.conditions}\n`;
        bulletin += `- Precipitation: ${tomorrowForecast.precipitation}mm\n\n`;
      }

      // Add agricultural recommendations based on conditions
      bulletin += this.generateAgriRecommendations(current, forecast);

      // Weekly forecast summary
      bulletin += `\nWeekly Weather Outlook:\n`;
      forecast.slice(0, 7).forEach(day => {
        bulletin += `- ${formatIST(day.timestamp).split(',')[0]}: ${day.conditions}, ${day.temperature.min.toFixed(2)}°C to ${day.temperature.max.toFixed(2)}°C\n`;
      });

      return bulletin;
    } catch (error) {
      console.error('Error generating bulletin:', error);
      throw new Error('Failed to generate weather bulletin');
    }
  }

  generateAgriRecommendations(current, forecast) {
    try {
      let recommendations = `Agricultural Recommendations:\n\n`;

      // Temperature-based recommendations
      if (current.temperature > 35) {
        recommendations += `High Temperature Precautions:\n`;
        recommendations += `• Increase irrigation frequency\n`;
        recommendations += `• Apply mulch to retain soil moisture\n`;
        recommendations += `• Consider shade nets for sensitive crops\n`;
        recommendations += `• Water early morning or late evening\n\n`;
      } else if (current.temperature < 10) {
        recommendations += `Low Temperature Precautions:\n`;
        recommendations += `• Protect sensitive plants with covers\n`;
        recommendations += `• Reduce irrigation frequency\n`;
        recommendations += `• Consider using frost protection methods\n\n`;
      }

      // Rain forecast recommendations
      const rainForecast = forecast.find(day => parseFloat(day.precipitation) > 0);
      if (rainForecast) {
        recommendations += `Rain Expected:\n`;
        recommendations += `• Ensure proper field drainage\n`;
        recommendations += `• Delay fertilizer application\n`;
        recommendations += `• Consider harvesting mature crops\n`;
        recommendations += `• Prepare for pest management\n\n`;
      }

      // General recommendations
      recommendations += `General Farm Maintenance:\n`;
      recommendations += `• Monitor soil moisture levels\n`;
      recommendations += `• Check for pest infestations\n`;
      recommendations += `• Inspect irrigation systems\n`;
      recommendations += `• Maintain proper drainage\n`;
      recommendations += `• Keep farm equipment in good condition\n\n`;

      return recommendations;
    } catch (error) {
      console.error('Error generating recommendations:', error);
      return 'Unable to generate agricultural recommendations at this time.\n\n';
    }
  }
}

// Add a method to get weather data for AI agent
WeatherService.prototype.getFarmerWeather = async function(farmerId) {
  try {
    // Get current weather and forecast
    const [current, forecast] = await Promise.all([
      this.getCurrentWeather(farmerId),
      this.getForecast(farmerId)
    ]);

    // Format the data for the AI
    return {
      current: {
        temperature: current.temperature,
        humidity: current.humidity,
        conditions: current.conditions,
        windSpeed: current.windSpeed
      },
      forecast: forecast.map(day => ({
        date: day.date,
        maxTemp: day.temperature.max,
        minTemp: day.temperature.min,
        condition: day.conditions,
        chanceOfRain: day.precipitation > 0 ? `${day.precipitation}mm` : 'Low'
      }))
    };
  } catch (error) {
    console.error('Error getting farmer weather for AI:', error);
    // Return a minimal object with empty arrays to prevent errors
    return {
      current: {
        temperature: 'Unknown',
        humidity: 'Unknown',
        conditions: 'Unknown',
        windSpeed: 'Unknown'
      },
      forecast: []
    };
  }
};

module.exports = new WeatherService();

