require("dotenv").config();
const express = require("express");
const axios = require("axios");

const router = express.Router();

// Azure Speech Service configuration
const AZURE_SPEECH_KEY = process.env.AZURE_SPEECH_KEY;
const AZURE_SPEECH_REGION = process.env.AZURE_SPEECH_REGION;

if (!AZURE_SPEECH_KEY || !AZURE_SPEECH_REGION) {
  console.error("❌ Missing Azure Speech Service configuration in .env!");
}

const AZURE_TTS_URL = `https://${AZURE_SPEECH_REGION}.tts.speech.microsoft.com/cognitiveservices/v1`;

// Prevent Multiple Speech Requests
let isSpeaking = false;

router.post("/speak", async (req, res) => {
  try {
    const { text } = req.body;
    if (!text) {
      return res
        .status(400)
        .json({ error: "❌ Missing required field: 'text'." });
    }

    if (isSpeaking) {
      console.warn("⚠️ Speech request ignored (already speaking).");
      return res
        .status(429)
        .json({ error: "Already generating speech, please wait." });
    }
    isSpeaking = true;

    console.log("📢 Generating speech for:", text);

    // Use default voice
    const selectedVoice = "en-US-JennyNeural";

    // Build SSML payload with a default language attribute
    const xmlBody = `
      <speak version='1.0' xml:lang='en-US'>
        <voice name='${selectedVoice}'>
          ${text}
        </voice>
      </speak>`;

    // Send request to Azure Speech Service
    const response = await axios.post(AZURE_TTS_URL, xmlBody, {
      headers: {
        "Ocp-Apim-Subscription-Key": AZURE_SPEECH_KEY,
        "Content-Type": "application/ssml+xml",
        "X-Microsoft-OutputFormat": "audio-16khz-128kbitrate-mono-mp3",
        "User-Agent": "AzureTTSClient",
      },
      responseType: "arraybuffer",
    });

    console.log("✅ Speech synthesis successful!");
    isSpeaking = false;
    res.setHeader("Content-Type", "audio/mpeg");
    res.send(response.data);
  } catch (error) {
    isSpeaking = false;
    console.error("❌ Azure Speech Service Error:", error.message);
    res
      .status(500)
      .json({ error: "Failed to generate speech with Azure Speech Service." });
  }
});

module.exports = router;
