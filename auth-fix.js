// Quick fix for authentication issues in production
// This will temporarily allow access for testing

const admin = require('../config/firebase.config');
require('dotenv').config();

const auth = async (req, res, next) => {
  // Allow in development mode OR if FIREBASE_DEV_MODE is true
  if (process.env.NODE_ENV === 'development' || process.env.FIREBASE_DEV_MODE === 'true') {
    req.user = {
      id: 'development-user-id',
      uid: 'emulator-uid',
      role: 'TM', // Set to TM for testing TM dashboard
      email: '<EMAIL>',
      name: 'Territory Manager'
    };
    console.log('🔑 Development/Demo mode - Using mock user:', req.user);
    return next();
  }

  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('❌ No token provided in request');
      return res.status(401).json({
        success: false,
        message: 'No token provided'
      });
    }

    const token = authHeader.split(' ')[1];
    try {
      const decodedToken = await admin.auth().verifyIdToken(token);
      req.user = {
        ...decodedToken,
        id: decodedToken.uid
      };
      console.log('🔑 Authenticated user:', { id: req.user.id, role: req.user.role });
      next();
    } catch (error) {
      console.error('❌ Token verification failed:', error);
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    }
  } catch (error) {
    console.error('❌ Auth middleware error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error during authentication'
    });
  }
};

module.exports = auth;
