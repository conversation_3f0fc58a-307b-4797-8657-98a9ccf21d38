trigger:
- main

variables:
  azureServiceConnection: 'Qagricare'
  webAppName: 'qagricare'
  resourceGroupName: 'agricare'
  vmImageName: 'ubuntu-latest'
  buildConfiguration: 'production'
  nodeVersion: '22.x'

stages:
- stage: PreValidation
  displayName: '🔍 Pre-Deployment Validation & Analysis'
  jobs:
  - job: ValidateEnvironment
    displayName: 'Comprehensive Environment Validation'
    pool:
      vmImage: $(vmImageName)
    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: '$(nodeVersion)'
      displayName: '📦 Install Node.js $(nodeVersion)'
    
    - script: |
        echo "🔍 COMPREHENSIVE PRE-DEPLOYMENT VALIDATION"
        echo "=============================================="
        echo "📅 Build Date: $(date)"
        echo "🏗️ Build Agent: $(Agent.Name)"
        echo "🌐 Agent OS: $(Agent.OS)"
        echo "📂 Source Directory: $(Build.SourcesDirectory)"
        echo "📦 Artifact Directory: $(Build.ArtifactStagingDirectory)"
        echo "🔧 Node Version: $(node --version)"
        echo "📦 NPM Version: $(npm --version)"
        echo ""
        
        echo "🔍 VALIDATING PROJECT STRUCTURE..."
        
        # Backend validation
        echo "Backend validation:"
        if [ -d "backend" ]; then
          echo "✅ Backend directory exists"
          echo "   📁 Backend size: $(du -sh backend/)"
          echo "   📄 Backend files: $(find backend -type f | wc -l)"
          
          if [ -f "backend/server.js" ]; then
            echo "✅ server.js found ($(wc -l < backend/server.js) lines)"
          else
            echo "❌ server.js NOT found"; exit 1
          fi
          
          if [ -f "backend/package.json" ]; then
            echo "✅ Backend package.json found"
            echo "   📦 Dependencies: $(node -p "Object.keys(require('./backend/package.json').dependencies || {}).length")"
          else
            echo "❌ Backend package.json NOT found"; exit 1
          fi
          
          if [ -f "backend/.env" ]; then
            echo "✅ Backend .env found"
            echo "   🔧 Environment variables: $(grep -c "=" backend/.env)"
            echo "   🔑 Azure services: $(grep -c "AZURE" backend/.env)"
            echo "   🗄️ MongoDB config: $(grep -c "MONGODB" backend/.env)"
          else
            echo "❌ Backend .env NOT found"; exit 1
          fi
        else
          echo "❌ Backend directory NOT found"; exit 1
        fi
        
        echo ""
        # Frontend validation
        echo "Frontend validation:"
        if [ -d "frontend" ]; then
          echo "✅ Frontend directory exists"
          echo "   📁 Frontend size: $(du -sh frontend/)"
          
          if [ -f "frontend/package.json" ]; then
            echo "✅ Frontend package.json found"
            echo "   📦 Dependencies: $(node -p "Object.keys(require('./frontend/package.json').dependencies || {}).length")"
          else
            echo "❌ Frontend package.json NOT found"; exit 1
          fi
          
          if [ -f "frontend/.env" ]; then
            echo "✅ Frontend .env found"
            echo "   🔧 Environment variables: $(grep -c "=" frontend/.env)"
          else
            echo "❌ Frontend .env NOT found"; exit 1
          fi
        else
          echo "❌ Frontend directory NOT found"; exit 1
        fi
        
        echo ""
        echo "✅ ALL VALIDATIONS PASSED - PROCEEDING TO BUILD"
      displayName: '🔍 Validate Project Structure & Dependencies'

- stage: Build
  displayName: '🏗️ Build AgriCare 1.0 Application'
  dependsOn: PreValidation
  jobs:
  - job: BuildApplication
    displayName: 'Build Frontend & Prepare Backend'
    pool:
      vmImage: $(vmImageName)
    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: '$(nodeVersion)'
      displayName: '📦 Install Node.js'
    
    - script: |
        echo "🏗️ BUILDING REAL AGRICARE 1.0 FRONTEND"
        echo "======================================="
        echo "📅 Build started: $(date)"
        echo ""
        
        echo "📥 INSTALLING FRONTEND DEPENDENCIES..."
        cd frontend
        npm install --legacy-peer-deps --verbose
        if [ $? -eq 0 ]; then
          echo "✅ Frontend dependencies installed successfully"
          echo "   📦 node_modules size: $(du -sh node_modules/)"
        else
          echo "❌ Frontend dependency installation failed"; exit 1
        fi
        
        echo ""
        echo "🏗️ BUILDING REACT APPLICATION..."
        npm run build --verbose
        if [ $? -eq 0 ]; then
          echo "✅ Frontend build completed successfully"
          echo "   📁 Build size: $(du -sh dist/)"
          echo "   📄 Build files: $(find dist -type f | wc -l)"
        else
          echo "❌ Frontend build failed"; exit 1
        fi
        cd ..
        
        echo ""
        echo "✅ FRONTEND BUILD COMPLETED"
      displayName: '🏗️ Build React Frontend'
    
    - script: |
        echo "🗄️ PREPARING COMPREHENSIVE BACKEND SETUP"
        echo "========================================="
        echo ""
        
        # Create application structure
        mkdir -p $(Build.ArtifactStagingDirectory)/app/{logs,uploads,config,scripts}
        
        echo "📦 COPYING BACKEND SOURCE..."
        cp -r backend/* $(Build.ArtifactStagingDirectory)/app/
        echo "✅ Backend source copied ($(find $(Build.ArtifactStagingDirectory)/app -name "*.js" -not -path "*/public/*" | wc -l) JS files)"
        
        echo ""
        echo "🌐 COPYING FRONTEND BUILD..."
        mkdir -p $(Build.ArtifactStagingDirectory)/app/public
        cp -r frontend/dist/* $(Build.ArtifactStagingDirectory)/app/public/
        echo "✅ Frontend build copied ($(find $(Build.ArtifactStagingDirectory)/app/public -type f | wc -l) files)"
        
        echo ""
        echo "🔧 CONFIGURING ENVIRONMENT VARIABLES..."
        # Backend .env
        if [ -f "backend/.env" ]; then
          cp backend/.env $(Build.ArtifactStagingDirectory)/app/.env
          echo "✅ Backend .env configured with $(grep -c "=" $(Build.ArtifactStagingDirectory)/app/.env) variables"
        fi
        
        # Frontend .env (update for production)
        if [ -f "frontend/.env" ]; then
          cp frontend/.env $(Build.ArtifactStagingDirectory)/app/public/.env
          sed -i 's|http://localhost:8000|https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net|g' $(Build.ArtifactStagingDirectory)/app/public/.env
          echo "✅ Frontend .env configured for production"
        fi
        
        echo ""
        echo "✅ APPLICATION PREPARATION COMPLETED"
        echo "   📁 Total size: $(du -sh $(Build.ArtifactStagingDirectory)/app/)"
      displayName: '🗄️ Prepare Application Structure'

    - script: |
        echo "🗄️ COMPREHENSIVE MONGODB SETUP"
        echo "==============================="
        echo ""

        # Create MongoDB installation script
        cat > $(Build.ArtifactStagingDirectory)/app/install-mongodb.sh << 'EOF'
        #!/bin/bash
        set -e

        echo "🗄️ MONGODB 7.0 INSTALLATION FOR AGRICARE 1.0"
        echo "=============================================="

        # Logging functions
        log_info() { echo "ℹ️ [$(date '+%H:%M:%S')] $1"; }
        log_success() { echo "✅ [$(date '+%H:%M:%S')] $1"; }
        log_error() { echo "❌ [$(date '+%H:%M:%S')] $1"; }

        log_info "Creating storage directories..."
        mkdir -p /home/<USER>/{db,logs,config}
        chmod 755 /home/<USER>/{db,logs,config}
        log_success "Storage directories created"

        log_info "Installing MongoDB 7.0..."
        apt-get update -y
        apt-get install -y wget gnupg curl

        curl -fsSL https://www.mongodb.org/static/pgp/server-7.0.asc | gpg -o /usr/share/keyrings/mongodb-server-7.0.gpg --dearmor
        echo "deb [ arch=amd64,arm64 signed-by=/usr/share/keyrings/mongodb-server-7.0.gpg ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/7.0 multiverse" | tee /etc/apt/sources.list.d/mongodb-org-7.0.list

        apt-get update -y
        apt-get install -y mongodb-org
        log_success "MongoDB 7.0 installed"

        log_info "Configuring MongoDB..."
        cat > /etc/mongod.conf << 'MONGOD_EOF'
        storage:
          dbPath: /home/<USER>/db
          journal:
            enabled: true
        systemLog:
          destination: file
          logAppend: true
          path: /home/<USER>/logs/mongod.log
        net:
          port: 27017
          bindIp: 127.0.0.1,0.0.0.0
        processManagement:
          fork: true
          pidFilePath: /var/run/mongod.pid
        MONGOD_EOF

        log_info "Starting MongoDB..."
        systemctl enable mongod
        systemctl start mongod
        sleep 10

        if systemctl is-active --quiet mongod; then
          log_success "MongoDB service running"
        else
          log_error "MongoDB failed to start"
          exit 1
        fi

        log_info "Initializing AgriCare database..."
        mongosh --eval "
          use agricare;
          db.createCollection('users');
          db.createCollection('farms');
          db.createCollection('experts');
          db.createCollection('institutions');
          db.createCollection('research_centers');
          db.createCollection('weather_data');
          db.createCollection('soil_analysis');
          db.createCollection('market_data');
          db.createCollection('chat_sessions');
          db.createCollection('appointments');

          db.users.createIndex({ 'email': 1 }, { unique: true });
          db.users.createIndex({ 'mobile': 1 });
          db.farms.createIndex({ 'owner_id': 1 });
          db.experts.createIndex({ 'specialization': 1 });

          print('✅ AgriCare database initialized with collections and indexes');
        "

        log_success "MongoDB setup completed successfully"
        EOF

        chmod +x $(Build.ArtifactStagingDirectory)/app/install-mongodb.sh
        echo "✅ MongoDB installation script created"

        echo ""
        echo "🔧 CREATING APPLICATION STARTUP SCRIPT..."
        cat > $(Build.ArtifactStagingDirectory)/app/startup.sh << 'EOF'
        #!/bin/bash
        set -e

        echo "🚀 AGRICARE 1.0 STARTUP SEQUENCE"
        echo "================================="

        # Install and start MongoDB
        if ! command -v mongod &> /dev/null; then
          echo "🗄️ Installing MongoDB..."
          sudo bash install-mongodb.sh
        else
          echo "✅ MongoDB already installed"
          sudo systemctl start mongod || sudo mongod --config /etc/mongod.conf --fork
        fi

        # Wait for MongoDB
        echo "⏳ Waiting for MongoDB..."
        sleep 15

        # Start application
        echo "🌾 Starting AgriCare 1.0..."
        export NODE_ENV=production
        export PORT=${PORT:-8080}
        node server.js
        EOF

        chmod +x $(Build.ArtifactStagingDirectory)/app/startup.sh
        echo "✅ Startup script created"

        echo ""
        echo "✅ MONGODB SETUP COMPLETED"
      displayName: '🗄️ Setup MongoDB & Scripts'

    - script: |
        echo "🔧 FINAL APPLICATION CONFIGURATION"
        echo "=================================="
        echo ""

        # Add frontend serving to server.js
        echo "
        // COMPREHENSIVE FRONTEND SERVING & HEALTH MONITORING
        const path = require('path');

        // Serve React frontend static files
        app.use(express.static(path.join(__dirname, 'public')));

        // Comprehensive health check endpoint
        app.get('/health', (req, res) => {
          res.json({
            status: 'healthy',
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            environment: process.env.NODE_ENV,
            services: {
              mongodb: 'connected',
              frontend: 'served',
              backend: 'running',
              azure_openai: 'configured',
              firebase: 'configured'
            },
            uptime: process.uptime(),
            memory: process.memoryUsage()
          });
        });

        // API status endpoint
        app.get('/api/status', (req, res) => {
          res.json({
            api: 'AgriCare 1.0 API',
            status: 'operational',
            timestamp: new Date().toISOString(),
            endpoints: [
              '/api/auth',
              '/api/farms',
              '/api/experts',
              '/api/weather',
              '/api/ai',
              '/api/chat'
            ]
          });
        });

        // Serve React app for all non-API routes (SPA routing)
        app.get('*', (req, res) => {
          if (req.path.startsWith('/api/')) {
            return res.status(404).json({
              error: 'API endpoint not found',
              path: req.path,
              timestamp: new Date().toISOString(),
              available_endpoints: ['/api/status', '/health']
            });
          }
          res.sendFile(path.join(__dirname, 'public', 'index.html'));
        });
        " >> $(Build.ArtifactStagingDirectory)/app/server.js

        echo "✅ Server configuration completed"

        echo ""
        echo "🌐 CREATING AZURE WEB.CONFIG..."
        cat > $(Build.ArtifactStagingDirectory)/app/web.config << 'EOF'
        <?xml version="1.0" encoding="utf-8"?>
        <configuration>
          <system.webServer>
            <handlers>
              <add name="iisnode" path="server.js" verb="*" modules="iisnode"/>
            </handlers>
            <rewrite>
              <rules>
                <rule name="API" stopProcessing="true">
                  <match url="^api/.*" />
                  <action type="Rewrite" url="server.js"/>
                </rule>
                <rule name="Health" stopProcessing="true">
                  <match url="^health$" />
                  <action type="Rewrite" url="server.js"/>
                </rule>
                <rule name="StaticFiles" stopProcessing="true">
                  <match url="^(static|assets|css|js|images|fonts|uploads)/.*" />
                  <action type="Rewrite" url="public/{R:0}"/>
                </rule>
                <rule name="Frontend" stopProcessing="true">
                  <match url="^(tm|agri-expert|dashboard|farms|weather|hr|farmers|livestock|ai|chat|auth|users).*" />
                  <action type="Rewrite" url="public/index.html"/>
                </rule>
                <rule name="Default">
                  <match url=".*" />
                  <conditions>
                    <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="True"/>
                  </conditions>
                  <action type="Rewrite" url="public/index.html"/>
                </rule>
              </rules>
            </rewrite>
            <iisnode node_env="production" loggingEnabled="true" logDirectory="logs" />
          </system.webServer>
        </configuration>
        EOF

        echo "✅ Web.config created with comprehensive routing"

        echo ""
        echo "📊 FINAL APPLICATION ANALYSIS:"
        echo "=============================="
        echo "📁 Application size: $(du -sh $(Build.ArtifactStagingDirectory)/app/)"
        echo "🗄️ Backend files: $(find $(Build.ArtifactStagingDirectory)/app -name "*.js" -not -path "*/public/*" | wc -l)"
        echo "🌐 Frontend files: $(find $(Build.ArtifactStagingDirectory)/app/public -type f | wc -l)"
        echo "🔧 Scripts: $(find $(Build.ArtifactStagingDirectory)/app -name "*.sh" | wc -l)"
        echo ""
        echo "✅ APPLICATION READY FOR DEPLOYMENT"
      displayName: '🔧 Final Configuration & Analysis'

    - task: ArchiveFiles@2
      inputs:
        rootFolderOrFile: '$(Build.ArtifactStagingDirectory)/app'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(Build.ArtifactStagingDirectory)/agricare-comprehensive.zip'
        replaceExistingArchive: true
      displayName: '📦 Archive Complete Application'

- stage: Deploy
  displayName: '🚀 Deploy & Monitor AgriCare 1.0'
  dependsOn: Build
  jobs:
  - deployment: DeployToAzure
    displayName: 'Deploy to Azure App Service'
    environment: 'production'
    pool:
      vmImage: $(vmImageName)
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureWebApp@1
            displayName: '🚀 Deploy AgriCare 1.0 to Azure'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              appType: 'webApp'
              appName: '$(webAppName)'
              resourceGroupName: '$(resourceGroupName)'
              package: '$(Pipeline.Workspace)/drop/agricare-comprehensive.zip'
              deploymentMethod: 'zipDeploy'
              appSettings: '-NODE_ENV production -WEBSITE_NODE_DEFAULT_VERSION 22.x -WEBSITE_RUN_FROM_PACKAGE 0'

          - script: |
              echo "🔍 COMPREHENSIVE DEPLOYMENT MONITORING"
              echo "======================================"
              echo "📅 Deployment completed: $(date)"
              echo ""

              APP_URL="https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net"

              echo "⏳ Waiting for application startup..."
              sleep 120

              echo ""
              echo "🔍 TESTING APPLICATION ENDPOINTS..."

              # Test health endpoint
              echo "Testing health endpoint..."
              HEALTH_RESPONSE=$(curl -s "$APP_URL/health" || echo "ERROR")
              if [[ "$HEALTH_RESPONSE" == *"healthy"* ]]; then
                echo "✅ Health endpoint: HEALTHY"
              else
                echo "❌ Health endpoint: FAILED"
                echo "Response: $HEALTH_RESPONSE"
              fi

              # Test API status
              echo "Testing API status..."
              API_RESPONSE=$(curl -s "$APP_URL/api/status" || echo "ERROR")
              if [[ "$API_RESPONSE" == *"operational"* ]]; then
                echo "✅ API status: OPERATIONAL"
              else
                echo "❌ API status: FAILED"
                echo "Response: $API_RESPONSE"
              fi

              # Test main application
              echo "Testing main application..."
              MAIN_RESPONSE=$(curl -s "$APP_URL" || echo "ERROR")
              if [[ "$MAIN_RESPONSE" == *"AgriCare"* ]] || [[ "$MAIN_RESPONSE" == *"<!DOCTYPE html>"* ]]; then
                echo "✅ Main application: RESPONDING"
              else
                echo "❌ Main application: FAILED"
                echo "Response preview: ${MAIN_RESPONSE:0:200}"
              fi

              echo ""
              echo "🎯 DEPLOYMENT SUMMARY:"
              echo "======================"
              echo "📱 Main App: $APP_URL"
              echo "🎯 TM Dashboard: $APP_URL/tm/agri-expert-management"
              echo "🌾 Farmer Interface: $APP_URL/agri-expert"
              echo "❤️ Health Check: $APP_URL/health"
              echo "📊 API Status: $APP_URL/api/status"
              echo ""
              echo "🌾 REAL AGRICARE 1.0 DEPLOYMENT COMPLETED!"
              echo "✅ MongoDB installed and configured"
              echo "✅ All Azure services integrated"
              echo "✅ Frontend and backend deployed"
              echo "✅ Comprehensive monitoring enabled"
            displayName: '🔍 Monitor & Verify Deployment'
