trigger:
- main

variables:
  azureServiceConnection: 'Qagricare'
  webAppName: 'qagricare'
  resourceGroupName: 'agricare'

pool:
  vmImage: 'ubuntu-latest'

stages:
- stage: Build
  displayName: 'Cosmos DB Connection Fix'
  jobs:
  - job: BuildApp
    displayName: 'Build with Azure Cosmos DB'
    steps:
    - checkout: self
      clean: true
      fetchDepth: 1
      displayName: 'Checkout Repository'
    
    - task: NodeTool@0
      inputs:
        versionSpec: '22.x'
      displayName: 'Install Node.js'
    
    - script: |
        echo "=== COMPREHENSIVE API URL FIX ==="
        cd frontend
        
        echo "Fixing all API URLs to use production endpoints..."
        
        # Fix all localhost references to use relative URLs
        find src -name "*.js" -o -name "*.jsx" | while read file; do
          if grep -q "localhost\|8000\|8002" "$file"; then
            echo "Fixing: $file"
            sed -i "s|http://localhost:8000/api|/api|g" "$file"
            sed -i "s|http://localhost:8000|https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net|g" "$file"
            sed -i "s|localhost:8000|qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net|g" "$file"
            sed -i "s|http://localhost:8002/api|/api|g" "$file"
            sed -i "s|http://localhost:8002|https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net|g" "$file"
            sed -i "s|localhost:8002|qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net|g" "$file"
          fi
        done
        
        npm install --legacy-peer-deps
        npm run build
        echo "Frontend build completed with API URL fixes"
      displayName: 'Build Frontend with API URL Fix'
    
    - script: |
        echo "Installing backend dependencies..."
        cd backend
        npm install
        echo "Backend dependencies installed"
      displayName: 'Install Backend Dependencies'
    
    - script: |
        echo "Preparing application with Azure Cosmos DB..."
        
        # Create deployment directory
        mkdir -p $(Build.ArtifactStagingDirectory)/app
        
        # Copy backend files
        cp -r backend/* $(Build.ArtifactStagingDirectory)/app/
        
        # Copy frontend build to backend public directory
        mkdir -p $(Build.ArtifactStagingDirectory)/app/public
        cp -r frontend/dist/* $(Build.ArtifactStagingDirectory)/app/public/
        
        # Create production .env file with Azure Cosmos DB connection
        cat > $(Build.ArtifactStagingDirectory)/app/.env << 'EOF'
        NODE_ENV=production
        PORT=8080
        
        # Azure Cosmos DB MongoDB API Configuration
        MONGODB_URI=*******************************************************************************************************************************************************************************************************************************************
        DB_NAME=agricare
        
        # Authentication
        JWT_SECRET=agricare-production-secret-2025
        FIREBASE_DEV_MODE=true
        DEFAULT_OTP=123456
        
        # Azure OpenAI Configuration (demo mode)
        AZURE_OPENAI_ENDPOINT=https://image-gpt4o.openai.azure.com/
        AZURE_OPENAI_API_KEY=demo-key
        AZURE_DEPLOYMENT_NAME=gpt-4o
        AZURE_API_VERSION=2024-08-01-preview
        
        # Azure Speech Services (demo mode)
        AZURE_SPEECH_KEY=demo-speech-key
        AZURE_SPEECH_REGION=centralindia
        
        # Application URLs
        FRONTEND_URL=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net
        API_URL=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net/api
        EOF
        
        echo "Application prepared with Azure Cosmos DB configuration"
      displayName: 'Prepare Application'
    
    - script: |
        echo "Updating database configuration for Azure Cosmos DB..."
        
        cd $(Build.ArtifactStagingDirectory)/app
        
        # Update database.js for Azure Cosmos DB
        cat > config/database.js << 'COSMOS_DB_EOF'
        const mongoose = require('mongoose');
        require('dotenv').config();
        
        const connectDB = async () => {
            try {
                console.log('🔄 Connecting to Azure Cosmos DB...');
                
                // Azure Cosmos DB specific options
                const options = {
                    useNewUrlParser: true,
                    useUnifiedTopology: true,
                    serverSelectionTimeoutMS: 30000,
                    socketTimeoutMS: 45000,
                    maxPoolSize: 10,
                    bufferMaxEntries: 0,
                    retryWrites: false, // Important for Cosmos DB
                    ssl: true, // Required for Cosmos DB
                };
        
                const mongoUri = process.env.MONGODB_URI;
                console.log('🔗 Cosmos DB URI configured');
                
                const conn = await mongoose.connect(mongoUri, options);
                console.log(`✅ Azure Cosmos DB Connected: ${conn.connection.host}`);
                console.log(`🗄️ Database: ${conn.connection.name}`);
                console.log(`🌐 Connection State: ${conn.connection.readyState}`);
                
                // Test the connection
                await mongoose.connection.db.admin().ping();
                console.log('🏓 Cosmos DB ping successful');
                
            } catch (error) {
                console.error(`❌ Azure Cosmos DB Connection Error: ${error.message}`);
                console.error('Full error:', error);
                
                // In production, retry connection
                if (process.env.NODE_ENV === 'production') {
                    console.log('🔄 Retrying Cosmos DB connection in 10 seconds...');
                    setTimeout(async () => {
                        try {
                            const conn = await mongoose.connect(process.env.MONGODB_URI, options);
                            console.log(`✅ Azure Cosmos DB Connected (retry): ${conn.connection.host}`);
                        } catch (retryError) {
                            console.error(`❌ Cosmos DB Connection Failed (retry): ${retryError.message}`);
                            // Don't exit - let app continue with limited functionality
                            console.log('⚠️ Continuing without database connection');
                        }
                    }, 10000);
                } else {
                    process.exit(1);
                }
            }
        };
        
        module.exports = connectDB;
        COSMOS_DB_EOF
        
        echo "Database configuration updated for Azure Cosmos DB"
      displayName: 'Update Database Configuration'
    
    - script: |
        echo "Creating AI fallback for demo mode..."
        
        cd $(Build.ArtifactStagingDirectory)/app
        
        # Create AI fallback responses
        cat > routes/ai-fallback.js << 'AI_FALLBACK_EOF'
        // Fallback AI responses when Azure OpenAI is not available
        const fallbackResponses = {
          weather: "Based on the current weather conditions in your area, I recommend monitoring soil moisture levels and adjusting irrigation accordingly. The temperature and humidity levels look suitable for most crops. Consider covering sensitive plants if extreme weather is expected.",
          
          soil: "Your soil health parameters look good overall. I recommend regular testing and organic matter addition to maintain optimal growing conditions. Consider crop rotation and proper fertilization based on your soil's NPK levels.",
          
          pest: "For effective pest management, I recommend regular field inspections and integrated pest management practices. Monitor for early signs of pest activity, use beneficial insects when possible, and apply targeted treatments only when necessary.",
          
          crop: "Your crops appear to be in good condition. Continue with regular monitoring, proper watering, and timely fertilization. Consider the growth stage of your crops when planning maintenance activities.",
          
          general: "I'm here to help with your farming questions! While I'm running in demo mode with Azure Cosmos DB, I can still provide general agricultural guidance based on best practices and your farm data."
        };
        
        function getFallbackResponse(message, farmData) {
          const msg = message.toLowerCase();
          
          if (msg.includes('weather') || msg.includes('rain') || msg.includes('temperature')) {
            return fallbackResponses.weather;
          } else if (msg.includes('soil') || msg.includes('fertilizer') || msg.includes('nutrient')) {
            return fallbackResponses.soil;
          } else if (msg.includes('pest') || msg.includes('insect') || msg.includes('disease')) {
            return fallbackResponses.pest;
          } else if (msg.includes('crop') || msg.includes('plant') || msg.includes('grow')) {
            return fallbackResponses.crop;
          } else {
            return fallbackResponses.general;
          }
        }
        
        module.exports = { getFallbackResponse };
        AI_FALLBACK_EOF
        
        echo "AI fallback responses created"
      displayName: 'Create AI Fallback'
    
    - script: |
        echo "Fixing authentication middleware..."
        
        cd $(Build.ArtifactStagingDirectory)/app
        
        # Fix auth middleware to allow FIREBASE_DEV_MODE in production
        cat > middleware/auth.js << 'AUTH_EOF'
        const admin = require('../config/firebase.config');
        require('dotenv').config();

        const auth = async (req, res, next) => {
          // Allow in development mode OR if FIREBASE_DEV_MODE is true
          if (process.env.NODE_ENV === 'development' || process.env.FIREBASE_DEV_MODE === 'true') {
            req.user = {
              id: 'development-user-id',
              uid: 'emulator-uid',
              role: 'TM', // Set to TM for testing TM dashboard
              email: '<EMAIL>',
              name: 'Territory Manager'
            };
            console.log('🔑 Development/Demo mode - Using mock user:', req.user);
            return next();
          }

          try {
            const authHeader = req.headers.authorization;
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
              console.log('❌ No token provided in request');
              return res.status(401).json({
                success: false,
                message: 'No token provided'
              });
            }

            const token = authHeader.split(' ')[1];
            try {
              const decodedToken = await admin.auth().verifyIdToken(token);
              req.user = {
                ...decodedToken,
                id: decodedToken.uid
              };
              console.log('🔑 Authenticated user:', { id: req.user.id, role: req.user.role });
              next();
            } catch (error) {
              console.error('❌ Token verification failed:', error);
              return res.status(401).json({
                success: false,
                message: 'Invalid token'
              });
            }
          } catch (error) {
            console.error('❌ Auth middleware error:', error);
            return res.status(500).json({
              success: false,
              message: 'Internal server error during authentication'
            });
          }
        };

        module.exports = auth;
        AUTH_EOF
        
        echo "Authentication middleware fixed"
      displayName: 'Fix Authentication'
    
    - script: |
        echo "Adding frontend serving to server.js..."
        
        cd $(Build.ArtifactStagingDirectory)/app
        
        # Use Python to fix the server.js route
        python3 << 'PYTHON_EOF'
        import re
        
        # Read the server.js file
        with open('server.js', 'r') as f:
            content = f.read()
        
        # Replace the conflicting default route
        old_pattern = r'app\.get\("/", \(req, res\) => \{\s*res\.json\(\{ status: "ok", message: "API is running[^}]+\}\);\s*\}\);'
        new_route = '''// Serve React frontend on root route
        app.use(express.static(path.join(__dirname, 'public')));
        
        app.get("/", (req, res) => {
          res.sendFile(path.join(__dirname, 'public', 'index.html'));
        });
        
        // Serve React app for all non-API routes (SPA routing)
        app.get('*', (req, res) => {
          if (req.path.startsWith('/api/')) {
            return res.status(404).json({ error: 'API endpoint not found' });
          }
          res.sendFile(path.join(__dirname, 'public', 'index.html'));
        });'''
        
        # Replace the pattern
        content = re.sub(old_pattern, new_route, content, flags=re.DOTALL)
        
        # Write back to file
        with open('server.js', 'w') as f:
            f.write(content)
        
        print("✅ Server.js updated successfully")
        PYTHON_EOF
        
        echo "Frontend serving configured"
      displayName: 'Configure Frontend Serving'
    
    - task: ArchiveFiles@2
      inputs:
        rootFolderOrFile: '$(Build.ArtifactStagingDirectory)/app'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(Build.ArtifactStagingDirectory)/agricare-app.zip'
        replaceExistingArchive: true
      displayName: 'Archive Application'
    
    - task: PublishBuildArtifacts@1
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)'
        ArtifactName: 'drop'
        publishLocation: 'Container'
      displayName: 'Publish Artifacts'

- stage: Deploy
  displayName: 'Deploy with Cosmos DB'
  dependsOn: Build
  jobs:
  - deployment: DeployApp
    displayName: 'Deploy with Azure Cosmos DB'
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureWebApp@1
            displayName: 'Deploy to Azure App Service'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              appType: 'webApp'
              appName: '$(webAppName)'
              resourceGroupName: '$(resourceGroupName)'
              package: '$(Pipeline.Workspace)/drop/agricare-app.zip'
              deploymentMethod: 'zipDeploy'
              appSettings: '-NODE_ENV production -WEBSITE_NODE_DEFAULT_VERSION 22.x -PORT 8080 -FIREBASE_DEV_MODE true'
          
          - script: |
              echo "Testing Azure Cosmos DB connection..."
              sleep 120
              
              APP_URL="https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net"
              
              echo "Testing API endpoints with Cosmos DB..."
              
              # Test chat history (should work with Cosmos DB)
              echo "Testing /api/chat/history..."
              CHAT_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL/api/chat/history" || echo "000")
              echo "Chat history status: $CHAT_STATUS"
              
              # Test analysis history (should work with Cosmos DB)
              echo "Testing /api/chat/analysis-history..."
              ANALYSIS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL/api/chat/analysis-history" || echo "000")
              echo "Analysis history status: $ANALYSIS_STATUS"
              
              # Test AI chat (should work with fallback)
              echo "Testing /api/ai/chat..."
              AI_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL/api/ai/chat" || echo "000")
              echo "AI chat status: $AI_STATUS"
              
              # Test farmers stats (should work with Cosmos DB)
              echo "Testing /api/farmers/stats..."
              FARMERS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL/api/farmers/stats" || echo "000")
              echo "Farmers stats status: $FARMERS_STATUS"
              
              # Test main app
              echo "Testing main application..."
              MAIN_RESPONSE=$(curl -s "$APP_URL" | head -c 100 || echo "No response")
              echo "Main response: $MAIN_RESPONSE"
              
              echo "Azure Cosmos DB deployment completed"
              echo "Application URL: $APP_URL"
              echo "MongoDB features should now work with Azure Cosmos DB!"
            displayName: 'Verify Cosmos DB Deployment'
