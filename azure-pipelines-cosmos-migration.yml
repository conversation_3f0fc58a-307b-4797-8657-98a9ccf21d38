trigger:
- main

variables:
  azureServiceConnection: 'Qagricare'
  webAppName: 'qagricare'
  resourceGroupName: 'agricare'

pool:
  vmImage: 'ubuntu-latest'

stages:
- stage: Build
  displayName: 'Build with Cosmos DB Migration'
  jobs:
  - job: BuildApp
    displayName: 'Build and Migrate Data'
    steps:
    - checkout: self
      clean: true
      fetchDepth: 1
      displayName: 'Checkout Repository'
    
    - task: NodeTool@0
      inputs:
        versionSpec: '22.x'
      displayName: 'Install Node.js'
    
    - script: |
        echo "=== COMPREHENSIVE API URL FIX ==="
        cd frontend
        
        echo "Fixing all API URLs to use production endpoints..."
        find src -name "*.js" -o -name "*.jsx" | while read file; do
          if grep -q "localhost\|8000\|8002" "$file"; then
            echo "Fixing: $file"
            sed -i "s|http://localhost:8000/api|/api|g" "$file"
            sed -i "s|http://localhost:8000|https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net|g" "$file"
            sed -i "s|localhost:8000|qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net|g" "$file"
            sed -i "s|http://localhost:8002/api|/api|g" "$file"
            sed -i "s|http://localhost:8002|https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net|g" "$file"
            sed -i "s|localhost:8002|qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net|g" "$file"
          fi
        done
        
        npm install --legacy-peer-deps
        npm run build
        echo "Frontend build completed with API URL fixes"
      displayName: 'Build Frontend with API URL Fix'
    
    - script: |
        echo "Installing backend dependencies..."
        cd backend
        npm install
        echo "Backend dependencies installed"
      displayName: 'Install Backend Dependencies'
    
    - script: |
        echo "Preparing application with Azure Cosmos DB..."
        
        # Create deployment directory
        mkdir -p $(Build.ArtifactStagingDirectory)/app
        
        # Copy backend files
        cp -r backend/* $(Build.ArtifactStagingDirectory)/app/
        
        # Copy frontend build to backend public directory
        mkdir -p $(Build.ArtifactStagingDirectory)/app/public
        cp -r frontend/dist/* $(Build.ArtifactStagingDirectory)/app/public/
        
        # Copy migration script
        cp migrate-to-cosmos.js $(Build.ArtifactStagingDirectory)/app/
        
        # Create production .env file with Azure Cosmos DB connection
        cat > $(Build.ArtifactStagingDirectory)/app/.env << 'EOF'
        NODE_ENV=production
        PORT=8080
        
        # Azure Cosmos DB MongoDB API Configuration
        MONGODB_URI=*******************************************************************************************************************************************************************************************************************************************
        DB_NAME=agricare
        
        # Authentication
        JWT_SECRET=agricare-production-secret-2025
        FIREBASE_DEV_MODE=true
        DEFAULT_OTP=123456
        
        # Azure OpenAI Configuration (demo mode)
        AZURE_OPENAI_ENDPOINT=https://image-gpt4o.openai.azure.com/
        AZURE_OPENAI_API_KEY=demo-key
        AZURE_DEPLOYMENT_NAME=gpt-4o
        AZURE_API_VERSION=2024-08-01-preview
        
        # Azure Speech Services (demo mode)
        AZURE_SPEECH_KEY=demo-speech-key
        AZURE_SPEECH_REGION=centralindia
        
        # Application URLs
        FRONTEND_URL=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net
        API_URL=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net/api
        EOF
        
        echo "Application prepared with Azure Cosmos DB configuration"
      displayName: 'Prepare Application'
    
    - script: |
        echo "Updating database configuration for Azure Cosmos DB..."
        
        cd $(Build.ArtifactStagingDirectory)/app
        
        # Update database.js for Azure Cosmos DB with better error handling
        cat > config/database.js << 'COSMOS_DB_EOF'
        const mongoose = require('mongoose');
        require('dotenv').config();
        
        let isConnected = false;
        
        const connectDB = async () => {
            try {
                console.log('🔄 Connecting to Azure Cosmos DB...');
                console.log('🔗 Using Cosmos DB URI: quamin.mongo.cosmos.azure.com:10255');
                
                // Azure Cosmos DB specific options
                const options = {
                    useNewUrlParser: true,
                    useUnifiedTopology: true,
                    serverSelectionTimeoutMS: 30000,
                    socketTimeoutMS: 45000,
                    maxPoolSize: 10,
                    bufferMaxEntries: 0,
                    retryWrites: false, // Important for Cosmos DB
                    ssl: true, // Required for Cosmos DB
                    authSource: 'admin'
                };
        
                const mongoUri = process.env.MONGODB_URI;
                
                const conn = await mongoose.connect(mongoUri, options);
                console.log(`✅ Azure Cosmos DB Connected: ${conn.connection.host}`);
                console.log(`🗄️ Database: ${conn.connection.name}`);
                console.log(`🌐 Connection State: ${conn.connection.readyState}`);
                
                // Test the connection
                await mongoose.connection.db.admin().ping();
                console.log('🏓 Cosmos DB ping successful');
                
                isConnected = true;
                
                // Set up connection event handlers
                mongoose.connection.on('connected', () => {
                    console.log('✅ Mongoose connected to Cosmos DB');
                    isConnected = true;
                });
                
                mongoose.connection.on('error', (err) => {
                    console.error('❌ Mongoose connection error:', err);
                    isConnected = false;
                });
                
                mongoose.connection.on('disconnected', () => {
                    console.log('⚠️ Mongoose disconnected from Cosmos DB');
                    isConnected = false;
                });
                
            } catch (error) {
                console.error(`❌ Azure Cosmos DB Connection Error: ${error.message}`);
                console.error('Full error:', error);
                isConnected = false;
                
                // In production, retry connection
                if (process.env.NODE_ENV === 'production') {
                    console.log('🔄 Retrying Cosmos DB connection in 15 seconds...');
                    setTimeout(async () => {
                        try {
                            const conn = await mongoose.connect(process.env.MONGODB_URI, options);
                            console.log(`✅ Azure Cosmos DB Connected (retry): ${conn.connection.host}`);
                            isConnected = true;
                        } catch (retryError) {
                            console.error(`❌ Cosmos DB Connection Failed (retry): ${retryError.message}`);
                            console.log('⚠️ Continuing without database connection');
                        }
                    }, 15000);
                } else {
                    process.exit(1);
                }
            }
        };
        
        const getConnectionStatus = () => {
            return {
                isConnected,
                readyState: mongoose.connection.readyState,
                host: mongoose.connection.host,
                name: mongoose.connection.name
            };
        };
        
        module.exports = { connectDB, getConnectionStatus };
        COSMOS_DB_EOF
        
        echo "Database configuration updated for Azure Cosmos DB"
      displayName: 'Update Database Configuration'
    
    - script: |
        echo "Creating database status endpoint..."
        
        cd $(Build.ArtifactStagingDirectory)/app
        
        # Add database status route to server.js
        cat > add-db-status.js << 'DB_STATUS_SCRIPT_EOF'
        const fs = require('fs');
        
        console.log('Adding database status endpoint...');
        let content = fs.readFileSync('server.js', 'utf8');
        
        // Add database status endpoint before health check
        const dbStatusRoute = `
        // Database status endpoint
        app.get('/api/db-status', async (req, res) => {
          try {
            const dbStatus = mongoose.connection.readyState === 1 ? 'connected' : 'disconnected';
            const collections = await mongoose.connection.db.listCollections().toArray();
            
            res.json({
              success: true,
              database: {
                status: dbStatus,
                host: mongoose.connection.host || 'unknown',
                name: mongoose.connection.name || 'unknown',
                readyState: mongoose.connection.readyState,
                collections: collections.map(c => c.name),
                collectionCount: collections.length
              },
              timestamp: new Date().toISOString()
            });
          } catch (error) {
            console.error('Database status check failed:', error);
            res.status(500).json({
              success: false,
              database: {
                status: 'error',
                error: error.message
              },
              timestamp: new Date().toISOString()
            });
          }
        });
        `;
        
        // Insert before health check
        const healthCheckIndex = content.indexOf('// ✅ Health Check Endpoint');
        if (healthCheckIndex > -1) {
          const beforeHealth = content.substring(0, healthCheckIndex);
          const afterHealth = content.substring(healthCheckIndex);
          content = beforeHealth + dbStatusRoute + '\n' + afterHealth;
          
          fs.writeFileSync('server.js', content);
          console.log('✅ Database status endpoint added');
        } else {
          console.log('⚠️ Could not find health check endpoint location');
        }
        DB_STATUS_SCRIPT_EOF
        
        node add-db-status.js
        
        echo "Database status endpoint created"
      displayName: 'Add Database Status Endpoint'
    
    - script: |
        echo "Fixing server.js route conflict..."
        
        cd $(Build.ArtifactStagingDirectory)/app
        
        # Create route fix script
        cat > fix-routes.js << 'FIX_SCRIPT_EOF'
        const fs = require('fs');
        
        console.log('Reading server.js...');
        let content = fs.readFileSync('server.js', 'utf8');
        
        // Find and replace the conflicting default route
        const oldRoute = `app.get("/", (req, res) => {
          res.json({ status: "ok", message: "API is running 🚀", timestamp: new Date().toISOString() });
        });`;
        
        const newRoute = `// Serve React frontend on root route
        app.use(express.static(path.join(__dirname, 'public')));
        
        app.get("/", (req, res) => {
          res.sendFile(path.join(__dirname, 'public', 'index.html'));
        });
        
        // Serve React app for all non-API routes (SPA routing)
        app.get('*', (req, res) => {
          if (req.path.startsWith('/api/')) {
            return res.status(404).json({ error: 'API endpoint not found' });
          }
          res.sendFile(path.join(__dirname, 'public', 'index.html'));
        });`;
        
        // Replace the route
        if (content.includes('res.json({ status: "ok", message: "API is running')) {
          console.log('Found conflicting route, replacing...');
          content = content.replace(oldRoute, newRoute);
          console.log('Route replaced successfully');
        } else {
          console.log('Conflicting route not found, adding frontend serving...');
          const healthCheckIndex = content.indexOf('// ✅ Health Check Endpoint');
          if (healthCheckIndex > -1) {
            const beforeHealth = content.substring(0, healthCheckIndex);
            const afterHealth = content.substring(healthCheckIndex);
            content = beforeHealth + newRoute + '\n\n' + afterHealth;
          }
        }
        
        fs.writeFileSync('server.js', content);
        console.log('Server.js fixed successfully');
        FIX_SCRIPT_EOF
        
        node fix-routes.js
        echo "Server.js route conflict fixed"
      displayName: 'Fix Server Routes'
    
    - script: |
        echo "Fixing authentication middleware..."
        
        cd $(Build.ArtifactStagingDirectory)/app
        
        cat > middleware/auth.js << 'AUTH_EOF'
        const admin = require('../config/firebase.config');
        require('dotenv').config();

        const auth = async (req, res, next) => {
          if (process.env.NODE_ENV === 'development' || process.env.FIREBASE_DEV_MODE === 'true') {
            req.user = {
              id: 'development-user-id',
              uid: 'emulator-uid',
              role: 'TM',
              email: '<EMAIL>',
              name: 'Territory Manager'
            };
            console.log('🔑 Development/Demo mode - Using mock user:', req.user);
            return next();
          }

          try {
            const authHeader = req.headers.authorization;
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
              return res.status(401).json({
                success: false,
                message: 'No token provided'
              });
            }

            const token = authHeader.split(' ')[1];
            const decodedToken = await admin.auth().verifyIdToken(token);
            req.user = { ...decodedToken, id: decodedToken.uid };
            next();
          } catch (error) {
            console.error('❌ Auth middleware error:', error);
            return res.status(401).json({
              success: false,
              message: 'Invalid token'
            });
          }
        };

        module.exports = auth;
        AUTH_EOF
        
        echo "Authentication middleware fixed"
      displayName: 'Fix Authentication'
    
    - task: ArchiveFiles@2
      inputs:
        rootFolderOrFile: '$(Build.ArtifactStagingDirectory)/app'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(Build.ArtifactStagingDirectory)/agricare-app.zip'
        replaceExistingArchive: true
      displayName: 'Archive Application'
    
    - task: PublishBuildArtifacts@1
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)'
        ArtifactName: 'drop'
        publishLocation: 'Container'
      displayName: 'Publish Artifacts'

- stage: Deploy
  displayName: 'Deploy with Cosmos DB'
  dependsOn: Build
  jobs:
  - deployment: DeployApp
    displayName: 'Deploy with Database Migration'
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureWebApp@1
            displayName: 'Deploy to Azure App Service'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              appType: 'webApp'
              appName: '$(webAppName)'
              resourceGroupName: '$(resourceGroupName)'
              package: '$(Pipeline.Workspace)/drop/agricare-app.zip'
              deploymentMethod: 'zipDeploy'
              appSettings: '-NODE_ENV production -WEBSITE_NODE_DEFAULT_VERSION 22.x -PORT 8080 -FIREBASE_DEV_MODE true'
          
          - script: |
              echo "Testing Azure Cosmos DB connection and endpoints..."
              sleep 150
              
              APP_URL="https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net"
              
              echo "=== TESTING COSMOS DB CONNECTION ==="
              
              # Test database status endpoint
              echo "1. Testing /api/db-status..."
              DB_RESPONSE=$(curl -s "$APP_URL/api/db-status" || echo "ERROR")
              echo "Database status response:"
              echo "$DB_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$DB_RESPONSE"
              
              # Test health endpoint
              echo "2. Testing /api/health..."
              HEALTH_RESPONSE=$(curl -s "$APP_URL/api/health" || echo "ERROR")
              echo "Health response:"
              echo "$HEALTH_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$HEALTH_RESPONSE"
              
              # Test chat history (should work with Cosmos DB)
              echo "3. Testing /api/chat/history..."
              CHAT_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL/api/chat/history" || echo "000")
              echo "Chat history status: $CHAT_STATUS"
              
              # Test farmers stats
              echo "4. Testing /api/farmers/stats..."
              FARMERS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL/api/farmers/stats" || echo "000")
              echo "Farmers stats status: $FARMERS_STATUS"
              
              # Test main app
              echo "5. Testing main application..."
              MAIN_RESPONSE=$(curl -s "$APP_URL" | head -c 100 || echo "No response")
              echo "Main response: $MAIN_RESPONSE"
              
              echo "=== DEPLOYMENT VERIFICATION COMPLETED ==="
              echo "Application URL: $APP_URL"
              echo "Database Status URL: $APP_URL/api/db-status"
              echo "Health Check URL: $APP_URL/api/health"
              
              if [[ "$DB_RESPONSE" == *"connected"* ]]; then
                echo "✅ Azure Cosmos DB: CONNECTED"
              else
                echo "❌ Azure Cosmos DB: CONNECTION FAILED"
              fi
              
              if [[ "$MAIN_RESPONSE" == *"<!DOCTYPE html>"* ]]; then
                echo "✅ Frontend: WORKING"
              else
                echo "❌ Frontend: FAILED"
              fi
            displayName: 'Verify Cosmos DB Connection'
