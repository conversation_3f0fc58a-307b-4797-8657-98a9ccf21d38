trigger:
- main

variables:
  azureServiceConnection: 'Qagricare'
  webAppName: 'qagricare'
  resourceGroupName: 'agricare'

pool:
  vmImage: 'ubuntu-latest'

stages:
- stage: Build
  displayName: 'Build with Minimal Cosmos DB'
  jobs:
  - job: BuildApp
    displayName: 'Build Application'
    steps:
    - checkout: self
      clean: true
      fetchDepth: 1
      displayName: 'Checkout Repository'
    
    - task: NodeTool@0
      inputs:
        versionSpec: '22.x'
      displayName: 'Install Node.js'
    
    - script: |
        echo "Building frontend with API URL fixes..."
        cd frontend
        
        # Fix localhost references to use relative URLs
        find src -name "*.js" -o -name "*.jsx" | while read file; do
          if grep -q "localhost\|8000\|8002" "$file"; then
            echo "Fixing: $file"
            sed -i "s|http://localhost:8000/api|/api|g" "$file"
            sed -i "s|localhost:8000|qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net|g" "$file"
            sed -i "s|http://localhost:8002/api|/api|g" "$file"
            sed -i "s|localhost:8002|qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net|g" "$file"
          fi
        done
        
        npm install --legacy-peer-deps
        npm run build
        echo "Frontend build completed"
      displayName: 'Build Frontend'
    
    - script: |
        echo "Installing backend dependencies..."
        cd backend
        npm install
        echo "Backend dependencies installed"
      displayName: 'Install Backend Dependencies'
    
    - script: |
        echo "Preparing application with Cosmos DB..."
        
        # Create deployment directory
        mkdir -p $(Build.ArtifactStagingDirectory)/app
        
        # Copy backend files
        cp -r backend/* $(Build.ArtifactStagingDirectory)/app/
        
        # Copy frontend build to backend public directory
        mkdir -p $(Build.ArtifactStagingDirectory)/app/public
        cp -r frontend/dist/* $(Build.ArtifactStagingDirectory)/app/public/
        
        # Create production .env file with Cosmos DB
        cat > $(Build.ArtifactStagingDirectory)/app/.env << 'EOF'
        NODE_ENV=production
        PORT=8080
        MONGODB_URI=*******************************************************************************************************************************************************************************************************************************************
        DB_NAME=agricare
        JWT_SECRET=agricare-production-secret-2025
        FIREBASE_DEV_MODE=true
        DEFAULT_OTP=123456
        AZURE_OPENAI_ENDPOINT=https://image-gpt4o.openai.azure.com/
        AZURE_OPENAI_API_KEY=demo-key
        FRONTEND_URL=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net
        API_URL=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net/api
        EOF
        
        echo "Application prepared with Cosmos DB"
      displayName: 'Prepare Application'
    
    - script: |
        echo "Minimal route fix for frontend serving..."
        
        cd $(Build.ArtifactStagingDirectory)/app
        
        # Simple route fix using Node.js
        cat > fix-routes.js << 'FIX_EOF'
        const fs = require('fs');
        
        console.log('Applying minimal route fix...');
        let content = fs.readFileSync('server.js', 'utf8');
        
        // Only replace if the conflicting route exists
        if (content.includes('res.json({ status: "ok", message: "API is running')) {
          console.log('Found conflicting route, replacing...');
          
          const oldRoute = `app.get("/", (req, res) => {
          res.json({ status: "ok", message: "API is running 🚀", timestamp: new Date().toISOString() });
        });`;
          
          const newRoute = `// Serve React frontend
        app.use(express.static(path.join(__dirname, 'public')));
        
        app.get("/", (req, res) => {
          res.sendFile(path.join(__dirname, 'public', 'index.html'));
        });
        
        // SPA routing
        app.get('*', (req, res) => {
          if (req.path.startsWith('/api/')) {
            return res.status(404).json({ error: 'API endpoint not found' });
          }
          res.sendFile(path.join(__dirname, 'public', 'index.html'));
        });`;
          
          content = content.replace(oldRoute, newRoute);
          fs.writeFileSync('server.js', content);
          console.log('Route fix applied successfully');
        } else {
          console.log('No conflicting route found - no changes needed');
        }
        FIX_EOF
        
        node fix-routes.js
        echo "Minimal route fix completed"
      displayName: 'Apply Minimal Route Fix'
    
    - script: |
        echo "Fixing authentication middleware..."
        
        cd $(Build.ArtifactStagingDirectory)/app
        
        cat > middleware/auth.js << 'AUTH_EOF'
        const admin = require('../config/firebase.config');
        require('dotenv').config();

        const auth = async (req, res, next) => {
          if (process.env.NODE_ENV === 'development' || process.env.FIREBASE_DEV_MODE === 'true') {
            req.user = {
              id: 'development-user-id',
              uid: 'emulator-uid',
              role: 'TM',
              email: '<EMAIL>',
              name: 'Territory Manager'
            };
            console.log('🔑 Demo mode - Using mock user');
            return next();
          }

          try {
            const authHeader = req.headers.authorization;
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
              return res.status(401).json({ success: false, message: 'No token provided' });
            }

            const token = authHeader.split(' ')[1];
            const decodedToken = await admin.auth().verifyIdToken(token);
            req.user = { ...decodedToken, id: decodedToken.uid };
            next();
          } catch (error) {
            console.error('Auth error:', error);
            return res.status(401).json({ success: false, message: 'Invalid token' });
          }
        };

        module.exports = auth;
        AUTH_EOF
        
        echo "Authentication middleware updated"
      displayName: 'Fix Authentication'
    
    - task: ArchiveFiles@2
      inputs:
        rootFolderOrFile: '$(Build.ArtifactStagingDirectory)/app'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(Build.ArtifactStagingDirectory)/agricare-app.zip'
        replaceExistingArchive: true
      displayName: 'Archive Application'
    
    - task: PublishBuildArtifacts@1
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)'
        ArtifactName: 'drop'
        publishLocation: 'Container'
      displayName: 'Publish Artifacts'

- stage: Deploy
  displayName: 'Deploy with Cosmos DB'
  dependsOn: Build
  jobs:
  - deployment: DeployApp
    displayName: 'Deploy Application'
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureWebApp@1
            displayName: 'Deploy to Azure App Service'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              appType: 'webApp'
              appName: '$(webAppName)'
              resourceGroupName: '$(resourceGroupName)'
              package: '$(Pipeline.Workspace)/drop/agricare-app.zip'
              deploymentMethod: 'zipDeploy'
              appSettings: '-NODE_ENV production -WEBSITE_NODE_DEFAULT_VERSION 22.x -PORT 8080 -FIREBASE_DEV_MODE true'
          
          - script: |
              echo "Testing minimal deployment..."
              sleep 90
              
              APP_URL="https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net"
              
              echo "Testing application..."
              STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL" || echo "000")
              echo "Status: $STATUS"
              
              if [ "$STATUS" = "200" ]; then
                echo "✅ Application is working"
                RESPONSE=$(curl -s "$APP_URL" | head -c 100)
                echo "Response: $RESPONSE"
              else
                echo "❌ Application status: $STATUS"
              fi
              
              echo "Application URL: $APP_URL"
            displayName: 'Verify Deployment'
