trigger:
- main

variables:
  azureServiceConnection: 'Qagricare'
  webAppName: 'qagricare'
  resourceGroupName: 'agricare'

pool:
  vmImage: 'ubuntu-latest'

stages:
- stage: Build
  displayName: 'Build AgriCare'
  jobs:
  - job: BuildApp
    displayName: 'Build Application'
    steps:
    - checkout: self
      clean: true
      fetchDepth: 1
      displayName: 'Checkout Repository'
    
    - task: NodeTool@0
      inputs:
        versionSpec: '22.x'
      displayName: 'Install Node.js'
    
    - script: |
        echo "Building frontend..."
        cd frontend
        npm install --legacy-peer-deps
        npm run build
        echo "Frontend build completed"
      displayName: 'Build Frontend'
    
    - script: |
        echo "Installing backend dependencies..."
        cd backend
        npm install
        echo "Backend dependencies installed"
      displayName: 'Install Backend Dependencies'
    
    - script: |
        echo "Preparing application for deployment..."
        
        # Create deployment directory
        mkdir -p $(Build.ArtifactStagingDirectory)/app
        
        # Copy backend files
        cp -r backend/* $(Build.ArtifactStagingDirectory)/app/
        
        # Copy frontend build to backend public directory
        mkdir -p $(Build.ArtifactStagingDirectory)/app/public
        cp -r frontend/dist/* $(Build.ArtifactStagingDirectory)/app/public/
        
        # Create simple .env file
        cat > $(Build.ArtifactStagingDirectory)/app/.env << 'EOF'
        NODE_ENV=production
        PORT=8080
        MONGODB_URI=mongodb://localhost:27017/agricare
        JWT_SECRET=agricare-secret
        FIREBASE_DEV_MODE=true
        DEFAULT_OTP=123456
        EOF
        
        # Create simple package.json for deployment
        cat > $(Build.ArtifactStagingDirectory)/app/package.json << 'EOF'
        {
          "name": "agricare-backend",
          "version": "1.0.0",
          "main": "server.js",
          "scripts": {
            "start": "node server.js"
          },
          "engines": {
            "node": "22.x"
          }
        }
        EOF
        
        echo "Application prepared successfully"
        echo "Files in app directory:"
        ls -la $(Build.ArtifactStagingDirectory)/app/
        echo "Public directory contents:"
        ls -la $(Build.ArtifactStagingDirectory)/app/public/
      displayName: 'Prepare Application'
    
    - task: ArchiveFiles@2
      inputs:
        rootFolderOrFile: '$(Build.ArtifactStagingDirectory)/app'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(Build.ArtifactStagingDirectory)/agricare-app.zip'
        replaceExistingArchive: true
      displayName: 'Archive Application'
    
    - task: PublishBuildArtifacts@1
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)'
        ArtifactName: 'drop'
        publishLocation: 'Container'
      displayName: 'Publish Artifacts'

- stage: Deploy
  displayName: 'Deploy to Azure'
  dependsOn: Build
  jobs:
  - deployment: DeployApp
    displayName: 'Deploy AgriCare'
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureWebApp@1
            displayName: 'Deploy to Azure App Service'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              appType: 'webApp'
              appName: '$(webAppName)'
              resourceGroupName: '$(resourceGroupName)'
              package: '$(Pipeline.Workspace)/drop/agricare-app.zip'
              deploymentMethod: 'zipDeploy'
              appSettings: '-NODE_ENV production -WEBSITE_NODE_DEFAULT_VERSION 22.x -PORT 8080'
          
          - script: |
              echo "Waiting for application to start..."
              sleep 90
              
              APP_URL="https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net"
              
              echo "Testing application..."
              for i in {1..5}; do
                echo "Attempt $i:"
                STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL" || echo "000")
                echo "Status: $STATUS"
                if [ "$STATUS" != "503" ] && [ "$STATUS" != "000" ]; then
                  echo "Application is responding!"
                  break
                fi
                sleep 30
              done
              
              echo "Final test:"
              curl -s "$APP_URL" | head -c 200 || echo "No response"
              
              echo "Deployment completed"
              echo "Application URL: $APP_URL"
            displayName: 'Verify Deployment'
