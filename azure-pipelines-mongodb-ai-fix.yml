trigger:
- main

variables:
  azureServiceConnection: 'Qagricare'
  webAppName: 'qagricare'
  resourceGroupName: 'agricare'

pool:
  vmImage: 'ubuntu-latest'

stages:
- stage: Build
  displayName: 'MongoDB and AI Fix'
  jobs:
  - job: BuildApp
    displayName: 'Build with MongoDB and AI Fix'
    steps:
    - checkout: self
      clean: true
      fetchDepth: 1
      displayName: 'Checkout Repository'
    
    - task: NodeTool@0
      inputs:
        versionSpec: '22.x'
      displayName: 'Install Node.js'
    
    - script: |
        echo "Building frontend with production API URL fix..."
        cd frontend
        
        # Fix the API URL in frontend to use production URL
        find src -name "*.js" -o -name "*.jsx" | xargs grep -l "localhost:8000" | while read file; do
          echo "Fixing API URL in: $file"
          sed -i 's|http://localhost:8000/api|/api|g' "$file"
          sed -i 's|localhost:8000|qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net|g' "$file"
        done
        
        npm install --legacy-peer-deps
        npm run build
        echo "Frontend build completed with API URL fixes"
      displayName: 'Build Frontend with API URL Fix'
    
    - script: |
        echo "Installing backend dependencies..."
        cd backend
        npm install
        echo "Backend dependencies installed"
      displayName: 'Install Backend Dependencies'
    
    - script: |
        echo "Preparing application with MongoDB and AI fixes..."
        
        # Create deployment directory
        mkdir -p $(Build.ArtifactStagingDirectory)/app
        
        # Copy backend files
        cp -r backend/* $(Build.ArtifactStagingDirectory)/app/
        
        # Copy frontend build to backend public directory
        mkdir -p $(Build.ArtifactStagingDirectory)/app/public
        cp -r frontend/dist/* $(Build.ArtifactStagingDirectory)/app/public/
        
        # Create comprehensive production .env file
        cat > $(Build.ArtifactStagingDirectory)/app/.env << 'EOF'
        NODE_ENV=production
        PORT=8080
        
        # MongoDB Configuration (will be installed)
        MONGODB_URI=mongodb://localhost:27017/agricare
        DB_NAME=agricare
        
        # Authentication
        JWT_SECRET=agricare-production-secret-2025
        FIREBASE_DEV_MODE=true
        DEFAULT_OTP=123456
        
        # Azure OpenAI Configuration (placeholder - will work in demo mode)
        AZURE_OPENAI_ENDPOINT=https://image-gpt4o.openai.azure.com/
        AZURE_OPENAI_API_KEY=placeholder-key
        AZURE_DEPLOYMENT_NAME=gpt-4o
        AZURE_API_VERSION=2024-08-01-preview
        
        # Azure Speech Services (placeholder)
        AZURE_SPEECH_KEY=placeholder-speech-key
        AZURE_SPEECH_REGION=centralindia
        
        # Application URLs
        FRONTEND_URL=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net
        API_URL=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net/api
        EOF
        
        echo "Environment configuration created"
      displayName: 'Prepare Application'
    
    - script: |
        echo "Creating MongoDB installation script..."
        
        cd $(Build.ArtifactStagingDirectory)/app
        
        # Create MongoDB installation script
        cat > install-mongodb.sh << 'MONGODB_EOF'
        #!/bin/bash
        set -e
        
        echo "Installing MongoDB for AgriCare..."
        
        # Update system
        sudo apt-get update -y
        
        # Install MongoDB
        sudo apt-get install -y wget gnupg
        wget -qO - https://www.mongodb.org/static/pgp/server-7.0.asc | sudo apt-key add -
        echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/7.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-7.0.list
        sudo apt-get update -y
        sudo apt-get install -y mongodb-org
        
        # Create data directory
        sudo mkdir -p /home/<USER>/mongodb
        sudo chown -R mongodb:mongodb /home/<USER>/mongodb
        
        # Configure MongoDB
        sudo tee /etc/mongod.conf > /dev/null << 'MONGOD_CONFIG'
        storage:
          dbPath: /home/<USER>/mongodb
          journal:
            enabled: true
        
        systemLog:
          destination: file
          logAppend: true
          path: /var/log/mongodb/mongod.log
        
        net:
          port: 27017
          bindIp: 127.0.0.1
        
        processManagement:
          fork: true
          pidFilePath: /var/run/mongod.pid
        MONGOD_CONFIG
        
        # Start MongoDB
        sudo systemctl enable mongod
        sudo systemctl start mongod
        
        # Wait for MongoDB to start
        sleep 10
        
        # Initialize database
        mongosh agricare --eval "
          db.createCollection('users');
          db.createCollection('aiconversations');
          db.createCollection('imageanalyses');
          db.createCollection('chathistory');
          print('AgriCare database initialized');
        "
        
        echo "MongoDB installation completed"
        MONGODB_EOF
        
        chmod +x install-mongodb.sh
        echo "MongoDB installation script created"
      displayName: 'Create MongoDB Installation'
    
    - script: |
        echo "Fixing AI routes for demo mode..."
        
        cd $(Build.ArtifactStagingDirectory)/app
        
        # Create fallback AI responses for when Azure OpenAI is not available
        cat > routes/ai-fallback.js << 'AI_FALLBACK_EOF'
        // Fallback AI responses when Azure OpenAI is not available
        const fallbackResponses = {
          weather: "Based on the current weather conditions, I recommend monitoring soil moisture levels and adjusting irrigation accordingly. The temperature and humidity levels look suitable for most crops.",
          soil: "Your soil health parameters look good. Consider regular testing and organic matter addition to maintain optimal growing conditions.",
          pest: "For pest management, I recommend regular field inspections and integrated pest management practices. Monitor for early signs of pest activity.",
          general: "I'm here to help with your farming questions! While I'm running in demo mode, I can still provide general agricultural guidance based on best practices."
        };
        
        function getFallbackResponse(message, farmData) {
          const msg = message.toLowerCase();
          
          if (msg.includes('weather') || msg.includes('rain') || msg.includes('temperature')) {
            return fallbackResponses.weather;
          } else if (msg.includes('soil') || msg.includes('fertilizer') || msg.includes('nutrient')) {
            return fallbackResponses.soil;
          } else if (msg.includes('pest') || msg.includes('insect') || msg.includes('disease')) {
            return fallbackResponses.pest;
          } else {
            return fallbackResponses.general;
          }
        }
        
        module.exports = { getFallbackResponse };
        AI_FALLBACK_EOF
        
        echo "AI fallback responses created"
      displayName: 'Create AI Fallback'

    - script: |
        echo "Modifying AI routes to handle missing Azure OpenAI..."

        cd $(Build.ArtifactStagingDirectory)/app

        # Modify ai.js to handle missing Azure OpenAI gracefully
        python3 << 'PYTHON_EOF'
        import re

        # Read the ai.js file
        with open('routes/ai.js', 'r') as f:
            content = f.read()

        # Add fallback import at the top
        content = content.replace(
            "const auth = require('../middleware/auth');",
            "const auth = require('../middleware/auth');\nconst { getFallbackResponse } = require('./ai-fallback');"
        )

        # Modify the chat endpoint to handle Azure OpenAI errors
        chat_pattern = r'(// Get AI response\s+const completion = await openai\.createChatCompletion\({[^}]+}\);[^}]+const aiResponse = completion\.data\.choices\[0\]\.message\.content;)'

        fallback_code = '''// Get AI response
        let aiResponse;
        try {
          if (!openai) {
            throw new Error('Azure OpenAI not available - using fallback');
          }
          const completion = await openai.createChatCompletion({
            model: deploymentId,
            messages,
            temperature: 0.7,
            max_tokens: 500
          });
          aiResponse = completion.data.choices[0].message.content;
        } catch (error) {
          console.log('Azure OpenAI error, using fallback response:', error.message);
          aiResponse = getFallbackResponse(message, farmData);
        }'''

        content = re.sub(chat_pattern, fallback_code, content, flags=re.DOTALL)

        # Write back to file
        with open('routes/ai.js', 'w') as f:
            f.write(content)

        print("✅ AI routes updated with fallback handling")
        PYTHON_EOF

        echo "AI routes updated for demo mode"
      displayName: 'Update AI Routes'

    - script: |
        echo "Creating startup script with MongoDB..."

        cd $(Build.ArtifactStagingDirectory)/app

        # Create comprehensive startup script
        cat > startup.sh << 'STARTUP_EOF'
        #!/bin/bash
        set -e

        echo "Starting AgriCare 1.0 with MongoDB..."

        # Install MongoDB if not present
        if ! command -v mongod &> /dev/null; then
          echo "Installing MongoDB..."
          sudo bash install-mongodb.sh
        else
          echo "MongoDB already installed, starting service..."
          sudo systemctl start mongod || sudo mongod --config /etc/mongod.conf --fork
        fi

        # Wait for MongoDB
        echo "Waiting for MongoDB to be ready..."
        for i in {1..30}; do
          if mongosh --eval "db.adminCommand('ping')" >/dev/null 2>&1; then
            echo "MongoDB is ready"
            break
          fi
          echo "Waiting for MongoDB... ($i/30)"
          sleep 2
        done

        # Start the application
        echo "Starting AgriCare application..."
        export NODE_ENV=production
        export PORT=8080
        node server.js
        STARTUP_EOF

        chmod +x startup.sh
        echo "Startup script created"
      displayName: 'Create Startup Script'

    - script: |
        echo "Adding frontend serving to server.js..."

        cd $(Build.ArtifactStagingDirectory)/app

        # Use Python to fix the server.js route
        python3 << 'PYTHON_EOF'
        import re

        # Read the server.js file
        with open('server.js', 'r') as f:
            content = f.read()

        # Replace the conflicting default route
        old_pattern = r'app\.get\("/", \(req, res\) => \{\s*res\.json\(\{ status: "ok", message: "API is running[^}]+\}\);\s*\}\);'
        new_route = '''// Serve React frontend on root route
        app.use(express.static(path.join(__dirname, 'public')));

        app.get("/", (req, res) => {
          res.sendFile(path.join(__dirname, 'public', 'index.html'));
        });

        // Serve React app for all non-API routes (SPA routing)
        app.get('*', (req, res) => {
          if (req.path.startsWith('/api/')) {
            return res.status(404).json({ error: 'API endpoint not found' });
          }
          res.sendFile(path.join(__dirname, 'public', 'index.html'));
        });'''

        # Replace the pattern
        content = re.sub(old_pattern, new_route, content, flags=re.DOTALL)

        # Write back to file
        with open('server.js', 'w') as f:
            f.write(content)

        print("✅ Server.js updated successfully")
        PYTHON_EOF

        echo "Frontend serving configured"
      displayName: 'Configure Frontend Serving'

    - task: ArchiveFiles@2
      inputs:
        rootFolderOrFile: '$(Build.ArtifactStagingDirectory)/app'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(Build.ArtifactStagingDirectory)/agricare-app.zip'
        replaceExistingArchive: true
      displayName: 'Archive Application'

    - task: PublishBuildArtifacts@1
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)'
        ArtifactName: 'drop'
        publishLocation: 'Container'
      displayName: 'Publish Artifacts'

- stage: Deploy
  displayName: 'Deploy with MongoDB and AI'
  dependsOn: Build
  jobs:
  - deployment: DeployApp
    displayName: 'Deploy Complete Application'
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureWebApp@1
            displayName: 'Deploy to Azure App Service'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              appType: 'webApp'
              appName: '$(webAppName)'
              resourceGroupName: '$(resourceGroupName)'
              package: '$(Pipeline.Workspace)/drop/agricare-app.zip'
              startUpCommand: 'cd /home/<USER>/wwwroot && chmod +x startup.sh && ./startup.sh'
              deploymentMethod: 'zipDeploy'
              appSettings: '-NODE_ENV production -WEBSITE_NODE_DEFAULT_VERSION 22.x -PORT 8080 -FIREBASE_DEV_MODE true -MONGODB_URI mongodb://localhost:27017/agricare'

          - script: |
              echo "Testing complete application..."
              sleep 180

              APP_URL="https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net"

              echo "Testing API endpoints..."

              # Test chat history
              echo "Testing /api/chat/history..."
              CHAT_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL/api/chat/history" || echo "000")
              echo "Chat history status: $CHAT_STATUS"

              # Test AI chat
              echo "Testing /api/ai/chat..."
              AI_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL/api/ai/chat" || echo "000")
              echo "AI chat status: $AI_STATUS"

              # Test main app
              echo "Testing main application..."
              MAIN_RESPONSE=$(curl -s "$APP_URL" | head -c 100 || echo "No response")
              echo "Main response: $MAIN_RESPONSE"

              echo "Complete deployment verification completed"
              echo "Application URL: $APP_URL"
              echo "MongoDB and AI features should now work!"
            displayName: 'Verify Complete Deployment'
