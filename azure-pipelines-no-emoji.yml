trigger:
- main

variables:
  azureServiceConnection: 'Qagricare'
  webAppName: 'qagricare'
  resourceGroupName: 'agricare'
  vmImageName: 'ubuntu-latest'
  buildConfiguration: 'production'
  nodeVersion: '22.x'

stages:
- stage: PreValidation
  displayName: 'Pre-Deployment Validation and Analysis'
  jobs:
  - job: ValidateEnvironment
    displayName: 'Comprehensive Environment Validation'
    pool:
      vmImage: $(vmImageName)
    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: '$(nodeVersion)'
      displayName: 'Install Node.js $(nodeVersion)'
    
    - script: |
        echo "COMPREHENSIVE PRE-DEPLOYMENT VALIDATION"
        echo "=============================================="
        echo "Build Date: $(date)"
        echo "Build Agent: $(Agent.Name)"
        echo "Agent OS: $(Agent.OS)"
        echo "Source Directory: $(Build.SourcesDirectory)"
        echo "Artifact Directory: $(Build.ArtifactStagingDirectory)"
        echo "Node Version: $(node --version)"
        echo "NPM Version: $(npm --version)"
        echo ""
        
        echo "VALIDATING PROJECT STRUCTURE..."
        
        # Backend validation
        echo "Backend validation:"
        if [ -d "backend" ]; then
          echo "Backend directory exists"
          echo "   Backend size: $(du -sh backend/)"
          echo "   Backend files: $(find backend -type f | wc -l)"
          
          if [ -f "backend/server.js" ]; then
            echo "server.js found ($(wc -l < backend/server.js) lines)"
          else
            echo "server.js NOT found"; exit 1
          fi
          
          if [ -f "backend/package.json" ]; then
            echo "Backend package.json found"
            echo "   Dependencies: $(node -p "Object.keys(require('./backend/package.json').dependencies || {}).length")"
          else
            echo "Backend package.json NOT found"; exit 1
          fi
          
          if [ -f "backend/.env" ]; then
            echo "Backend .env found"
            echo "   Environment variables: $(grep -c "=" backend/.env)"
            echo "   Azure services: $(grep -c "AZURE" backend/.env)"
            echo "   MongoDB config: $(grep -c "MONGODB" backend/.env)"
          else
            echo "Backend .env NOT found - will create from template"
            echo "   This is normal for Git repositories (.env files are usually ignored)"
          fi
        else
          echo "Backend directory NOT found"; exit 1
        fi
        
        echo ""
        # Frontend validation
        echo "Frontend validation:"
        if [ -d "frontend" ]; then
          echo "Frontend directory exists"
          echo "   Frontend size: $(du -sh frontend/)"
          
          if [ -f "frontend/package.json" ]; then
            echo "Frontend package.json found"
            echo "   Dependencies: $(node -p "Object.keys(require('./frontend/package.json').dependencies || {}).length")"
          else
            echo "Frontend package.json NOT found"; exit 1
          fi
          
          if [ -f "frontend/.env" ]; then
            echo "Frontend .env found"
            echo "   Environment variables: $(grep -c "=" frontend/.env)"
          else
            echo "Frontend .env NOT found - will create from template"
            echo "   This is normal for Git repositories (.env files are usually ignored)"
          fi
        else
          echo "Frontend directory NOT found"; exit 1
        fi
        
        echo ""
        echo "ALL VALIDATIONS PASSED - PROCEEDING TO BUILD"
      displayName: 'Validate Project Structure and Dependencies'

- stage: Build
  displayName: 'Build AgriCare 1.0 Application'
  dependsOn: PreValidation
  jobs:
  - job: BuildApplication
    displayName: 'Build Frontend and Prepare Backend'
    pool:
      vmImage: $(vmImageName)
    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: '$(nodeVersion)'
      displayName: 'Install Node.js'
    
    - script: |
        echo "BUILDING REAL AGRICARE 1.0 FRONTEND"
        echo "======================================="
        echo "Build started: $(date)"
        echo ""
        
        echo "INSTALLING FRONTEND DEPENDENCIES..."
        cd frontend
        npm install --legacy-peer-deps --verbose
        if [ $? -eq 0 ]; then
          echo "Frontend dependencies installed successfully"
          echo "   node_modules size: $(du -sh node_modules/)"
        else
          echo "Frontend dependency installation failed"; exit 1
        fi
        
        echo ""
        echo "BUILDING REACT APPLICATION..."
        npm run build --verbose
        if [ $? -eq 0 ]; then
          echo "Frontend build completed successfully"
          echo "   Build size: $(du -sh dist/)"
          echo "   Build files: $(find dist -type f | wc -l)"
        else
          echo "Frontend build failed"; exit 1
        fi
        cd ..
        
        echo ""
        echo "FRONTEND BUILD COMPLETED"
      displayName: 'Build React Frontend'
