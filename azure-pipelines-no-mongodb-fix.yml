trigger:
- main

variables:
  azureServiceConnection: 'Qagricare'
  webAppName: 'qagricare'
  resourceGroupName: 'agricare'

pool:
  vmImage: 'ubuntu-latest'

stages:
- stage: Build
  displayName: 'No MongoDB Fix Build'
  jobs:
  - job: BuildApp
    displayName: 'Build with MongoDB Fallback'
    steps:
    - checkout: self
      clean: true
      fetchDepth: 1
      displayName: 'Checkout Repository'
    
    - task: NodeTool@0
      inputs:
        versionSpec: '22.x'
      displayName: 'Install Node.js'
    
    - script: |
        echo "=== COMPREHENSIVE API URL FIX ==="
        cd frontend
        
        echo "Fixing all API URLs to use production endpoints..."
        
        # Fix all localhost references to use relative URLs
        find src -name "*.js" -o -name "*.jsx" | while read file; do
          if grep -q "localhost\|8000\|8002" "$file"; then
            echo "Fixing: $file"
            sed -i "s|http://localhost:8000/api|/api|g" "$file"
            sed -i "s|http://localhost:8000|https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net|g" "$file"
            sed -i "s|localhost:8000|qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net|g" "$file"
            sed -i "s|http://localhost:8002/api|/api|g" "$file"
            sed -i "s|http://localhost:8002|https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net|g" "$file"
            sed -i "s|localhost:8002|qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net|g" "$file"
          fi
        done
        
        npm install --legacy-peer-deps
        npm run build
        echo "Frontend build completed with API URL fixes"
      displayName: 'Build Frontend with API URL Fix'
    
    - script: |
        echo "Installing backend dependencies..."
        cd backend
        npm install
        echo "Backend dependencies installed"
      displayName: 'Install Backend Dependencies'
    
    - script: |
        echo "Preparing application with MongoDB fallback..."
        
        # Create deployment directory
        mkdir -p $(Build.ArtifactStagingDirectory)/app
        
        # Copy backend files
        cp -r backend/* $(Build.ArtifactStagingDirectory)/app/
        
        # Copy frontend build to backend public directory
        mkdir -p $(Build.ArtifactStagingDirectory)/app/public
        cp -r frontend/dist/* $(Build.ArtifactStagingDirectory)/app/public/
        
        # Create production .env file
        cat > $(Build.ArtifactStagingDirectory)/app/.env << 'EOF'
        NODE_ENV=production
        PORT=8080
        
        # MongoDB Configuration (will fallback to mock data)
        MONGODB_URI=mongodb://localhost:27017/agricare
        DB_NAME=agricare
        USE_MOCK_DATA=true
        
        # Authentication
        JWT_SECRET=agricare-production-secret-2025
        FIREBASE_DEV_MODE=true
        DEFAULT_OTP=123456
        
        # Azure OpenAI Configuration (demo mode)
        AZURE_OPENAI_ENDPOINT=https://image-gpt4o.openai.azure.com/
        AZURE_OPENAI_API_KEY=demo-key
        AZURE_DEPLOYMENT_NAME=gpt-4o
        AZURE_API_VERSION=2024-08-01-preview
        
        # Application URLs
        FRONTEND_URL=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net
        API_URL=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net/api
        EOF
        
        echo "Application prepared with fallback configuration"
      displayName: 'Prepare Application'
    
    - script: |
        echo "Creating MongoDB fallback system..."
        
        cd $(Build.ArtifactStagingDirectory)/app
        
        # Create mock database service
        cat > services/mockDatabase.js << 'MOCK_DB_EOF'
        // Mock database service for when MongoDB is not available
        
        const mockData = {
          chatHistory: [
            {
              _id: 'mock-chat-1',
              userId: 'development-user-id',
              messages: [
                { role: 'user', content: 'Hello, how is my farm doing?' },
                { role: 'assistant', content: 'Your farm is doing well! Based on current conditions, everything looks healthy.' }
              ],
              createdAt: new Date(),
              updatedAt: new Date()
            }
          ],
          
          imageAnalyses: [
            {
              _id: 'mock-analysis-1',
              userId: 'development-user-id',
              imageUrl: '/placeholder-crop.jpg',
              analysis: 'Healthy crop detected with good growth patterns.',
              confidence: 0.95,
              createdAt: new Date()
            }
          ],
          
          farmers: [
            {
              _id: 'mock-farmer-1',
              name: 'Demo Farmer',
              email: '<EMAIL>',
              phone: '+91-**********',
              location: 'Karnataka, India',
              farmSize: '5 acres',
              crops: ['Rice', 'Wheat'],
              createdAt: new Date()
            }
          ]
        };
        
        class MockDatabase {
          constructor() {
            console.log('🔄 Using mock database service');
          }
          
          async find(collection, query = {}) {
            console.log(`Mock DB: Finding in ${collection}`, query);
            return mockData[collection] || [];
          }
          
          async findOne(collection, query = {}) {
            console.log(`Mock DB: Finding one in ${collection}`, query);
            const items = mockData[collection] || [];
            return items[0] || null;
          }
          
          async create(collection, data) {
            console.log(`Mock DB: Creating in ${collection}`, data);
            const newItem = {
              _id: `mock-${Date.now()}`,
              ...data,
              createdAt: new Date(),
              updatedAt: new Date()
            };
            
            if (!mockData[collection]) {
              mockData[collection] = [];
            }
            mockData[collection].push(newItem);
            return newItem;
          }
          
          async update(collection, query, data) {
            console.log(`Mock DB: Updating in ${collection}`, query, data);
            return { acknowledged: true, modifiedCount: 1 };
          }
          
          async delete(collection, query) {
            console.log(`Mock DB: Deleting from ${collection}`, query);
            return { acknowledged: true, deletedCount: 1 };
          }
          
          async count(collection, query = {}) {
            console.log(`Mock DB: Counting in ${collection}`, query);
            return (mockData[collection] || []).length;
          }
        }
        
        module.exports = new MockDatabase();
        MOCK_DB_EOF
        
        echo "Mock database service created"
      displayName: 'Create Mock Database'
    
    - script: |
        echo "Modifying database connection to use fallback..."
        
        cd $(Build.ArtifactStagingDirectory)/app
        
        # Modify database.js to handle MongoDB connection failures gracefully
        cat > config/database.js << 'DB_CONFIG_EOF'
        const mongoose = require('mongoose');
        const mockDatabase = require('../services/mockDatabase');
        require('dotenv').config();
        
        let isMongoConnected = false;
        let useMockData = false;
        
        const connectDB = async () => {
            try {
                console.log('🔄 Attempting to connect to MongoDB...');
                
                const options = {
                    useNewUrlParser: true,
                    useUnifiedTopology: true,
                    serverSelectionTimeoutMS: 5000, // Reduced timeout
                    socketTimeoutMS: 10000,
                };
        
                const conn = await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/agricare', options);
                console.log(`✅ MongoDB Connected: ${conn.connection.host}`);
                console.log(`🗄️ Database: ${conn.connection.name}`);
                isMongoConnected = true;
                useMockData = false;
            } catch (error) {
                console.error(`❌ MongoDB Connection Error: ${error.message}`);
                console.log('🔄 MongoDB not available, using mock data service...');
                isMongoConnected = false;
                useMockData = true;
                
                // Don't exit - continue with mock data
                console.log('✅ Application will continue with mock data');
            }
        };
        
        const getDatabase = () => {
            if (useMockData || process.env.USE_MOCK_DATA === 'true') {
                return mockDatabase;
            }
            return mongoose;
        };
        
        const isConnected = () => isMongoConnected;
        const isMockMode = () => useMockData || process.env.USE_MOCK_DATA === 'true';
        
        module.exports = { connectDB, getDatabase, isConnected, isMockMode };
        DB_CONFIG_EOF
        
        echo "Database connection updated with fallback"
      displayName: 'Update Database Connection'
