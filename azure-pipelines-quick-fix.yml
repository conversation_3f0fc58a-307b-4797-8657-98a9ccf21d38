trigger:
- main

variables:
  azureServiceConnection: 'Qagricare'
  webAppName: 'qagricare'
  resourceGroupName: 'agricare'

pool:
  vmImage: 'ubuntu-latest'

stages:
- stage: Build
  displayName: 'Quick Fix Build'
  jobs:
  - job: BuildApp
    displayName: 'Build and Fix Application'
    steps:
    - checkout: self
      clean: true
      fetchDepth: 1
      displayName: 'Checkout Repository'
    
    - task: NodeTool@0
      inputs:
        versionSpec: '22.x'
      displayName: 'Install Node.js'
    
    - script: |
        echo "Building frontend..."
        cd frontend
        npm install --legacy-peer-deps
        npm run build
        echo "Frontend build completed"
      displayName: 'Build Frontend'
    
    - script: |
        echo "Installing backend dependencies..."
        cd backend
        npm install
        echo "Backend dependencies installed"
      displayName: 'Install Backend Dependencies'
    
    - script: |
        echo "Preparing application with simple fix..."
        
        # Create deployment directory
        mkdir -p $(Build.ArtifactStagingDirectory)/app
        
        # Copy backend files
        cp -r backend/* $(Build.ArtifactStagingDirectory)/app/
        
        # Copy frontend build to backend public directory
        mkdir -p $(Build.ArtifactStagingDirectory)/app/public
        cp -r frontend/dist/* $(Build.ArtifactStagingDirectory)/app/public/
        
        # Create production .env file
        cat > $(Build.ArtifactStagingDirectory)/app/.env << 'EOF'
        NODE_ENV=production
        PORT=8080
        MONGODB_URI=mongodb://localhost:27017/agricare
        JWT_SECRET=agricare-production-secret
        FIREBASE_DEV_MODE=true
        DEFAULT_OTP=123456
        EOF
        
        echo "Application prepared successfully"
      displayName: 'Prepare Application'
    
    - script: |
        echo "Adding simple frontend serving to server.js..."
        
        # Find the line with the conflicting route and replace it
        cd $(Build.ArtifactStagingDirectory)/app
        
        # Create a simple replacement for the default route
        python3 << 'PYTHON_EOF'
        import re
        
        # Read the server.js file
        with open('server.js', 'r') as f:
            content = f.read()
        
        # Replace the conflicting default route
        old_pattern = r'app\.get\("/", \(req, res\) => \{\s*res\.json\(\{ status: "ok", message: "API is running[^}]+\}\);\s*\}\);'
        new_route = '''// Serve React frontend on root route
        app.use(express.static(path.join(__dirname, 'public')));
        
        app.get("/", (req, res) => {
          res.sendFile(path.join(__dirname, 'public', 'index.html'));
        });
        
        // Serve React app for all non-API routes (SPA routing)
        app.get('*', (req, res) => {
          if (req.path.startsWith('/api/')) {
            return res.status(404).json({ error: 'API endpoint not found' });
          }
          res.sendFile(path.join(__dirname, 'public', 'index.html'));
        });'''
        
        # Replace the pattern
        content = re.sub(old_pattern, new_route, content, flags=re.DOTALL)
        
        # Write back to file
        with open('server.js', 'w') as f:
            f.write(content)
        
        print("✅ Server.js updated successfully")
        PYTHON_EOF
        
        echo "Frontend serving added to server.js"
      displayName: 'Fix Frontend Serving'
    
    - task: ArchiveFiles@2
      inputs:
        rootFolderOrFile: '$(Build.ArtifactStagingDirectory)/app'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(Build.ArtifactStagingDirectory)/agricare-app.zip'
        replaceExistingArchive: true
      displayName: 'Archive Application'
    
    - task: PublishBuildArtifacts@1
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)'
        ArtifactName: 'drop'
        publishLocation: 'Container'
      displayName: 'Publish Artifacts'

- stage: Deploy
  displayName: 'Deploy to Azure'
  dependsOn: Build
  jobs:
  - deployment: DeployApp
    displayName: 'Deploy Fixed Application'
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureWebApp@1
            displayName: 'Deploy to Azure App Service'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              appType: 'webApp'
              appName: '$(webAppName)'
              resourceGroupName: '$(resourceGroupName)'
              package: '$(Pipeline.Workspace)/drop/agricare-app.zip'
              deploymentMethod: 'zipDeploy'
              appSettings: '-NODE_ENV production -WEBSITE_NODE_DEFAULT_VERSION 22.x -PORT 8080'
          
          - script: |
              echo "Testing fixed application..."
              sleep 90
              
              APP_URL="https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net"
              
              echo "Testing main application..."
              RESPONSE=$(curl -s "$APP_URL" | head -c 100 || echo "No response")
              echo "Response: $RESPONSE"
              
              if [[ "$RESPONSE" == *"<!DOCTYPE html>"* ]]; then
                echo "✅ SUCCESS: Frontend is now serving HTML!"
              else
                echo "❌ Still not serving frontend properly"
              fi
              
              echo "Application URL: $APP_URL"
            displayName: 'Verify Fix'
