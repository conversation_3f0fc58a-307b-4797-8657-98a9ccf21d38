trigger:
- main

variables:
  azureServiceConnection: 'Qagricare'
  webAppName: 'qagricare'
  resourceGroupName: 'agricare'

pool:
  vmImage: 'ubuntu-latest'

stages:
- stage: Build
  displayName: 'Route Fix Build'
  jobs:
  - job: BuildApp
    displayName: 'Build with Route Fix'
    steps:
    - checkout: self
      clean: true
      fetchDepth: 1
      displayName: 'Checkout Repository'
    
    - task: NodeTool@0
      inputs:
        versionSpec: '22.x'
      displayName: 'Install Node.js'
    
    - script: |
        echo "Building frontend with API URL fixes..."
        cd frontend
        
        # Fix all localhost references
        find src -name "*.js" -o -name "*.jsx" | while read file; do
          if grep -q "localhost\|8000\|8002" "$file"; then
            echo "Fixing: $file"
            sed -i "s|http://localhost:8000/api|/api|g" "$file"
            sed -i "s|http://localhost:8000|https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net|g" "$file"
            sed -i "s|localhost:8000|qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net|g" "$file"
            sed -i "s|http://localhost:8002/api|/api|g" "$file"
            sed -i "s|http://localhost:8002|https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net|g" "$file"
            sed -i "s|localhost:8002|qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net|g" "$file"
          fi
        done
        
        npm install --legacy-peer-deps
        npm run build
        echo "Frontend build completed"
      displayName: 'Build Frontend'
    
    - script: |
        echo "Installing backend dependencies..."
        cd backend
        npm install
        echo "Backend dependencies installed"
      displayName: 'Install Backend Dependencies'
    
    - script: |
        echo "Preparing application..."
        
        # Create deployment directory
        mkdir -p $(Build.ArtifactStagingDirectory)/app
        
        # Copy backend files
        cp -r backend/* $(Build.ArtifactStagingDirectory)/app/
        
        # Copy frontend build to backend public directory
        mkdir -p $(Build.ArtifactStagingDirectory)/app/public
        cp -r frontend/dist/* $(Build.ArtifactStagingDirectory)/app/public/
        
        # Create production .env file with Azure Cosmos DB
        cat > $(Build.ArtifactStagingDirectory)/app/.env << 'EOF'
        NODE_ENV=production
        PORT=8080
        
        # Azure Cosmos DB MongoDB API Configuration
        MONGODB_URI=*******************************************************************************************************************************************************************************************************************************************
        DB_NAME=agricare
        
        # Authentication
        JWT_SECRET=agricare-production-secret-2025
        FIREBASE_DEV_MODE=true
        DEFAULT_OTP=123456
        
        # Azure OpenAI Configuration
        AZURE_OPENAI_ENDPOINT=https://image-gpt4o.openai.azure.com/
        AZURE_OPENAI_API_KEY=demo-key
        AZURE_DEPLOYMENT_NAME=gpt-4o
        AZURE_API_VERSION=2024-08-01-preview
        
        # Application URLs
        FRONTEND_URL=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net
        API_URL=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net/api
        EOF
        
        echo "Application prepared"
      displayName: 'Prepare Application'
    
    - script: |
        echo "Fixing server.js route conflict..."
        
        cd $(Build.ArtifactStagingDirectory)/app
        
        # Create a simple Node.js script to fix the route
        cat > fix-routes.js << 'FIX_SCRIPT_EOF'
        const fs = require('fs');
        
        console.log('Reading server.js...');
        let content = fs.readFileSync('server.js', 'utf8');
        
        console.log('Original content length:', content.length);
        
        // Find and replace the conflicting default route
        const oldRoute = `app.get("/", (req, res) => {
          res.json({ status: "ok", message: "API is running 🚀", timestamp: new Date().toISOString() });
        });`;
        
        const newRoute = `// Serve React frontend on root route
        app.use(express.static(path.join(__dirname, 'public')));
        
        app.get("/", (req, res) => {
          res.sendFile(path.join(__dirname, 'public', 'index.html'));
        });
        
        // Serve React app for all non-API routes (SPA routing)
        app.get('*', (req, res) => {
          if (req.path.startsWith('/api/')) {
            return res.status(404).json({ error: 'API endpoint not found' });
          }
          res.sendFile(path.join(__dirname, 'public', 'index.html'));
        });`;
        
        // Replace the route
        if (content.includes('res.json({ status: "ok", message: "API is running')) {
          console.log('Found conflicting route, replacing...');
          content = content.replace(oldRoute, newRoute);
          console.log('Route replaced successfully');
        } else {
          console.log('Conflicting route not found, adding frontend serving...');
          // Add frontend serving before the health check
          const healthCheckIndex = content.indexOf('// ✅ Health Check Endpoint');
          if (healthCheckIndex > -1) {
            const beforeHealth = content.substring(0, healthCheckIndex);
            const afterHealth = content.substring(healthCheckIndex);
            content = beforeHealth + newRoute + '\n\n' + afterHealth;
          }
        }
        
        console.log('Writing fixed server.js...');
        fs.writeFileSync('server.js', content);
        console.log('Server.js fixed successfully');
        FIX_SCRIPT_EOF
        
        # Run the fix script
        node fix-routes.js
        
        echo "Server.js route conflict fixed"
      displayName: 'Fix Server Routes'
    
    - script: |
        echo "Fixing authentication middleware..."
        
        cd $(Build.ArtifactStagingDirectory)/app
        
        # Fix auth middleware
        cat > middleware/auth.js << 'AUTH_EOF'
        const admin = require('../config/firebase.config');
        require('dotenv').config();

        const auth = async (req, res, next) => {
          // Allow in development mode OR if FIREBASE_DEV_MODE is true
          if (process.env.NODE_ENV === 'development' || process.env.FIREBASE_DEV_MODE === 'true') {
            req.user = {
              id: 'development-user-id',
              uid: 'emulator-uid',
              role: 'TM',
              email: '<EMAIL>',
              name: 'Territory Manager'
            };
            console.log('🔑 Development/Demo mode - Using mock user:', req.user);
            return next();
          }

          try {
            const authHeader = req.headers.authorization;
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
              console.log('❌ No token provided in request');
              return res.status(401).json({
                success: false,
                message: 'No token provided'
              });
            }

            const token = authHeader.split(' ')[1];
            try {
              const decodedToken = await admin.auth().verifyIdToken(token);
              req.user = {
                ...decodedToken,
                id: decodedToken.uid
              };
              console.log('🔑 Authenticated user:', { id: req.user.id, role: req.user.role });
              next();
            } catch (error) {
              console.error('❌ Token verification failed:', error);
              return res.status(401).json({
                success: false,
                message: 'Invalid token'
              });
            }
          } catch (error) {
            console.error('❌ Auth middleware error:', error);
            return res.status(500).json({
              success: false,
              message: 'Internal server error during authentication'
            });
          }
        };

        module.exports = auth;
        AUTH_EOF
        
        echo "Authentication middleware fixed"
      displayName: 'Fix Authentication'
    
    - task: ArchiveFiles@2
      inputs:
        rootFolderOrFile: '$(Build.ArtifactStagingDirectory)/app'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(Build.ArtifactStagingDirectory)/agricare-app.zip'
        replaceExistingArchive: true
      displayName: 'Archive Application'
    
    - task: PublishBuildArtifacts@1
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)'
        ArtifactName: 'drop'
        publishLocation: 'Container'
      displayName: 'Publish Artifacts'

- stage: Deploy
  displayName: 'Deploy Route Fix'
  dependsOn: Build
  jobs:
  - deployment: DeployApp
    displayName: 'Deploy Fixed Routes'
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureWebApp@1
            displayName: 'Deploy to Azure App Service'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              appType: 'webApp'
              appName: '$(webAppName)'
              resourceGroupName: '$(resourceGroupName)'
              package: '$(Pipeline.Workspace)/drop/agricare-app.zip'
              deploymentMethod: 'zipDeploy'
              appSettings: '-NODE_ENV production -WEBSITE_NODE_DEFAULT_VERSION 22.x -PORT 8080 -FIREBASE_DEV_MODE true'
          
          - script: |
              echo "Testing route fixes and database connection..."
              sleep 120
              
              APP_URL="https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net"
              
              echo "=== TESTING API ENDPOINTS ==="
              
              # Test health endpoint (should show database status)
              echo "1. Testing /api/health..."
              HEALTH_RESPONSE=$(curl -s "$APP_URL/api/health" || echo "ERROR")
              echo "Health response: $HEALTH_RESPONSE"
              
              # Test chat history (should work with Cosmos DB)
              echo "2. Testing /api/chat/history..."
              CHAT_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL/api/chat/history" || echo "000")
              echo "Chat history status: $CHAT_STATUS"
              
              # Test farmers stats
              echo "3. Testing /api/farmers/stats..."
              FARMERS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL/api/farmers/stats" || echo "000")
              echo "Farmers stats status: $FARMERS_STATUS"
              
              # Test main app (should serve HTML)
              echo "4. Testing main application..."
              MAIN_RESPONSE=$(curl -s "$APP_URL" | head -c 100 || echo "No response")
              echo "Main response: $MAIN_RESPONSE"
              
              echo "=== DEPLOYMENT VERIFICATION COMPLETED ==="
              echo "Application URL: $APP_URL"
              
              if [[ "$HEALTH_RESPONSE" == *"connected"* ]]; then
                echo "✅ Database connection: WORKING"
              else
                echo "❌ Database connection: FAILED"
              fi
              
              if [[ "$MAIN_RESPONSE" == *"<!DOCTYPE html>"* ]]; then
                echo "✅ Frontend serving: WORKING"
              else
                echo "❌ Frontend serving: FAILED"
              fi
            displayName: 'Verify Route Fix and Database'
