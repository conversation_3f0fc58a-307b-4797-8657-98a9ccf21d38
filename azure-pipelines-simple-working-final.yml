trigger:
- main

variables:
  azureServiceConnection: 'Qagricare'
  webAppName: 'qagricare'
  resourceGroupName: 'agricare'

pool:
  vmImage: 'ubuntu-latest'

stages:
- stage: Build
  displayName: 'Build AgriCare Application'
  jobs:
  - job: BuildApp
    displayName: 'Build and Package Application'
    steps:
    - checkout: self
      clean: true
      fetchDepth: 1
      displayName: 'Checkout Repository'
    
    - task: NodeTool@0
      inputs:
        versionSpec: '22.x'
      displayName: 'Install Node.js'
    
    - script: |
        echo "Building frontend..."
        cd frontend
        npm install --legacy-peer-deps
        npm run build
        echo "Frontend build completed"
        ls -la dist/
      displayName: 'Build Frontend'
    
    - script: |
        echo "Installing backend dependencies..."
        cd backend
        npm install
        echo "Backend dependencies installed"
      displayName: 'Install Backend Dependencies'
    
    - script: |
        echo "Preparing application for deployment..."
        
        # Create deployment directory
        mkdir -p $(Build.ArtifactStagingDirectory)/app
        
        # Copy backend files
        cp -r backend/* $(Build.ArtifactStagingDirectory)/app/
        
        # Copy frontend build to backend public directory
        mkdir -p $(Build.ArtifactStagingDirectory)/app/public
        cp -r frontend/dist/* $(Build.ArtifactStagingDirectory)/app/public/
        
        # Create production .env file
        cat > $(Build.ArtifactStagingDirectory)/app/.env << 'EOF'
        NODE_ENV=production
        PORT=8080
        MONGODB_URI=mongodb://localhost:27017/agricare
        JWT_SECRET=agricare-production-secret
        FIREBASE_DEV_MODE=true
        DEFAULT_OTP=123456
        AZURE_OPENAI_ENDPOINT=https://image-gpt4o.openai.azure.com/
        AZURE_OPENAI_API_KEY=placeholder
        FRONTEND_URL=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net
        EOF
        
        echo "Application prepared successfully"
        echo "Files in app directory:"
        ls -la $(Build.ArtifactStagingDirectory)/app/
        echo "Public directory contents:"
        ls -la $(Build.ArtifactStagingDirectory)/app/public/
      displayName: 'Prepare Application'
    
    - script: |
        echo "Creating simple server modification..."
        
        # Create a simple server wrapper that serves frontend
        cat > $(Build.ArtifactStagingDirectory)/app/app.js << 'EOF'
        const express = require('express');
        const path = require('path');
        
        // Import the original server setup
        const originalApp = require('./server.js');
        
        // Create new app instance
        const app = express();
        
        // Serve static files from public directory
        app.use(express.static(path.join(__dirname, 'public')));
        
        // Use all original API routes
        app.use('/api', originalApp);
        
        // Health check
        app.get('/health', (req, res) => {
          res.json({
            status: 'healthy',
            timestamp: new Date().toISOString(),
            app: 'AgriCare 1.0'
          });
        });
        
        // Serve React app for all other routes
        app.get('*', (req, res) => {
          res.sendFile(path.join(__dirname, 'public', 'index.html'));
        });
        
        const PORT = process.env.PORT || 8080;
        app.listen(PORT, () => {
          console.log(`AgriCare 1.0 running on port ${PORT}`);
        });
        EOF
        
        # Create simple package.json for deployment
        cat > $(Build.ArtifactStagingDirectory)/app/package.json << 'EOF'
        {
          "name": "agricare-app",
          "version": "1.0.0",
          "main": "app.js",
          "scripts": {
            "start": "node app.js"
          },
          "engines": {
            "node": "22.x"
          }
        }
        EOF
        
        echo "Simple server wrapper created"
      displayName: 'Create Server Wrapper'
    
    - task: ArchiveFiles@2
      inputs:
        rootFolderOrFile: '$(Build.ArtifactStagingDirectory)/app'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(Build.ArtifactStagingDirectory)/agricare-app.zip'
        replaceExistingArchive: true
      displayName: 'Archive Application'
    
    - task: PublishBuildArtifacts@1
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)'
        ArtifactName: 'drop'
        publishLocation: 'Container'
      displayName: 'Publish Artifacts'

- stage: Deploy
  displayName: 'Deploy to Azure'
  dependsOn: Build
  jobs:
  - deployment: DeployApp
    displayName: 'Deploy AgriCare'
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureWebApp@1
            displayName: 'Deploy to Azure App Service'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              appType: 'webApp'
              appName: '$(webAppName)'
              resourceGroupName: '$(resourceGroupName)'
              package: '$(Pipeline.Workspace)/drop/agricare-app.zip'
              deploymentMethod: 'zipDeploy'
              appSettings: '-NODE_ENV production -WEBSITE_NODE_DEFAULT_VERSION 22.x -PORT 8080'
          
          - script: |
              echo "Waiting for application to start..."
              sleep 120
              
              APP_URL="https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net"
              
              echo "Testing application endpoints..."
              
              # Test health
              echo "1. Testing health endpoint..."
              HEALTH_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL/health" || echo "000")
              echo "Health status: $HEALTH_STATUS"
              
              # Test main app
              echo "2. Testing main application..."
              MAIN_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL" || echo "000")
              echo "Main app status: $MAIN_STATUS"
              
              # Get actual response
              echo "3. Getting main app response..."
              RESPONSE=$(curl -s "$APP_URL" | head -c 200 || echo "No response")
              echo "Response: $RESPONSE"
              
              echo "Deployment verification completed"
              echo "Application URL: $APP_URL"
            displayName: 'Verify Deployment'
