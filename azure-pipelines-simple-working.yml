trigger:
- main

variables:
  azureServiceConnection: 'Qagricare'
  webAppName: 'qagricare'
  resourceGroupName: 'agricare'

pool:
  vmImage: 'ubuntu-latest'

stages:
- stage: Build
  displayName: 'Build AgriCare Application'
  jobs:
  - job: BuildApp
    displayName: 'Build and Package Application'
    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: '22.x'
      displayName: 'Install Node.js'
    
    - script: |
        echo "Building AgriCare 1.0..."
        
        # Build frontend
        echo "Building frontend..."
        cd frontend
        npm install --legacy-peer-deps
        npm run build
        cd ..
        
        # Prepare application directory
        mkdir -p $(Build.ArtifactStagingDirectory)/app
        
        # Copy backend files
        cp -r backend/* $(Build.ArtifactStagingDirectory)/app/
        
        # Copy frontend build to public directory
        mkdir -p $(Build.ArtifactStagingDirectory)/app/public
        cp -r frontend/dist/* $(Build.ArtifactStagingDirectory)/app/public/
        
        # Create production .env file
        cat > $(Build.ArtifactStagingDirectory)/app/.env << 'EOF'
        NODE_ENV=production
        PORT=8080
        MONGODB_URI=mongodb://localhost:27017/agricare
        JWT_SECRET=agricare-production-secret
        FIREBASE_DEV_MODE=true
        DEFAULT_OTP=123456
        AZURE_OPENAI_ENDPOINT=https://image-gpt4o.openai.azure.com/
        AZURE_OPENAI_API_KEY=placeholder
        FRONTEND_URL=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net
        EOF
        
        # Modify server.js to serve frontend properly
        echo "
        
        // Frontend serving configuration
        const path = require('path');
        
        // Serve static files from public directory
        app.use(express.static(path.join(__dirname, 'public')));
        
        // Health check endpoint
        app.get('/health-check', (req, res) => {
          res.json({ 
            status: 'healthy', 
            timestamp: new Date().toISOString(),
            app: 'AgriCare 1.0'
          });
        });
        
        // Serve React app for all non-API routes
        app.get('*', (req, res) => {
          if (req.path.startsWith('/api/')) {
            return res.status(404).json({ error: 'API endpoint not found' });
          }
          res.sendFile(path.join(__dirname, 'public', 'index.html'));
        });
        " >> $(Build.ArtifactStagingDirectory)/app/server.js
        
        echo "Application prepared successfully"
      displayName: 'Build and Prepare Application'
    
    - task: ArchiveFiles@2
      inputs:
        rootFolderOrFile: '$(Build.ArtifactStagingDirectory)/app'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(Build.ArtifactStagingDirectory)/agricare-app.zip'
        replaceExistingArchive: true
      displayName: 'Archive Application'
    
    - task: PublishBuildArtifacts@1
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)'
        ArtifactName: 'drop'
        publishLocation: 'Container'
      displayName: 'Publish Artifacts'

- stage: Deploy
  displayName: 'Deploy to Azure'
  dependsOn: Build
  jobs:
  - deployment: DeployApp
    displayName: 'Deploy AgriCare to Azure'
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureWebApp@1
            displayName: 'Deploy to Azure App Service'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              appType: 'webApp'
              appName: '$(webAppName)'
              resourceGroupName: '$(resourceGroupName)'
              package: '$(Pipeline.Workspace)/drop/agricare-app.zip'
              deploymentMethod: 'zipDeploy'
              appSettings: '-NODE_ENV production -WEBSITE_NODE_DEFAULT_VERSION 22.x'
          
          - script: |
              echo "Testing deployment..."
              sleep 60
              
              APP_URL="https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net"
              
              echo "Testing health check..."
              HEALTH_RESPONSE=$(curl -s "$APP_URL/health-check" || echo "ERROR")
              echo "Health response: $HEALTH_RESPONSE"
              
              echo "Testing main application..."
              MAIN_RESPONSE=$(curl -s "$APP_URL" || echo "ERROR")
              echo "Main app response length: ${#MAIN_RESPONSE}"
              
              echo "Deployment verification completed"
              echo "Application URL: $APP_URL"
            displayName: 'Verify Deployment'
