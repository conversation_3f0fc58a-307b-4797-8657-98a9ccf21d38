trigger:
- main

variables:
  azureServiceConnection: 'Qagricare'
  webAppName: 'qagricare'
  resourceGroupName: 'agricare'

pool:
  vmImage: 'ubuntu-latest'

stages:
- stage: Build
  displayName: 'Build AgriCare 1.0'
  jobs:
  - job: BuildApp
    displayName: 'Build Application'
    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: '22.x'
      displayName: 'Install Node.js'
    
    - script: |
        echo "Building AgriCare 1.0 Frontend..."
        cd frontend
        npm install --legacy-peer-deps
        npm run build
        cd ..
        echo "Frontend build completed"
      displayName: 'Build Frontend'
    
    - script: |
        echo "Preparing application..."
        mkdir -p $(Build.ArtifactStagingDirectory)/app
        
        # Copy backend
        cp -r backend/* $(Build.ArtifactStagingDirectory)/app/
        
        # Copy frontend build
        mkdir -p $(Build.ArtifactStagingDirectory)/app/public
        cp -r frontend/dist/* $(Build.ArtifactStagingDirectory)/app/public/
        
        # Create .env file
        cat > $(Build.ArtifactStagingDirectory)/app/.env << 'EOF'
        NODE_ENV=production
        PORT=8080
        MONGODB_URI=mongodb://localhost:27017/agricare
        JWT_SECRET=your-jwt-secret
        FIREBASE_DEV_MODE=true
        DEFAULT_OTP=123456
        AZURE_OPENAI_ENDPOINT=https://image-gpt4o.openai.azure.com/
        AZURE_OPENAI_API_KEY=placeholder
        AZURE_DEPLOYMENT_NAME=gpt-4o
        FRONTEND_URL=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net
        EOF
        
        # Add frontend serving to server.js
        echo "
        const path = require('path');
        app.use(express.static(path.join(__dirname, 'public')));
        app.get('/health', (req, res) => {
          res.json({ status: 'healthy', timestamp: new Date().toISOString() });
        });
        app.get('*', (req, res) => {
          if (req.path.startsWith('/api/')) {
            return res.status(404).json({ error: 'API endpoint not found' });
          }
          res.sendFile(path.join(__dirname, 'public', 'index.html'));
        });
        " >> $(Build.ArtifactStagingDirectory)/app/server.js
        
        # Create web.config
        cat > $(Build.ArtifactStagingDirectory)/app/web.config << 'EOF'
        <?xml version="1.0" encoding="utf-8"?>
        <configuration>
          <system.webServer>
            <handlers>
              <add name="iisnode" path="server.js" verb="*" modules="iisnode"/>
            </handlers>
            <rewrite>
              <rules>
                <rule name="API" stopProcessing="true">
                  <match url="^api/.*" />
                  <action type="Rewrite" url="server.js"/>
                </rule>
                <rule name="Health" stopProcessing="true">
                  <match url="^health$" />
                  <action type="Rewrite" url="server.js"/>
                </rule>
                <rule name="Default">
                  <match url=".*" />
                  <conditions>
                    <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="True"/>
                  </conditions>
                  <action type="Rewrite" url="public/index.html"/>
                </rule>
              </rules>
            </rewrite>
            <iisnode node_env="production" />
          </system.webServer>
        </configuration>
        EOF
        
        echo "Application prepared successfully"
      displayName: 'Prepare Application'
    
    - task: ArchiveFiles@2
      inputs:
        rootFolderOrFile: '$(Build.ArtifactStagingDirectory)/app'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(Build.ArtifactStagingDirectory)/agricare-app.zip'
        replaceExistingArchive: true
      displayName: 'Archive Application'
    
    - task: PublishBuildArtifacts@1
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)'
        ArtifactName: 'drop'
        publishLocation: 'Container'
      displayName: 'Publish Artifacts'

- stage: Deploy
  displayName: 'Deploy to Azure'
  dependsOn: Build
  jobs:
  - deployment: DeployApp
    displayName: 'Deploy AgriCare 1.0'
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureWebApp@1
            displayName: 'Deploy to Azure App Service'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              appType: 'webApp'
              appName: '$(webAppName)'
              resourceGroupName: '$(resourceGroupName)'
              package: '$(Pipeline.Workspace)/drop/agricare-app.zip'
              deploymentMethod: 'zipDeploy'
              appSettings: '-NODE_ENV production -WEBSITE_NODE_DEFAULT_VERSION 22.x'
          
          - script: |
              echo "Testing deployment..."
              sleep 60
              APP_URL="https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net"
              
              RESPONSE=$(curl -s "$APP_URL/health" || echo "ERROR")
              if [[ "$RESPONSE" == *"healthy"* ]]; then
                echo "SUCCESS! AgriCare 1.0 is healthy"
              else
                echo "Health check failed: $RESPONSE"
              fi
              
              echo "Deployment completed!"
              echo "Main App: $APP_URL"
              echo "Health Check: $APP_URL/health"
            displayName: 'Verify Deployment'
