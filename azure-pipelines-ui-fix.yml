trigger:
- main

variables:
  azureServiceConnection: 'Qagricare'
  webAppName: 'qagricare'
  resourceGroupName: 'agricare'

pool:
  vmImage: 'ubuntu-latest'

stages:
- stage: Build
  displayName: 'UI Fix Build'
  jobs:
  - job: BuildApp
    displayName: 'Fix UI Visibility'
    steps:
    - checkout: self
      clean: true
      fetchDepth: 1
      displayName: 'Checkout Repository'
    
    - task: NodeTool@0
      inputs:
        versionSpec: '22.x'
      displayName: 'Install Node.js'
    
    - script: |
        echo "Building frontend..."
        cd frontend
        npm install --legacy-peer-deps
        npm run build
        echo "Frontend build completed"
      displayName: 'Build Frontend'
    
    - script: |
        echo "Installing backend dependencies..."
        cd backend
        npm install
        echo "Backend dependencies installed"
      displayName: 'Install Backend Dependencies'
    
    - script: |
        echo "Preparing application with UI fix..."
        
        # Create deployment directory
        mkdir -p $(Build.ArtifactStagingDirectory)/app
        
        # Copy backend files
        cp -r backend/* $(Build.ArtifactStagingDirectory)/app/
        
        # Copy frontend build to backend public directory
        mkdir -p $(Build.ArtifactStagingDirectory)/app/public
        cp -r frontend/dist/* $(Build.ArtifactStagingDirectory)/app/public/
        
        # Create .env file
        cat > $(Build.ArtifactStagingDirectory)/app/.env << 'EOF'
        NODE_ENV=production
        PORT=8080
        MONGODB_URI=*******************************************************************************************************************************************************************************************************************************************
        DB_NAME=agricare
        JWT_SECRET=agricare-production-secret
        FIREBASE_DEV_MODE=true
        DEFAULT_OTP=123456
        EOF
        
        echo "Application prepared"
      displayName: 'Prepare Application'
    
    - script: |
        echo "CRITICAL UI FIX: Fixing route conflict..."
        
        cd $(Build.ArtifactStagingDirectory)/app
        
        # Create route fix script
        cat > fix-ui-routes.js << 'ROUTE_FIX_EOF'
        const fs = require('fs');
        const path = require('path');
        
        console.log('🔧 Fixing UI route conflict...');
        
        let serverContent = fs.readFileSync('server.js', 'utf8');
        console.log('Original server.js length:', serverContent.length);
        
        // Check if the conflicting route exists
        if (serverContent.includes('res.json({ status: "ok", message: "API is running')) {
          console.log('❌ Found conflicting API route - replacing with frontend serving...');
          
          // Replace the conflicting route with frontend serving
          const conflictingRoute = /app\.get\(["']\/["'],.*?res\.json\(\{[^}]*status[^}]*\}\);.*?\}\);/gs;
          
          const frontendRoute = `// Serve React frontend
        app.use(express.static(path.join(__dirname, 'public')));
        
        // Health check endpoint
        app.get('/api/health', (req, res) => {
          res.json({ 
            status: "ok", 
            message: "AgriCare API is running 🚀", 
            timestamp: new Date().toISOString(),
            database: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected'
          });
        });
        
        // Serve React app for all non-API routes
        app.get('*', (req, res) => {
          if (req.path.startsWith('/api/')) {
            return res.status(404).json({ error: 'API endpoint not found' });
          }
          res.sendFile(path.join(__dirname, 'public', 'index.html'));
        });`;
          
          serverContent = serverContent.replace(conflictingRoute, frontendRoute);
          
          // Ensure express.static and path are available
          if (!serverContent.includes('const path = require')) {
            serverContent = `const path = require('path');\n${serverContent}`;
          }
          
          fs.writeFileSync('server.js', serverContent);
          console.log('✅ UI route fix applied successfully');
          console.log('New server.js length:', fs.readFileSync('server.js', 'utf8').length);
        } else {
          console.log('✅ No conflicting route found - server.js is already correct');
        }
        ROUTE_FIX_EOF
        
        node fix-ui-routes.js
        echo "UI route fix completed"
      displayName: 'Fix UI Routes'
    
    - script: |
        echo "Updating authentication middleware..."
        
        cd $(Build.ArtifactStagingDirectory)/app
        
        cat > middleware/auth.js << 'AUTH_EOF'
        const admin = require('../config/firebase.config');
        const userMappingService = require('../services/userMapping.service');
        require('dotenv').config();

        const auth = async (req, res, next) => {
          if (process.env.FIREBASE_DEV_MODE === 'true') {
            req.user = {
              id: 'development-user-id',
              uid: 'emulator-uid',
              role: 'TM',
              email: '<EMAIL>',
              name: 'Manish Kumar',
              phone: '9611966747',
              phoneNumber: '9611966747'
            };
            return next();
          }

          try {
            const authHeader = req.headers.authorization;
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
              return res.status(401).json({ success: false, message: 'No token provided' });
            }

            const token = authHeader.split(' ')[1];
            const decodedToken = await admin.auth().verifyIdToken(token);
            
            const phoneNumber = decodedToken.phone_number || decodedToken.phoneNumber;
            let userDetails = null;
            if (phoneNumber) {
              userDetails = await userMappingService.getUserDetailsByPhone(phoneNumber);
            }
            
            req.user = {
              ...decodedToken,
              id: decodedToken.uid,
              phone: phoneNumber,
              phoneNumber: phoneNumber,
              name: userDetails?.name || decodedToken.name || `User ${phoneNumber?.slice(-4) || 'Unknown'}`,
              role: userDetails?.role || decodedToken.role || 'User'
            };
            
            next();
          } catch (error) {
            return res.status(401).json({ success: false, message: 'Invalid token' });
          }
        };

        module.exports = auth;
        AUTH_EOF
        
        echo "Authentication middleware updated"
      displayName: 'Update Authentication'
    
    - task: ArchiveFiles@2
      inputs:
        rootFolderOrFile: '$(Build.ArtifactStagingDirectory)/app'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(Build.ArtifactStagingDirectory)/agricare-ui-fix.zip'
        replaceExistingArchive: true
      displayName: 'Archive Application'
    
    - task: PublishBuildArtifacts@1
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)'
        ArtifactName: 'drop'
        publishLocation: 'Container'
      displayName: 'Publish Artifacts'

- stage: Deploy
  displayName: 'Deploy UI Fix'
  dependsOn: Build
  jobs:
  - deployment: DeployApp
    displayName: 'Deploy UI Fix'
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureWebApp@1
            displayName: 'Deploy UI Fix'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              appType: 'webApp'
              appName: '$(webAppName)'
              resourceGroupName: '$(resourceGroupName)'
              package: '$(Pipeline.Workspace)/drop/agricare-ui-fix.zip'
              deploymentMethod: 'zipDeploy'
              appSettings: '-NODE_ENV production -WEBSITE_NODE_DEFAULT_VERSION 22.x -PORT 8080 -FIREBASE_DEV_MODE true'
          
          - script: |
              echo "Testing UI fix deployment..."
              sleep 90
              
              APP_URL="https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net"
              
              echo "1. Testing main application..."
              MAIN_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL" || echo "000")
              echo "Main app status: $MAIN_STATUS"
              
              echo "2. Testing content type..."
              CONTENT=$(curl -s "$APP_URL" | head -c 200)
              echo "Content preview: $CONTENT"
              
              echo "3. Testing health endpoint..."
              HEALTH_RESPONSE=$(curl -s "$APP_URL/api/health" || echo "ERROR")
              echo "Health response: $HEALTH_RESPONSE"
              
              echo "4. Testing database status..."
              DB_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL/api/db-status" || echo "000")
              echo "Database status: $DB_STATUS"
              
              if [[ "$CONTENT" == *"<!DOCTYPE html>"* ]] || [[ "$CONTENT" == *"<html"* ]]; then
                echo "✅ UI is now visible - serving HTML content"
              elif [[ "$CONTENT" == *"status"*"ok"* ]]; then
                echo "❌ Still serving API response instead of UI"
              else
                echo "⚠️ Unknown content type"
              fi
              
              echo "🚀 UI FIX DEPLOYMENT COMPLETED"
              echo "Application URL: $APP_URL"
            displayName: 'Verify UI Fix'
