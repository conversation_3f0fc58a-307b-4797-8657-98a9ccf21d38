# Node.js Express Web App to Linux on Azure
# Build AgriCare 1.0 and deploy it to Azure as a Linux web app
# Configured for MongoDB and proper deployment

trigger:
- main

# Add checkout settings to optimize repository operations
resources:
  repositories:
    - repository: self
      type: git
      ref: main
      fetchDepth: 1  # Only fetch the latest commit
      clean: true    # Clean the repository before checkout

variables:
  # Azure Web App name
  webAppName: 'qagricare'
  # Environment name
  environmentName: 'production'
  # Agent VM image name
  vmImageName: 'ubuntu-latest'
  # Node.js version
  nodeVersion: '22.x'
  # Resource group name
  resourceGroupName: 'agricare'
  # Azure service connection
  azureServiceConnection: 'Qagricare'
  # Add Git settings to optimize checkout
  GIT_CLEAN: true
  GIT_FETCH_DEPTH: 1
  GIT_LFS_SKIP_SMUDGE: 1

stages:
- stage: Build
  displayName: Build stage
  jobs:
  - job: Build
    displayName: Build
    pool:
      vmImage: $(vmImageName)
    steps:
    # Use proper Azure DevOps checkout
    - checkout: self
      clean: true
      fetchDepth: 1
      displayName: 'Checkout Repository'

    # Verify repository structure
    - script: |
        echo "Verifying repository structure..."
        echo "Current directory: $(pwd)"
        echo "Build.SourcesDirectory: $(Build.SourcesDirectory)"
        echo "Files in source directory:"
        ls -la $(Build.SourcesDirectory)
        echo "Checking for frontend directory:"
        ls -la $(Build.SourcesDirectory)/frontend || echo "Frontend directory not found"
        echo "Checking for backend directory:"
        ls -la $(Build.SourcesDirectory)/backend || echo "Backend directory not found"
      displayName: 'Verify Repository Structure'

    - task: NodeTool@0
      inputs:
        versionSpec: $(nodeVersion)
      displayName: 'Install Node.js'

    # Install frontend dependencies and build
    - script: |
        echo "Building frontend..."
        cd $(Build.SourcesDirectory)/frontend
        echo "Current directory: $(pwd)"
        echo "Files in directory:"
        ls -la
        echo "Installing dependencies..."
        npm install --legacy-peer-deps
        echo "Building frontend..."
        npm run build
        echo "Build completed. Checking dist directory:"
        ls -la dist/
      displayName: 'Build Frontend'

    # Install backend dependencies
    - script: |
        echo "Installing backend dependencies..."
        cd $(Build.SourcesDirectory)/backend
        echo "Current directory: $(pwd)"
        echo "Files in directory:"
        ls -la
        echo "Installing dependencies..."
        npm install
        echo "Verifying installation..."
        npm list --depth=0
      displayName: 'Install Backend Dependencies'

    # Create and verify environment files
    - script: |
        cd $(Build.SourcesDirectory)
        # Create production environment file
        cat > .env.production << 'EOL'
        NODE_ENV=production
        PORT=8080
        MONGODB_URI=mongodb://localhost:27017/agricare
        FRONTEND_URL=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net
        JWT_SECRET=agricare-production-secret
        FIREBASE_DEV_MODE=true
        DEFAULT_OTP=123456
        AZURE_OPENAI_ENDPOINT=https://image-gpt4o.openai.azure.com/
        AZURE_OPENAI_API_KEY=placeholder
        EOL

        # Create frontend production environment file
        cat > frontend/.env.production << 'EOL'
        VITE_API_URL=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net/api
        VITE_FRONTEND_URL=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net
        EOL

        # Verify environment files
        echo "Backend environment file:"
        cat .env.production
        echo "Frontend environment file:"
        cat frontend/.env.production
      displayName: 'Setup Environment Files'

    # Copy frontend build to backend public directory
    - script: |
        cd $(Build.SourcesDirectory)
        # Create public directory in backend
        mkdir -p backend/public
        # Copy frontend build files
        cp -r frontend/dist/* backend/public/
        echo "Frontend files copied to backend/public"
        ls -la backend/public/
      displayName: 'Copy Frontend to Backend'

    # Create startup script (DO NOT MODIFY server.js)
    - script: |
        cd $(Build.SourcesDirectory)
        cat > startup.sh << 'EOL'
        #!/bin/bash
        set -e

        # Set working directory
        cd /home/<USER>/wwwroot

        # Copy production environment files
        cp .env.production .env
        cp frontend/.env.production frontend/.env

        # Start the application from backend directory
        echo "Starting AgriCare 1.0..."
        cd backend
        node server.js
        EOL
        chmod +x startup.sh
      displayName: 'Create Startup Script'

    # Archive files
    - task: ArchiveFiles@2
      inputs:
        rootFolderOrFile: '$(Build.SourcesDirectory)'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip'
        replaceExistingArchive: true
      displayName: 'Archive Files'

    # Publish build artifacts
    - task: PublishBuildArtifacts@1
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)'
        ArtifactName: 'drop'
        publishLocation: 'Container'
      displayName: 'Publish Artifacts'

- stage: Deploy
  displayName: Deploy stage
  dependsOn: Build
  condition: succeeded()
  jobs:
  - deployment: Deploy
    displayName: Deploy
    environment: $(environmentName)
    pool:
      vmImage: $(vmImageName)
    strategy:
      runOnce:
        deploy:
          steps:
          # Deploy the package
          - task: AzureWebApp@1
            displayName: 'Deploy AgriCare 1.0'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              appType: 'webApp'
              appName: $(webAppName)
              package: '$(Pipeline.Workspace)/drop/$(Build.BuildId).zip'
              startUpCommand: 'cd /home/<USER>/wwwroot && ./startup.sh'
              runtimeStack: 'NODE|22-lts'
              deploymentMethod: 'runFromPackage'
              deploymentTimeoutSec: '1200'
              enableCustomDeployment: true

          # Set app settings using Azure CLI
          - task: AzureCLI@2
            displayName: 'Configure App Settings'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                # Set app settings for AgriCare
                az webapp config appsettings set \
                  --name qagricare \
                  --resource-group agricare \
                  --settings \
                  WEBSITE_NODE_DEFAULT_VERSION=22.x \
                  NODE_ENV=production \
                  PORT=8080 \
                  WEBSITE_LOCAL_CACHE_OPTION=Always \
                  WEBSITE_LOCAL_CACHE_SIZEINMB=1024 \
                  WEBSITE_RUN_FROM_PACKAGE=1 \
                  SCM_DO_BUILD_DURING_DEPLOYMENT=true \
                  WEBSITE_HTTPLOGGING_RETENTION_DAYS=7 \
                  WEBSITE_ENABLE_APP_LOGS=true \
                  FRONTEND_URL=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net \
                  MONGODB_URI=mongodb://localhost:27017/agricare \
                  JWT_SECRET=agricare-production-secret \
                  FIREBASE_DEV_MODE=true \
                  DEFAULT_OTP=123456

          # Verify deployment
          - task: AzureCLI@2
            displayName: 'Verify Deployment'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                echo "Verifying deployment to Azure Web App: qagricare"
                status=$(az webapp show --name qagricare --resource-group agricare --query state -o tsv)
                echo "Web App Status: $status"

                if [ "$status" == "Running" ]; then
                  echo "Web App is running successfully"
                else
                  echo "Web App is not running. Status: $status"
                  exit 1
                fi

                echo "Waiting for application to start..."
                sleep 60

                APP_URL="https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net"

                echo "Testing health endpoint..."
                health_status=$(curl -s -o /dev/null -w "%{http_code}" $APP_URL/health || echo "000")
                if [ "$health_status" == "200" ]; then
                  echo "Health endpoint is accessible (Status: $health_status)"
                else
                  echo "Health endpoint status: $health_status"
                fi

                echo "Testing main application..."
                main_status=$(curl -s -o /dev/null -w "%{http_code}" $APP_URL || echo "000")
                echo "Main application status: $main_status"

                echo "Deployment verification completed"
                echo "Application URL: $APP_URL"
