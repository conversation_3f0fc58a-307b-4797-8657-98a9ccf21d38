trigger:
- main

variables:
  azureServiceConnection: 'Qagricare'
  webAppName: 'qagricare'
  resourceGroupName: 'agricare'

pool:
  vmImage: 'ubuntu-latest'

stages:
- stage: Build
  displayName: 'Build AgriCare 1.0'
  jobs:
  - job: BuildApp
    displayName: 'Build Full Application'
    steps:
    - checkout: self
      clean: true
      fetchDepth: 1
      displayName: 'Checkout Repository'

    - task: NodeTool@0
      inputs:
        versionSpec: '22.x'
      displayName: 'Install Node.js'

    - script: |
        echo "Building frontend with production configuration..."
        cd frontend

        # Copy production environment file
        cp .env.production .env.production.local

        # Install dependencies
        npm install --legacy-peer-deps

        # Build with production environment
        NODE_ENV=production npm run build

        echo "Frontend build completed with production configuration"
      displayName: 'Build Frontend'

    - script: |
        echo "Installing backend dependencies..."
        cd backend
        npm install
        echo "Backend dependencies installed"
      displayName: 'Install Backend Dependencies'

    - script: |
        echo "Preparing AgriCare 1.0 application..."

        # Create deployment directory
        mkdir -p $(Build.ArtifactStagingDirectory)/app

        # Copy backend files (preserve the actual server.js and all routes)
        cp -r backend/* $(Build.ArtifactStagingDirectory)/app/

        # Copy frontend build to backend public directory
        mkdir -p $(Build.ArtifactStagingDirectory)/app/public
        cp -r frontend/dist/* $(Build.ArtifactStagingDirectory)/app/public/

        # Copy environment files
        cp backend/.env $(Build.ArtifactStagingDirectory)/app/.env

        # Update production environment variables
        cat >> $(Build.ArtifactStagingDirectory)/app/.env << 'EOF'
        NODE_ENV=production
        PORT=8080
        MONGODB_URI=*******************************************************************************************************************************************************************************************************************************************
        DB_NAME=agricare
        TZ=Asia/Kolkata
        TIMEZONE=Asia/Kolkata

        # Azure OpenAI Configuration
        AZURE_OPENAI_ENDPOINT=https://image-gpt4o.openai.azure.com/
        AZURE_OPENAI_API_KEY=5kxS162ETTa3VMG7ou9OueTBgTgklpRtE758oSmJp1DyQMV1o7ZaJQQJ99BBACYeBjFXJ3w3AAABACOG5O2J
        AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4o
        AZURE_DEPLOYMENT_NAME=gpt-4o
        AZURE_API_VERSION=2024-08-01-preview

        # Azure Speech Service Configuration
        AZURE_SPEECH_KEY=d133bd0fbec34843afefea5fcbbb1242
        AZURE_SPEECH_REGION=centralindia
        REACT_APP_AZURE_SPEECH_ENDPOINT=https://centralindia.tts.speech.microsoft.com/

        # Firebase Development Mode
        FIREBASE_DEV_MODE=true
        DEFAULT_OTP=123456
        EOF

        # Create database initialization script
        cat > $(Build.ArtifactStagingDirectory)/app/init-database.js << 'INIT_EOF'
        const mongoose = require('mongoose');
        require('dotenv').config();

        // Import models to ensure they're registered
        require('./models/User');
        require('./models/Farmer');
        require('./models/Farm');
        require('./models/ChatHistory');
        require('./models/AIConversation');
        require('./models/WeatherData');
        require('./models/AgriExpert');

        async function initializeDatabase() {
          try {
            console.log('🔄 Connecting to MongoDB...');
            await mongoose.connect(process.env.MONGODB_URI, {
              useNewUrlParser: true,
              useUnifiedTopology: true,
              serverSelectionTimeoutMS: 30000,
              socketTimeoutMS: 45000,
              retryWrites: false,
              ssl: true
            });
            console.log('✅ Connected to MongoDB');

            // Create collections if they don't exist
            const collections = [
              'users', 'farmers', 'farms', 'chathistories',
              'aiconversations', 'weatherdata', 'agriexperts',
              'agriinstitutions', 'researchinstitutes'
            ];

            for (const collectionName of collections) {
              try {
                await mongoose.connection.db.createCollection(collectionName);
                console.log(`✅ Collection '${collectionName}' created/verified`);
              } catch (error) {
                if (error.code === 48) {
                  console.log(`ℹ️ Collection '${collectionName}' already exists`);
                } else {
                  console.error(`❌ Error creating collection '${collectionName}':`, error.message);
                }
              }
            }

            // Create test farmer if not exists
            const Farmer = mongoose.model('Farmer');
            const existingFarmer = await Farmer.findOne({ mobile: '9611966747' });

            if (!existingFarmer) {
              await Farmer.create({
                name: 'Test Farmer',
                mobile: '9611966747',
                aadharNumber: '************',
                panNumber: '**********',
                state: 'Karnataka',
                district: 'Bangalore Rural',
                farmSize: '5 acres',
                cropType: 'Mixed',
                irrigationStatus: 'Well',
                geoLocation: {
                  type: 'Point',
                  coordinates: [77.5946, 12.9716] // Bangalore coordinates
                }
              });
              console.log('✅ Test farmer created');
            } else {
              console.log('ℹ️ Test farmer already exists');
            }

            console.log('🎉 Database initialization completed successfully');
            process.exit(0);
          } catch (error) {
            console.error('❌ Database initialization failed:', error);
            process.exit(1);
          }
        }

        initializeDatabase();
        INIT_EOF

        # Create startup script for Azure App Service
        cat > $(Build.ArtifactStagingDirectory)/app/startup.sh << 'STARTUP_EOF'
        #!/bin/bash
        echo "🚀 Starting AgriCare 1.0 application..."

        # Set timezone
        export TZ=Asia/Kolkata

        # Initialize database (run once, ignore errors if already initialized)
        echo "🔄 Initializing database..."
        node init-database.js || echo "⚠️ Database initialization completed with warnings"

        # Start the main application
        echo "🌾 Starting AgriCare server..."
        node server.js
        STARTUP_EOF

        chmod +x $(Build.ArtifactStagingDirectory)/app/startup.sh

        echo "AgriCare 1.0 application prepared with database initialization"
      displayName: 'Prepare AgriCare Application'
    
    - script: |
        echo "Updating frontend API URLs for production..."

        cd $(Build.ArtifactStagingDirectory)/app/public

        # Update API URLs in the built frontend files
        find . -name "*.js" -type f -exec sed -i 's|http://localhost:8000|https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net|g' {} \;
        find . -name "*.js" -type f -exec sed -i 's|localhost:8000|qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net|g' {} \;

        echo "Frontend API URLs updated for production"
      displayName: 'Update Frontend API URLs'

    - script: |
        echo "Verifying AgriCare 1.0 deployment structure..."

        cd $(Build.ArtifactStagingDirectory)/app

        echo "=== Deployment Structure ==="
        ls -la

        echo "=== Routes Directory ==="
        ls -la routes/

        echo "=== Controllers Directory ==="
        ls -la controllers/

        echo "=== Models Directory ==="
        ls -la models/

        echo "=== Public Directory ==="
        ls -la public/

        echo "=== Server.js Content (first 20 lines) ==="
        head -20 server.js

        echo "=== Environment Variables ==="
        head -10 .env

        echo "AgriCare 1.0 structure verified"
      displayName: 'Verify Deployment Structure'
    
    - task: ArchiveFiles@2
      inputs:
        rootFolderOrFile: '$(Build.ArtifactStagingDirectory)/app'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(Build.ArtifactStagingDirectory)/agricare-full.zip'
        replaceExistingArchive: true
      displayName: 'Archive AgriCare Application'

    - task: PublishBuildArtifacts@1
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)'
        ArtifactName: 'drop'
        publishLocation: 'Container'
      displayName: 'Publish Artifacts'

- stage: Deploy
  displayName: 'Deploy AgriCare 1.0'
  dependsOn: Build
  jobs:
  - deployment: DeployApp
    displayName: 'Deploy Full Application'
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureWebApp@1
            displayName: 'Deploy AgriCare 1.0'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              appType: 'webApp'
              appName: '$(webAppName)'
              resourceGroupName: '$(resourceGroupName)'
              package: '$(Pipeline.Workspace)/drop/agricare-full.zip'
              deploymentMethod: 'zipDeploy'
              appSettings: '-NODE_ENV production -WEBSITE_NODE_DEFAULT_VERSION 22.x -PORT 8080 -TZ Asia/Kolkata -TIMEZONE Asia/Kolkata -AZURE_OPENAI_ENDPOINT https://image-gpt4o.openai.azure.com/ -AZURE_OPENAI_API_KEY 5kxS162ETTa3VMG7ou9OueTBgTgklpRtE758oSmJp1DyQMV1o7ZaJQQJ99BBACYeBjFXJ3w3AAABACOG5O2J -AZURE_OPENAI_DEPLOYMENT_NAME gpt-4o -AZURE_DEPLOYMENT_NAME gpt-4o -AZURE_API_VERSION 2024-08-01-preview -AZURE_SPEECH_KEY d133bd0fbec34843afefea5fcbbb1242 -AZURE_SPEECH_REGION centralindia -REACT_APP_AZURE_SPEECH_ENDPOINT https://centralindia.tts.speech.microsoft.com/ -FIREBASE_DEV_MODE true -DEFAULT_OTP 123456 -SCM_DO_BUILD_DURING_DEPLOYMENT false'
              startUpCommand: 'bash startup.sh'

          - script: |
              echo "Testing AgriCare 1.0 deployment..."
              sleep 120

              APP_URL="https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net"

              echo "=== Testing Main Application ==="
              for i in {1..15}; do
                echo "Test $i/15:"
                STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL" || echo "000")
                echo "Main App Status: $STATUS"

                if [ "$STATUS" = "200" ]; then
                  echo "✅ Main application is running!"
                  break
                fi

                sleep 10
              done

              echo "=== Testing API Endpoints ==="

              # Test health endpoint
              echo "Testing /api/health..."
              HEALTH_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL/api/health" || echo "000")
              echo "Health endpoint status: $HEALTH_STATUS"

              if [ "$HEALTH_STATUS" = "200" ]; then
                echo "✅ Health endpoint working"
                curl -s "$APP_URL/api/health" | head -c 200
              fi

              # Test dashboard endpoints
              echo "Testing /api/dashboard/soil..."
              SOIL_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL/api/dashboard/soil" || echo "000")
              echo "Soil endpoint status: $SOIL_STATUS"

              echo "Testing /api/weather/current..."
              WEATHER_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL/api/weather/current" || echo "000")
              echo "Weather endpoint status: $WEATHER_STATUS"

              # Test AI chat endpoint
              echo "Testing /api/chat/process (AI Chat)..."
              AI_RESPONSE=$(curl -s -X POST "$APP_URL/api/chat/process" -H "Content-Type: application/json" -d '{"text":"Hello","language":"en"}' || echo "error")
              echo "AI Chat response: $AI_RESPONSE" | head -c 200

              # Test database initialization
              echo "Testing /api/init-db..."
              DB_STATUS=$(curl -s -o /dev/null -w "%{http_code}" -X POST "$APP_URL/api/init-db" || echo "000")
              echo "Database init status: $DB_STATUS"

              echo "=== Deployment Summary ==="
              echo "Main App: $STATUS"
              echo "Health API: $HEALTH_STATUS"
              echo "Soil API: $SOIL_STATUS"
              echo "Weather API: $WEATHER_STATUS"

              if [ "$STATUS" = "200" ] && [ "$HEALTH_STATUS" = "200" ]; then
                echo "✅ AgriCare 1.0 deployment successful!"
              else
                echo "⚠️ Some issues detected, but application is deployed"
              fi

            displayName: 'Test AgriCare Deployment'
