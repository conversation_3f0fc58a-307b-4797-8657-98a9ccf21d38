# Azure DevOps Pipeline for AgriCare 1.0 Production Deployment
# Deploy complete application with Azure Cosmos DB MongoDB

trigger:
- main

variables:
  azureServiceConnection: 'Qagricare'
  webAppName: 'qagricare'
  resourceGroupName: 'agricare'
  nodeVersion: '18.x'

pool:
  vmImage: 'ubuntu-latest'

stages:
- stage: Build
  displayName: 'Build AgriCare Application'
  jobs:
  - job: BuildApp
    displayName: 'Build Complete Application'
    steps:
    - checkout: self
      clean: true
      fetchDepth: 1
      displayName: 'Checkout Repository'
    
    - task: NodeTool@0
      inputs:
        versionSpec: '$(nodeVersion)'
      displayName: 'Install Node.js'
    
    # Build Frontend with Production API URLs
    - script: |
        echo "Building frontend with production configuration..."
        cd frontend

        # Create production environment file for build only
        echo "Creating production environment configuration..."
        cat > .env.production.local << 'FRONTEND_PROD_EOF'
        # Production API Configuration - Azure App Service
        VITE_API_BASE_URL=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net
        VITE_API_BASE_URL_CHATBOT=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net
        VITE_API_URL=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net/api
        VITE_WS_URL=wss://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net
        VITE_FIREBASE_PROJECT_ID=agricare-1-0
        VITE_FIREBASE_DEV_MODE=true
        VITE_DEFAULT_OTP=123456
        VITE_ENABLE_AI_FEATURES=true
        VITE_ENABLE_WEATHER_FORECAST=true
        VITE_ENABLE_SOIL_ANALYSIS=true
        NODE_ENV=production
        FRONTEND_PROD_EOF

        # Install dependencies and build
        npm install --legacy-peer-deps
        NODE_ENV=production npm run build

        # Clean up temporary environment file
        rm -f .env.production.local

        echo "✅ Frontend built with production API URLs"
      displayName: 'Build Frontend with Production URLs'
    
    # Install Backend Dependencies
    - script: |
        echo "Installing backend dependencies..."
        cd backend
        npm install --production
        echo "✅ Backend dependencies installed"
      displayName: 'Install Backend Dependencies'
    
    # Prepare Application
    - script: |
        echo "Preparing complete AgriCare application..."
        
        # Create deployment directory
        mkdir -p $(Build.ArtifactStagingDirectory)/app
        
        # Copy backend files (excluding node_modules, we'll install fresh)
        rsync -av --exclude='node_modules' backend/ $(Build.ArtifactStagingDirectory)/app/
        
        # Copy frontend build to backend public directory
        mkdir -p $(Build.ArtifactStagingDirectory)/app/public
        cp -r frontend/dist/* $(Build.ArtifactStagingDirectory)/app/public/
        
        # Copy package.json for production install
        cp backend/package.json $(Build.ArtifactStagingDirectory)/app/
        cp backend/package-lock.json $(Build.ArtifactStagingDirectory)/app/
        
        echo "✅ Application prepared for deployment"
      displayName: 'Prepare Application'
    
    # Create Production Environment Configuration
    - script: |
        echo "Creating production environment configuration..."
        
        cd $(Build.ArtifactStagingDirectory)/app
        
        # Create production .env file
        cat > .env << 'EOF'
        NODE_ENV=production
        PORT=8080
        
        # Azure Cosmos DB MongoDB Configuration
        MONGODB_URI=*****************************************************************************************************************************************************************************************************************************************
        
        # JWT Configuration
        JWT_SECRET=agricare-production-jwt-secret-2024
        
        # Firebase Configuration
        FIREBASE_PROJECT_ID=agricare-1-0
        FIREBASE_DEV_MODE=true
        DEFAULT_OTP=123456
        
        # API Keys
        OPENAI_API_KEY=your-openai-api-key
        AGMARKNET_API_KEY=579b464db66ec23bdd000001cdd3946e44ce4aad7209ff7b23ac571b
        NASA_API_KEY=DEMO_KEY
        
        # CORS Configuration
        CORS_ORIGIN=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net
        
        # Logging
        LOG_LEVEL=info
        EOF
        
        echo "✅ Production environment configuration created"
      displayName: 'Create Production Environment'
    
    # Update Server Configuration for Production
    - script: |
        echo "Updating server configuration for production..."
        
        cd $(Build.ArtifactStagingDirectory)/app
        
        # Update server.js to serve frontend and handle database properly
        cat > server.js << 'SERVER_EOF'
        require("dotenv").config();
        const express = require("express");
        const cors = require("cors");
        const path = require('path');
        const connectDB = require('./config/database');
        
        const app = express();
        
        // Connect to database (mandatory)
        connectDB();
        
        // Middleware
        app.use(cors({
          origin: process.env.CORS_ORIGIN || '*',
          credentials: true
        }));
        app.use(express.json({ limit: '50mb' }));
        app.use(express.urlencoded({ extended: true, limit: '50mb' }));
        
        // Serve static files (frontend)
        app.use(express.static(path.join(__dirname, 'public')));
        
        // API Routes
        app.use("/api/auth", require("./routes/auth.routes"));
        app.use("/api/dashboard", require("./routes/dashboard"));
        app.use("/api/ai", require("./routes/ai"));
        app.use("/api/weather", require("./routes/weather"));
        app.use("/api/satellite", require("./routes/satelliteData"));
        app.use("/api/real-market", require("./routes/realMarketData"));
        app.use("/api/agri-expert", require("./routes/agriExpert"));
        app.use("/api/tm", require("./routes/tm"));
        app.use("/api/chat", require("./routes/chat"));
        app.use("/api/computer-vision", require("./routes/computerVision"));
        
        // Health check
        app.get('/api/health', (req, res) => {
          const mongoose = require('mongoose');
          res.json({ 
            status: 'ok', 
            message: 'AgriCare 1.0 API is running',
            timestamp: new Date().toISOString(),
            database: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',
            environment: process.env.NODE_ENV
          });
        });
        
        // Serve React app for all other routes
        app.get('*', (req, res) => {
          if (req.path.startsWith('/api/')) {
            return res.status(404).json({ error: 'API endpoint not found' });
          }
          res.sendFile(path.join(__dirname, 'public', 'index.html'));
        });
        
        // Error handling
        app.use((err, req, res, next) => {
          console.error('Server Error:', err);
          res.status(500).json({ error: 'Internal Server Error' });
        });
        
        // Start server
        const PORT = process.env.PORT || 8080;
        app.listen(PORT, () => {
          console.log(`🚀 AgriCare 1.0 server running on port ${PORT}`);
          console.log(`🌍 Environment: ${process.env.NODE_ENV}`);
          console.log(`🗄️ Database: ${process.env.MONGODB_URI ? 'Azure Cosmos DB' : 'Not configured'}`);
        });
        SERVER_EOF
        
        echo "✅ Server configuration updated"
      displayName: 'Update Server Configuration'
    
    # Archive Application
    - task: ArchiveFiles@2
      inputs:
        rootFolderOrFile: '$(Build.ArtifactStagingDirectory)/app'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(Build.ArtifactStagingDirectory)/agricare-production.zip'
        replaceExistingArchive: true
      displayName: 'Archive Application'
    
    # Publish Artifacts
    - task: PublishBuildArtifacts@1
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)'
        ArtifactName: 'drop'
        publishLocation: 'Container'
      displayName: 'Publish Artifacts'

- stage: Deploy
  displayName: 'Deploy to Production'
  dependsOn: Build
  jobs:
  - deployment: DeployApp
    displayName: 'Deploy AgriCare 1.0'
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          # Deploy to Azure App Service
          - task: AzureWebApp@1
            displayName: 'Deploy AgriCare 1.0 to Azure'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              appType: 'webApp'
              appName: '$(webAppName)'
              resourceGroupName: '$(resourceGroupName)'
              package: '$(Pipeline.Workspace)/drop/agricare-production.zip'
              deploymentMethod: 'zipDeploy'
              appSettings: |
                -NODE_ENV production 
                -WEBSITE_NODE_DEFAULT_VERSION 18.x 
                -PORT 8080
                -SCM_DO_BUILD_DURING_DEPLOYMENT true
                -ENABLE_ORYX_BUILD true
          
          # Post-deployment verification
          - script: |
              echo "🔍 Verifying AgriCare 1.0 deployment..."
              sleep 60
              
              APP_URL="https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net"
              
              echo "Testing application endpoints..."
              
              # Test health endpoint
              echo "1. Testing health endpoint..."
              HEALTH_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL/api/health" || echo "000")
              echo "Health endpoint status: $HEALTH_STATUS"
              
              # Test main application
              echo "2. Testing main application..."
              APP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL" || echo "000")
              echo "Main application status: $APP_STATUS"
              
              # Test database connection
              echo "3. Testing database connection..."
              DB_RESPONSE=$(curl -s "$APP_URL/api/health" | grep -o '"database":"[^"]*"' || echo "database check failed")
              echo "Database status: $DB_RESPONSE"
              
              if [ "$APP_STATUS" = "200" ] && [ "$HEALTH_STATUS" = "200" ]; then
                echo "✅ AgriCare 1.0 deployment successful!"
                echo "🌐 Application URL: $APP_URL"
                echo "🏥 Health Check: $APP_URL/api/health"
              else
                echo "❌ Deployment verification failed"
                echo "App Status: $APP_STATUS, Health Status: $HEALTH_STATUS"
                exit 1
              fi
            displayName: 'Verify Deployment'
