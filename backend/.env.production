# Production Environment Configuration for Azure App Service
NODE_ENV=production
PORT=8000

# Azure Cosmos DB MongoDB Configuration
MONGODB_URI="mongodb://quamin:<primary-key>@quamin.mongo.cosmos.azure.com:10255/quamin?ssl=true&replicaSet=globaldb&retrywrites=false&maxIdleTimeMS=120000&appName=@quamin@"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-for-production-change-this-in-production"

# Firebase Configuration (Production)
FIREBASE_PROJECT_ID="agricare-1-0"
FIREBASE_PRIVATE_KEY_ID="your-private-key-id"
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour-private-key-here\n-----END PRIVATE KEY-----"
FIREBASE_CLIENT_EMAIL="<EMAIL>"
FIREBASE_CLIENT_ID="your-client-id"
FIREBASE_AUTH_URI="https://accounts.google.com/o/oauth2/auth"
FIREBASE_TOKEN_URI="https://oauth2.googleapis.com/token"

# OpenAI Configuration
OPENAI_API_KEY="your-openai-api-key-here"

# Real Market Data API Keys
CEDA_API_KEY="demo-key"
PYPRICINGAPI_KEY="demo-key"
AGMARKNET_API_KEY="579b464db66ec23bdd000001cdd3946e44ce4aad7209ff7b23ac571b"

# NASA API Configuration
NASA_API_KEY="DEMO_KEY"

# CORS Configuration
CORS_ORIGIN="https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net"

# Azure App Service Configuration
WEBSITE_HOSTNAME="qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net"
WEBSITE_SITE_NAME="qagricare"

# Logging Configuration
LOG_LEVEL="info"
