const mongoose = require('mongoose');
require('dotenv').config();

const connectDB = async () => {
    try {
        // Set mongoose options to avoid deprecation warnings
        const options = {
            useNewUrlParser: true,
            useUnifiedTopology: true,
            serverSelectionTimeoutMS: 30000, // Increased timeout for MongoDB startup
            socketTimeoutMS: 45000, // Close sockets after 45s of inactivity
        };

        const conn = await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/agricare', options);
        console.log(`✅ MongoDB Connected: ${conn.connection.host}`);
        console.log(`🗄️ Database: ${conn.connection.name}`);
    } catch (error) {
        console.error(`❌ MongoDB Connection Error: ${error.message}`);
        console.error('Make sure MongoDB is running. Try starting it with: mongod --config mongod.conf --fork');

        // In production, wait a bit longer for MongoDB to start
        if (process.env.NODE_ENV === 'production') {
            console.log('🔄 Waiting for MongoDB to start in production...');
            setTimeout(async () => {
                try {
                    const conn = await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/agricare', options);
                    console.log(`✅ MongoDB Connected (retry): ${conn.connection.host}`);
                } catch (retryError) {
                    console.error(`❌ MongoDB Connection Failed (retry): ${retryError.message}`);
                    process.exit(1);
                }
            }, 15000); // Wait 15 seconds for MongoDB to start
        } else {
            process.exit(1);
        }
    }
};

module.exports = connectDB;
