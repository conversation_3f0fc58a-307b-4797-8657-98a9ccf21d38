/**
 * User Privacy Middleware
 * Ensures users can only see their own data unless they are admin
 */

const userMappingService = require('../services/userMapping.service');

/**
 * Middleware to ensure user privacy for chat and image analysis data
 * Users can only see their own data, admins can see any user's data
 */
const ensureUserPrivacy = async (req, res, next) => {
  try {
    // Get current user's phone number from auth
    const currentUserPhone = req.user?.phoneNumber || req.user?.phone;
    
    if (!currentUserPhone) {
      return res.status(401).json({
        success: false,
        message: 'User phone number not found in authentication'
      });
    }
    
    // Check if current user is TM/Admin
    const isAdmin = await userMappingService.isAdminUser(currentUserPhone);

    // If TM/Admin, allow access to any user's data
    if (isAdmin) {
      // Check if TM is requesting specific user's data
      const requestedPhone = req.query.phoneNumber || req.body.phoneNumber || req.params.phoneNumber;

      if (requestedPhone) {
        // TM is requesting specific user's data
        const targetUserId = await userMappingService.getUserIdByPhone(requestedPhone);
        req.targetUserId = targetUserId;
        req.targetPhoneNumber = requestedPhone;
        req.isAdminRequest = true;
        console.log(`TM ${currentUserPhone} accessing data for user ${requestedPhone}`);
      } else {
        // TM is requesting their own data
        const currentUserId = await userMappingService.getUserIdByPhone(currentUserPhone);
        req.targetUserId = currentUserId;
        req.targetPhoneNumber = currentUserPhone;
        req.isAdminRequest = false;
      }
    } else {
      // Regular user - can only access their own data
      const currentUserId = await userMappingService.getUserIdByPhone(currentUserPhone);
      req.targetUserId = currentUserId;
      req.targetPhoneNumber = currentUserPhone;
      req.isAdminRequest = false;
      
      // Check if regular user is trying to access someone else's data
      const requestedPhone = req.query.phoneNumber || req.body.phoneNumber || req.params.phoneNumber;
      if (requestedPhone && requestedPhone !== currentUserPhone) {
        console.log(`Access denied: User ${currentUserPhone} tried to access data for ${requestedPhone}`);
        return res.status(403).json({
          success: false,
          message: 'Access denied. You can only view your own data. Contact your Territory Manager for assistance.'
        });
      }
    }
    
    next();
  } catch (error) {
    console.error('User privacy middleware error:', error);
    return res.status(500).json({
      success: false,
      message: 'Error checking user permissions'
    });
  }
};

/**
 * Middleware to add user context to requests
 */
const addUserContext = async (req, res, next) => {
  try {
    const currentUserPhone = req.user?.phoneNumber || req.user?.phone;
    
    if (currentUserPhone) {
      const userDetails = await userMappingService.getUserDetailsByPhone(currentUserPhone);
      req.userContext = userDetails;
      req.currentUserId = userDetails.id;
      req.currentUserPhone = userDetails.phoneNumber;
      req.currentUserName = userDetails.name;
      req.isAdmin = await userMappingService.isAdminUser(currentUserPhone);
    }
    
    next();
  } catch (error) {
    console.error('Add user context middleware error:', error);
    next(); // Continue even if there's an error
  }
};

/**
 * Filter data based on user privacy settings
 */
const filterUserData = (data, userId, isAdmin = false, requestedUserId = null) => {
  if (!data) return data;
  
  // If admin and requesting specific user's data, return that user's data
  if (isAdmin && requestedUserId) {
    if (Array.isArray(data)) {
      return data.filter(item => 
        item.userId === requestedUserId || 
        item.user === requestedUserId ||
        item.phoneNumber === requestedUserId.replace('phone_', '')
      );
    }
    return data;
  }
  
  // If admin and no specific user requested, return all data
  if (isAdmin && !requestedUserId) {
    return data;
  }
  
  // Regular user - filter to only their data
  if (Array.isArray(data)) {
    return data.filter(item => 
      item.userId === userId || 
      item.user === userId ||
      item.phoneNumber === userId.replace('phone_', '')
    );
  }
  
  // Single item - check if it belongs to the user
  if (data.userId === userId || data.user === userId || 
      data.phoneNumber === userId.replace('phone_', '')) {
    return data;
  }
  
  return null;
};

/**
 * Add user identification to data being saved
 */
const addUserIdentification = (data, userId, phoneNumber) => {
  if (!data) return data;
  
  if (Array.isArray(data)) {
    return data.map(item => ({
      ...item,
      userId: userId,
      phoneNumber: phoneNumber,
      createdAt: item.createdAt || new Date(),
      updatedAt: new Date()
    }));
  }
  
  return {
    ...data,
    userId: userId,
    phoneNumber: phoneNumber,
    createdAt: data.createdAt || new Date(),
    updatedAt: new Date()
  };
};

module.exports = {
  ensureUserPrivacy,
  addUserContext,
  filterUserData,
  addUserIdentification
};
