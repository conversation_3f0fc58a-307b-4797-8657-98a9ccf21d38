const mongoose = require('mongoose');

const imageAnalysisSchema = new mongoose.Schema({
  userId: {
    type: String,
    required: true,
    index: true
  },
  phoneNumber: {
    type: String,
    required: true,
    index: true
  },
  userName: {
    type: String,
    required: true
  },
  imageUrl: {
    type: String,
    required: true
  },
  imageName: {
    type: String,
    required: true
  },
  imageSize: {
    type: Number
  },
  imageType: {
    type: String
  },
  analysisType: {
    type: String,
    enum: ['crop_disease', 'pest_identification', 'soil_analysis', 'plant_health', 'general'],
    default: 'general'
  },
  analysisResult: {
    disease: {
      name: String,
      confidence: Number,
      description: String,
      severity: {
        type: String,
        enum: ['low', 'medium', 'high', 'critical']
      }
    },
    pest: {
      name: String,
      confidence: Number,
      description: String,
      damage_level: {
        type: String,
        enum: ['minimal', 'moderate', 'severe', 'critical']
      }
    },
    plant_health: {
      status: {
        type: String,
        enum: ['healthy', 'stressed', 'diseased', 'dying']
      },
      confidence: Number,
      factors: [String]
    },
    soil_condition: {
      type: {
        type: String,
        enum: ['fertile', 'poor', 'waterlogged', 'dry', 'contaminated']
      },
      confidence: Number,
      ph_estimate: Number,
      moisture_level: String
    },
    general_description: String,
    confidence_score: {
      type: Number,
      min: 0,
      max: 100
    }
  },
  recommendations: [{
    category: {
      type: String,
      enum: ['treatment', 'prevention', 'fertilizer', 'irrigation', 'pesticide', 'general']
    },
    title: String,
    description: String,
    priority: {
      type: String,
      enum: ['low', 'medium', 'high', 'urgent'],
      default: 'medium'
    },
    cost_estimate: String,
    timeline: String
  }],
  location: {
    latitude: Number,
    longitude: Number,
    address: String,
    farm_name: String
  },
  weather_context: {
    temperature: Number,
    humidity: Number,
    rainfall: Number,
    season: String
  },
  crop_context: {
    crop_type: String,
    growth_stage: String,
    planting_date: Date,
    variety: String
  },
  follow_up_questions: [String],
  tags: [String],
  status: {
    type: String,
    enum: ['pending', 'analyzed', 'reviewed', 'archived'],
    default: 'analyzed'
  },
  feedback: {
    rating: {
      type: Number,
      min: 1,
      max: 5
    },
    comment: String,
    helpful: Boolean,
    accuracy: {
      type: Number,
      min: 1,
      max: 5
    }
  },
  processing_time: {
    type: Number, // in milliseconds
    default: 0
  },
  ai_model_version: {
    type: String,
    default: '1.0'
  },
  language: {
    type: String,
    default: 'en'
  },
  is_public: {
    type: Boolean,
    default: false
  },
  shared_with: [String], // Array of user IDs who can view this analysis
  created_at: {
    type: Date,
    default: Date.now
  },
  updated_at: {
    type: Date,
    default: Date.now
  },
  expires_at: {
    type: Date,
    default: function() {
      // Set expiration to 2 years from creation
      const date = new Date();
      date.setFullYear(date.getFullYear() + 2);
      return date;
    }
  }
});

// Create indexes for efficient querying
imageAnalysisSchema.index({ userId: 1, created_at: -1 });
imageAnalysisSchema.index({ phoneNumber: 1, created_at: -1 });
imageAnalysisSchema.index({ analysisType: 1, created_at: -1 });
imageAnalysisSchema.index({ 'analysisResult.disease.name': 1 });
imageAnalysisSchema.index({ 'analysisResult.pest.name': 1 });
imageAnalysisSchema.index({ status: 1 });
imageAnalysisSchema.index({ expires_at: 1 }, { expireAfterSeconds: 0 }); // TTL index

// Update the updated_at field on save
imageAnalysisSchema.pre('save', function(next) {
  this.updated_at = new Date();
  next();
});

// Virtual for formatted creation date
imageAnalysisSchema.virtual('formatted_date').get(function() {
  return this.created_at.toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
});

// Virtual for analysis summary
imageAnalysisSchema.virtual('summary').get(function() {
  if (this.analysisResult.disease && this.analysisResult.disease.name) {
    return `Disease: ${this.analysisResult.disease.name} (${this.analysisResult.disease.confidence}% confidence)`;
  } else if (this.analysisResult.pest && this.analysisResult.pest.name) {
    return `Pest: ${this.analysisResult.pest.name} (${this.analysisResult.pest.confidence}% confidence)`;
  } else if (this.analysisResult.plant_health && this.analysisResult.plant_health.status) {
    return `Plant Health: ${this.analysisResult.plant_health.status} (${this.analysisResult.plant_health.confidence}% confidence)`;
  } else if (this.analysisResult.general_description) {
    return this.analysisResult.general_description.substring(0, 100) + '...';
  }
  return 'Image analysis completed';
});

// Method to check if user can view this analysis
imageAnalysisSchema.methods.canUserView = function(userId, userPhone, isAdmin = false) {
  // Admin can view all
  if (isAdmin) return true;
  
  // Owner can view
  if (this.userId === userId || this.phoneNumber === userPhone) return true;
  
  // Public analyses can be viewed by anyone
  if (this.is_public) return true;
  
  // Shared analyses
  if (this.shared_with && this.shared_with.includes(userId)) return true;
  
  return false;
};

// Static method to get user's analyses
imageAnalysisSchema.statics.getUserAnalyses = function(userId, phoneNumber, options = {}) {
  const query = {
    $or: [
      { userId: userId },
      { phoneNumber: phoneNumber }
    ]
  };
  
  return this.find(query)
    .sort({ created_at: -1 })
    .limit(options.limit || 20)
    .skip(options.skip || 0);
};

const ImageAnalysis = mongoose.model('ImageAnalysis', imageAnalysisSchema);

module.exports = ImageAnalysis;
