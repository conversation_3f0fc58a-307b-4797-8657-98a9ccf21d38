/**
 * User Mapping Service - Phone Number to Name Mapping
 * Handles phone number as unique identifier and name display
 */

const User = require('../models/user.model');
const Farmer = require('../models/Farmer');
const mongoose = require('mongoose');

// Phone number to name mapping
const phoneToNameMapping = {
  '9611966747': '<PERSON><PERSON>',
  '9876543210': 'Test User',
  '9999999999': 'Admin User',
  // Add more mappings as needed
};

/**
 * Get user name by phone number
 * @param {string} phoneNumber - Phone number
 * @returns {string} User name
 */
const getUserNameByPhone = async (phoneNumber) => {
  try {
    // Normalize phone number
    const normalizedPhone = phoneNumber.replace(/\D/g, '').slice(-10);
    
    // First check if user exists in User collection
    const user = await User.findOne({ 
      $or: [
        { phoneNumber: normalizedPhone },
        { phone: normalizedPhone }
      ]
    });
    
    if (user && user.name) {
      return user.name;
    }
    
    // Check in Farmer collection
    const farmer = await Farmer.findOne({ mobile: normalizedPhone });
    if (farmer && farmer.name) {
      return farmer.name;
    }
    
    // Check in TM collection
    try {
      const tm = await mongoose.model('TM').findOne({ mobile: normalizedPhone });
      if (tm && tm.name) {
        return tm.name;
      }
    } catch (error) {
      console.log('TM collection not found or error:', error.message);
    }
    
    // Check predefined mapping
    if (phoneToNameMapping[normalizedPhone]) {
      return phoneToNameMapping[normalizedPhone];
    }
    
    // Default fallback
    return `User ${normalizedPhone.slice(-4)}`;
  } catch (error) {
    console.error('Error getting user name by phone:', error);
    return `User ${phoneNumber.slice(-4)}`;
  }
};

/**
 * Get user ID by phone number
 * @param {string} phoneNumber - Phone number
 * @returns {string} User ID
 */
const getUserIdByPhone = async (phoneNumber) => {
  try {
    const normalizedPhone = phoneNumber.replace(/\D/g, '').slice(-10);
    
    const user = await User.findOne({ 
      $or: [
        { phoneNumber: normalizedPhone },
        { phone: normalizedPhone }
      ]
    });
    
    if (user) {
      return user._id.toString();
    }
    
    // If no user found, create a virtual user ID based on phone
    return `phone_${normalizedPhone}`;
  } catch (error) {
    console.error('Error getting user ID by phone:', error);
    return `phone_${phoneNumber.replace(/\D/g, '').slice(-10)}`;
  }
};

/**
 * Get user details by phone number
 * @param {string} phoneNumber - Phone number
 * @returns {object} User details
 */
const getUserDetailsByPhone = async (phoneNumber) => {
  try {
    const normalizedPhone = phoneNumber.replace(/\D/g, '').slice(-10);
    
    const user = await User.findOne({ 
      $or: [
        { phoneNumber: normalizedPhone },
        { phone: normalizedPhone }
      ]
    });
    
    if (user) {
      return {
        id: user._id.toString(),
        phoneNumber: normalizedPhone,
        name: user.name,
        role: user.role,
        email: user.email
      };
    }
    
    // Check other collections
    const farmer = await Farmer.findOne({ mobile: normalizedPhone });
    if (farmer) {
      return {
        id: farmer._id.toString(),
        phoneNumber: normalizedPhone,
        name: farmer.name,
        role: 'Farmer',
        email: farmer.email || `${normalizedPhone}@agricare.com`
      };
    }
    
    try {
      const tm = await mongoose.model('TM').findOne({ mobile: normalizedPhone });
      if (tm) {
        return {
          id: tm._id.toString(),
          phoneNumber: normalizedPhone,
          name: tm.name,
          role: 'TM',
          email: tm.email || `${normalizedPhone}@agricare.com`
        };
      }
    } catch (error) {
      console.log('TM collection not found');
    }
    
    // Return default user details
    return {
      id: `phone_${normalizedPhone}`,
      phoneNumber: normalizedPhone,
      name: phoneToNameMapping[normalizedPhone] || `User ${normalizedPhone.slice(-4)}`,
      role: 'User',
      email: `${normalizedPhone}@agricare.com`
    };
  } catch (error) {
    console.error('Error getting user details by phone:', error);
    const normalizedPhone = phoneNumber.replace(/\D/g, '').slice(-10);
    return {
      id: `phone_${normalizedPhone}`,
      phoneNumber: normalizedPhone,
      name: `User ${normalizedPhone.slice(-4)}`,
      role: 'User',
      email: `${normalizedPhone}@agricare.com`
    };
  }
};

/**
 * Check if user is admin
 * @param {string} phoneNumber - Phone number
 * @returns {boolean} Is admin
 */
const isAdminUser = async (phoneNumber) => {
  try {
    const normalizedPhone = phoneNumber.replace(/\D/g, '').slice(-10);
    
    // Check if user has admin role
    const user = await User.findOne({ 
      $or: [
        { phoneNumber: normalizedPhone },
        { phone: normalizedPhone }
      ]
    });
    
    if (user && (user.role === 'Admin' || user.role === 'TM')) {
      return true;
    }
    
    // Check predefined admin phone numbers
    const adminPhones = ['9611966747', '9999999999']; // Add admin phone numbers
    return adminPhones.includes(normalizedPhone);
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
};

/**
 * Update phone to name mapping
 * @param {string} phoneNumber - Phone number
 * @param {string} name - User name
 */
const updatePhoneToNameMapping = (phoneNumber, name) => {
  const normalizedPhone = phoneNumber.replace(/\D/g, '').slice(-10);
  phoneToNameMapping[normalizedPhone] = name;
};

/**
 * Get all phone to name mappings (for admin)
 * @returns {object} All mappings
 */
const getAllPhoneToNameMappings = () => {
  return { ...phoneToNameMapping };
};

module.exports = {
  getUserNameByPhone,
  getUserIdByPhone,
  getUserDetailsByPhone,
  isAdminUser,
  updatePhoneToNameMapping,
  getAllPhoneToNameMappings
};
