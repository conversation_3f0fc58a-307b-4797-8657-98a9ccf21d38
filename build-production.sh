#!/bin/bash

# Build AgriCare 1.0 for Production (without affecting local development)
# This script builds the frontend with production API URLs

set -e

echo "🏗️ Building AgriCare 1.0 for Production..."

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

echo_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

echo_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Check if we're in the right directory
if [ ! -d "frontend" ] || [ ! -d "backend" ]; then
    echo "❌ Error: Please run this script from the AgriCare project root directory"
    exit 1
fi

# Build frontend with production configuration
echo_info "Building frontend with production API URLs..."
cd frontend

# Backup existing .env files if they exist
if [ -f ".env" ]; then
    echo_info "Backing up existing .env file..."
    cp .env .env.backup
fi

if [ -f ".env.local" ]; then
    echo_info "Backing up existing .env.local file..."
    cp .env.local .env.local.backup
fi

# Create temporary production environment file
echo_info "Creating temporary production environment configuration..."
cat > .env.production.local << 'PROD_ENV_EOF'
# Production API Configuration - Azure App Service
VITE_API_BASE_URL=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net
VITE_API_BASE_URL_CHATBOT=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net
VITE_API_URL=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net/api
VITE_WS_URL=wss://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net
VITE_FIREBASE_PROJECT_ID=agricare-1-0
VITE_FIREBASE_DEV_MODE=true
VITE_DEFAULT_OTP=123456
VITE_ENABLE_AI_FEATURES=true
VITE_ENABLE_WEATHER_FORECAST=true
VITE_ENABLE_SOIL_ANALYSIS=true
NODE_ENV=production
PROD_ENV_EOF

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo_info "Installing frontend dependencies..."
    npm install --legacy-peer-deps
fi

# Build for production
echo_info "Building frontend for production..."
NODE_ENV=production npm run build

# Clean up temporary files
echo_info "Cleaning up temporary files..."
rm -f .env.production.local

# Restore backup files if they existed
if [ -f ".env.backup" ]; then
    echo_info "Restoring original .env file..."
    mv .env.backup .env
fi

if [ -f ".env.local.backup" ]; then
    echo_info "Restoring original .env.local file..."
    mv .env.local.backup .env.local
fi

cd ..

echo_success "✅ Production build completed!"
echo_info "📁 Frontend build output: frontend/dist/"
echo_info "🌐 Built with Azure App Service URLs"
echo_info "🔧 Local development environment unchanged"

# Show build summary
if [ -d "frontend/dist" ]; then
    BUILD_SIZE=$(du -sh frontend/dist | cut -f1)
    echo_success "📊 Build size: $BUILD_SIZE"
    echo_info "🚀 Ready for deployment to Azure App Service"
else
    echo "❌ Build failed - dist directory not found"
    exit 1
fi

echo ""
echo_success "🎉 Production build ready for deployment!"
echo_info "Next steps:"
echo_info "1. Run './deploy-to-azure-direct.sh' to deploy to Azure"
echo_info "2. Or use the Azure DevOps pipeline for automated deployment"
