#!/bin/bash

# Deploy ACTUAL AgriCare 1.0 - Use Real React Build
# This script uses your actual built React application

set -e

echo "🌾 Creating ACTUAL AgriCare 1.0 deployment with REAL React build..."

# Create deployment directory
DEPLOY_DIR="actual-agricare-deployment"
rm -rf "$DEPLOY_DIR" 2>/dev/null || true
mkdir -p "$DEPLOY_DIR"

echo "📁 Copying REAL backend..."
# Copy your actual backend
cp -r backend/* "$DEPLOY_DIR/"

echo "📁 Copying REAL React build..."
# Copy your ACTUAL React build (not dummy HTML)
mkdir -p "$DEPLOY_DIR/public"
cp -r frontend/dist/* "$DEPLOY_DIR/public/"

echo "✅ Copied actual React build:"
ls -la "$DEPLOY_DIR/public/"

echo "🔧 Configuring server to serve REAL React app..."
# Ensure server.js serves your actual React build
cat >> "$DEPLOY_DIR/server.js" << 'EOF'

// ✅ Serve ACTUAL React Frontend Static Files
const path = require('path');
app.use(express.static(path.join(__dirname, 'public')));

// ✅ Serve ACTUAL React App for all non-API routes (SPA routing)
app.get('*', (req, res) => {
  // Don't serve React for API routes
  if (req.path.startsWith('/api/')) {
    return res.status(404).json({ error: 'API endpoint not found' });
  }
  
  // Serve ACTUAL React app
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});
EOF

echo "📦 Creating production package.json..."
cat > "$DEPLOY_DIR/package.json" << 'EOF'
{
  "name": "agricare-actual",
  "version": "1.0.0",
  "description": "AgriCare 1.0 - ACTUAL Application",
  "main": "server.js",
  "scripts": {
    "start": "node server.js"
  },
  "engines": {
    "node": "22.x"
  },
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "mongoose": "^7.6.0",
    "dotenv": "^16.3.1",
    "morgan": "^1.10.0",
    "multer": "^1.4.4",
    "bcryptjs": "^2.4.3",
    "jsonwebtoken": "^9.0.2",
    "firebase-admin": "^11.0.0",
    "axios": "^1.4.0",
    "socket.io": "^4.7.0"
  }
}
EOF

echo "⚙️ Creating web.config..."
cat > "$DEPLOY_DIR/web.config" << 'EOF'
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <handlers>
      <add name="iisnode" path="server.js" verb="*" modules="iisnode"/>
    </handlers>
    <rewrite>
      <rules>
        <rule name="API" stopProcessing="true">
          <match url="^api/.*" />
          <action type="Rewrite" url="server.js"/>
        </rule>
        <rule name="StaticFiles" stopProcessing="true">
          <match url="^(static|assets|css|js|images|fonts|uploads)/.*" />
          <action type="Rewrite" url="public/{R:0}"/>
        </rule>
        <rule name="Frontend" stopProcessing="true">
          <match url="^(tm|agri-expert|dashboard|farms|weather|hr|farmers|livestock|ai|chat|auth|users).*" />
          <action type="Rewrite" url="public/index.html"/>
        </rule>
        <rule name="Default">
          <match url=".*" />
          <conditions>
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="True"/>
          </conditions>
          <action type="Rewrite" url="public/index.html"/>
        </rule>
      </rules>
    </rewrite>
    <iisnode node_env="production" />
  </system.webServer>
</configuration>
EOF

echo "📦 Creating deployment package..."
cd "$DEPLOY_DIR"
zip -r "../agricare-actual-deployment.zip" . > /dev/null 2>&1
cd ..

PACKAGE_SIZE=$(du -sh "agricare-actual-deployment.zip" | cut -f1)
echo "✅ ACTUAL AgriCare 1.0 deployment package created: agricare-actual-deployment.zip ($PACKAGE_SIZE)"

echo ""
echo "🌾 ACTUAL AgriCare 1.0 Deployment Package Ready!"
echo ""
echo "📦 Package: agricare-actual-deployment.zip ($PACKAGE_SIZE)"
echo "🚀 Contains your ACTUAL React application (not dummy HTML)"
echo ""
echo "📱 To deploy:"
echo "1. Go to: https://portal.azure.com"
echo "2. Search: 'qagricare'"
echo "3. Go to: Advanced Tools → Go (Kudu)"
echo "4. Upload: agricare-actual-deployment.zip"
echo ""
echo "🎯 This will deploy your REAL AgriCare 1.0 with:"
echo "   ✅ Your actual React frontend (Material-UI components)"
echo "   ✅ Your complete backend (all routes and APIs)"
echo "   ✅ Territory Manager Dashboard"
echo "   ✅ Farmer Interface"
echo "   ✅ All agricultural modules"
echo ""
echo "NO MORE DUMMY CONTENT - THIS IS YOUR ACTUAL APPLICATION!"
