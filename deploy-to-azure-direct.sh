#!/bin/bash

# Direct Azure App Service Deployment Script for AgriCare 1.0
# This script deploys the application directly to Azure App Service with Azure Cosmos DB

set -e

echo "🚀 Starting AgriCare 1.0 Direct Deployment to Azure..."

# Configuration
APP_NAME="qagricare"
RESOURCE_GROUP="agricare"
SUBSCRIPTION_ID="eb126322-f0a5-441d-aa95-369f4388f676"
DEPLOYMENT_DIR="azure-deployment"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

echo_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

echo_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

echo_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Azure CLI is installed
if ! command -v az &> /dev/null; then
    echo_error "Azure CLI is not installed. Please install it first."
    exit 1
fi

# Login to Azure (if not already logged in)
echo_info "Checking Azure login status..."
if ! az account show &> /dev/null; then
    echo_info "Logging in to Azure..."
    az login
fi

# Set subscription
echo_info "Setting Azure subscription..."
az account set --subscription "$SUBSCRIPTION_ID"

# Create deployment directory
echo_info "Creating deployment directory..."
rm -rf "$DEPLOYMENT_DIR"
mkdir -p "$DEPLOYMENT_DIR"

# Build frontend with production API URLs
echo_info "Building frontend with production configuration..."
cd frontend

# Create temporary production .env file for build only
echo_info "Creating temporary production environment for build..."
cat > .env.production.local << 'FRONTEND_ENV_EOF'
# Production API Configuration - Azure App Service
VITE_API_BASE_URL=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net
VITE_API_BASE_URL_CHATBOT=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net
VITE_API_URL=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net/api
VITE_WS_URL=wss://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net
VITE_FIREBASE_PROJECT_ID=agricare-1-0
VITE_FIREBASE_DEV_MODE=true
VITE_DEFAULT_OTP=123456
VITE_ENABLE_AI_FEATURES=true
VITE_ENABLE_WEATHER_FORECAST=true
VITE_ENABLE_SOIL_ANALYSIS=true
NODE_ENV=production
FRONTEND_ENV_EOF

# Install dependencies and build with production environment
npm install --legacy-peer-deps
NODE_ENV=production npm run build

# Clean up temporary environment file
rm -f .env.production.local

echo_success "Frontend built with production API URLs"
cd ..

# Prepare backend
echo_info "Preparing backend..."
cp -r backend/* "$DEPLOYMENT_DIR/"
mkdir -p "$DEPLOYMENT_DIR/public"
cp -r frontend/dist/* "$DEPLOYMENT_DIR/public/"

# Create production environment file
echo_info "Creating production environment configuration..."
cat > "$DEPLOYMENT_DIR/.env" << 'EOF'
NODE_ENV=production
PORT=8080

# Azure Cosmos DB MongoDB Configuration
MONGODB_URI=*****************************************************************************************************************************************************************************************************************************************

# JWT Configuration
JWT_SECRET=agricare-production-jwt-secret-2024

# Firebase Configuration
FIREBASE_PROJECT_ID=agricare-1-0
FIREBASE_DEV_MODE=true
DEFAULT_OTP=123456

# API Keys
OPENAI_API_KEY=your-openai-api-key
AGMARKNET_API_KEY=579b464db66ec23bdd000001cdd3946e44ce4aad7209ff7b23ac571b
NASA_API_KEY=DEMO_KEY

# CORS Configuration
CORS_ORIGIN=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net

# Logging
LOG_LEVEL=info
EOF

# Update server.js for production
echo_info "Updating server configuration for production..."
cat > "$DEPLOYMENT_DIR/server.js" << 'SERVER_EOF'
require("dotenv").config();
const express = require("express");
const cors = require("cors");
const path = require('path');
const connectDB = require('./config/database');

const app = express();

// Connect to database (mandatory)
connectDB();

// Middleware
app.use(cors({
  origin: process.env.CORS_ORIGIN || '*',
  credentials: true
}));
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Serve static files (frontend)
app.use(express.static(path.join(__dirname, 'public')));

// API Routes
app.use("/api/auth", require("./routes/auth.routes"));
app.use("/api/dashboard", require("./routes/dashboard"));
app.use("/api/ai", require("./routes/ai"));
app.use("/api/weather", require("./routes/weather"));
app.use("/api/satellite", require("./routes/satelliteData"));
app.use("/api/real-market", require("./routes/realMarketData"));
app.use("/api/agri-expert", require("./routes/agriExpert"));
app.use("/api/tm", require("./routes/tm"));
app.use("/api/chat", require("./routes/chat"));
app.use("/api/computer-vision", require("./routes/computerVision"));

// Health check
app.get('/api/health', (req, res) => {
  const mongoose = require('mongoose');
  res.json({ 
    status: 'ok', 
    message: 'AgriCare 1.0 API is running',
    timestamp: new Date().toISOString(),
    database: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',
    environment: process.env.NODE_ENV
  });
});

// Serve React app for all other routes
app.get('*', (req, res) => {
  if (req.path.startsWith('/api/')) {
    return res.status(404).json({ error: 'API endpoint not found' });
  }
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Error handling
app.use((err, req, res, next) => {
  console.error('Server Error:', err);
  res.status(500).json({ error: 'Internal Server Error' });
});

// Start server
const PORT = process.env.PORT || 8080;
app.listen(PORT, () => {
  console.log(`🚀 AgriCare 1.0 server running on port ${PORT}`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV}`);
  console.log(`🗄️ Database: ${process.env.MONGODB_URI ? 'Azure Cosmos DB' : 'Not configured'}`);
});
SERVER_EOF

# Create deployment package
echo_info "Creating deployment package..."
cd "$DEPLOYMENT_DIR"
zip -r ../agricare-deployment.zip . -x "node_modules/*" "*.git*"
cd ..

# Deploy to Azure App Service
echo_info "Deploying to Azure App Service..."
az webapp deployment source config-zip \
  --resource-group "$RESOURCE_GROUP" \
  --name "$APP_NAME" \
  --src "agricare-deployment.zip"

# Configure App Service settings
echo_info "Configuring App Service settings..."
az webapp config appsettings set \
  --resource-group "$RESOURCE_GROUP" \
  --name "$APP_NAME" \
  --settings \
    NODE_ENV=production \
    WEBSITE_NODE_DEFAULT_VERSION=18.x \
    PORT=8080 \
    SCM_DO_BUILD_DURING_DEPLOYMENT=true \
    ENABLE_ORYX_BUILD=true

# Restart the app service
echo_info "Restarting App Service..."
az webapp restart \
  --resource-group "$RESOURCE_GROUP" \
  --name "$APP_NAME"

# Wait for deployment to complete
echo_info "Waiting for deployment to complete..."
sleep 60

# Test deployment
echo_info "Testing deployment..."
APP_URL="https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net"

# Test health endpoint
echo_info "Testing health endpoint..."
HEALTH_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL/api/health" || echo "000")
echo_info "Health endpoint status: $HEALTH_STATUS"

# Test main application
echo_info "Testing main application..."
APP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL" || echo "000")
echo_info "Main application status: $APP_STATUS"

# Test database connection
echo_info "Testing database connection..."
DB_RESPONSE=$(curl -s "$APP_URL/api/health" | grep -o '"database":"[^"]*"' || echo "database check failed")
echo_info "Database status: $DB_RESPONSE"

# Cleanup
echo_info "Cleaning up deployment files..."
rm -rf "$DEPLOYMENT_DIR"
rm -f "agricare-deployment.zip"

# Final status
if [ "$APP_STATUS" = "200" ] && [ "$HEALTH_STATUS" = "200" ]; then
    echo_success "🎉 AgriCare 1.0 deployment successful!"
    echo_success "🌐 Application URL: $APP_URL"
    echo_success "🏥 Health Check: $APP_URL/api/health"
    echo_success "🤖 AI Agent: Available with Azure Cosmos DB"
    echo_success "📱 Dashboard: All features enabled"
else
    echo_error "❌ Deployment verification failed"
    echo_error "App Status: $APP_STATUS, Health Status: $HEALTH_STATUS"
    echo_warning "Check Azure App Service logs for details"
    exit 1
fi

echo_success "✅ Deployment completed successfully!"
