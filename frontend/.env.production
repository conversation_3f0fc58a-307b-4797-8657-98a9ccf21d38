# Frontend Production Environment Variables for Azure Deployment

# Firebase Configuration for Production
VITE_FIREBASE_API_KEY=AIzaSyDxXxXxXxXxXxXxXxXxXxXxXxXxXxXxXxX
VITE_FIREBASE_AUTH_DOMAIN=agricare-1-0.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=agricare-1-0
VITE_FIREBASE_STORAGE_BUCKET=agricare-1-0.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789
VITE_FIREBASE_APP_ID=1:123456789:web:abcdef
VITE_FIREBASE_MEASUREMENT_ID=G-HTCNBBQYJD

# Firebase Development Mode (for testing with fixed OTP)
VITE_FIREBASE_DEV_MODE=true
VITE_DEFAULT_OTP=123456

# API Configuration - Production (Azure App Service)
VITE_API_BASE_URL=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net
VITE_API_BASE_URL_CHATBOT=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net
VITE_API_URL=https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net/api

# WebSocket Configuration
VITE_WS_URL=wss://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net

# Weather API Configuration
VITE_WEATHER_API_KEY=demo-key
VITE_OPENWEATHER_API_KEY=demo-key

# Feature Flags
VITE_ENABLE_AI_FEATURES=true
VITE_ENABLE_WEATHER_FORECAST=true
VITE_ENABLE_SOIL_ANALYSIS=true
VITE_ENABLE_MARKET_DATA=true
VITE_ENABLE_SATELLITE_DATA=true

# Azure Configuration
VITE_AZURE_SPEECH_KEY=d133bd0fbec34843afefea5fcbbb1242
VITE_AZURE_SPEECH_REGION=centralindia
VITE_AZURE_SPEECH_ENDPOINT=https://centralindia.tts.speech.microsoft.com/
VITE_AZURE_OPENAI_ENDPOINT=https://image-gpt4o.openai.azure.com/
VITE_AZURE_OPENAI_API_KEY=5kxS162ETTa3VMG7ou9OueTBgTgklpRtE758oSmJp1DyQMV1o7ZaJQQJ99BBACYeBjFXJ3w3AAABACOG5O2J
VITE_AZURE_DEPLOYMENT_NAME=gpt-4o
VITE_AZURE_API_VERSION=2024-08-01-preview

# Theme Configuration
VITE_THEME=dark
VITE_PRIMARY_COLOR=#4CAF50

# Cache Configuration
VITE_CACHE_DURATION=3600

# Environment
NODE_ENV=production
