import React, { useState, useEffect } from 'react';
import {
  TextField, Select, MenuItem, Button, Paper, Grid, Typography,
  FormControl, InputLabel, CircularProgress, Alert, Box, Chip,
  Accordion, AccordionSummary, AccordionDetails, IconButton,
  Dialog, DialogTitle, DialogContent, DialogActions, Divider,
  FormControlLabel, Switch, Tooltip, InputAdornment
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Science as ScienceIcon,
  Sensors as SensorsIcon,
  Create as CreateIcon,
  Info as InfoIcon
} from '@mui/icons-material';
// Using standard HTML date input instead of MUI DatePicker for compatibility

const EnhancedSoilDataForm = ({ 
  initialData, 
  onSubmit, 
  onCancel, 
  isEdit = false 
}) => {
  const [formData, setFormData] = useState({
    dataSource: 'manual',
    location: initialData?.location || {},
    soilData: {},
    labInfo: {},
    iotInfo: {},
    notes: '',
    testDate: new Date()
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [expandedSections, setExpandedSections] = useState({
    macronutrients: true,
    micronutrients: false,
    soilProperties: false,
    environmental: false,
    labInfo: false
  });

  // Soil nutrient definitions with units and normal ranges
  const nutrientDefinitions = {
    macronutrients: {
      title: 'Primary Macronutrients (NPK)',
      nutrients: {
        nitrogen: { label: 'Nitrogen (N)', unit: 'ppm', range: '20-50', description: 'Essential for plant growth and chlorophyll' },
        phosphorus: { label: 'Phosphorus (P)', unit: 'ppm', range: '15-30', description: 'Important for root development and flowering' },
        potassium: { label: 'Potassium (K)', unit: 'ppm', range: '150-300', description: 'Helps with disease resistance and water regulation' }
      }
    },
    secondaryMacronutrients: {
      title: 'Secondary Macronutrients',
      nutrients: {
        calcium: { label: 'Calcium (Ca)', unit: 'ppm', range: '1000-3000', description: 'Cell wall structure and pH buffering' },
        magnesium: { label: 'Magnesium (Mg)', unit: 'ppm', range: '120-400', description: 'Central atom in chlorophyll molecule' },
        sulfur: { label: 'Sulfur (S)', unit: 'ppm', range: '10-20', description: 'Protein synthesis and oil formation' }
      }
    },
    micronutrients: {
      title: 'Essential Micronutrients',
      nutrients: {
        iron: { label: 'Iron (Fe)', unit: 'ppm', range: '2.5-4.5', description: 'Chlorophyll synthesis and enzyme function' },
        manganese: { label: 'Manganese (Mn)', unit: 'ppm', range: '1-3', description: 'Photosynthesis and enzyme activation' },
        zinc: { label: 'Zinc (Zn)', unit: 'ppm', range: '0.5-1.0', description: 'Growth hormones and enzyme systems' },
        copper: { label: 'Copper (Cu)', unit: 'ppm', range: '0.2-0.5', description: 'Enzyme systems and lignin synthesis' },
        boron: { label: 'Boron (B)', unit: 'ppm', range: '0.5-1.0', description: 'Cell wall formation and sugar transport' },
        molybdenum: { label: 'Molybdenum (Mo)', unit: 'ppm', range: '0.1-0.3', description: 'Nitrogen fixation and nitrate reduction' },
        chlorine: { label: 'Chlorine (Cl)', unit: 'ppm', range: '2-20', description: 'Osmotic regulation and photosynthesis' },
        nickel: { label: 'Nickel (Ni)', unit: 'ppm', range: '0.05-0.1', description: 'Enzyme function and nitrogen metabolism' }
      }
    },
    soilProperties: {
      title: 'Soil Physical & Chemical Properties',
      nutrients: {
        ph: { label: 'pH Level', unit: 'pH', range: '6.0-7.5', description: 'Soil acidity/alkalinity affecting nutrient availability' },
        organicMatter: { label: 'Organic Matter', unit: '%', range: '2-5', description: 'Soil fertility and structure indicator' },
        organicCarbon: { label: 'Organic Carbon', unit: '%', range: '1-3', description: 'Carbon content in soil organic matter' },
        electricalConductivity: { label: 'Electrical Conductivity (EC)', unit: 'dS/m', range: '0-2', description: 'Soil salinity indicator' },
        cationExchangeCapacity: { label: 'Cation Exchange Capacity (CEC)', unit: 'meq/100g', range: '10-30', description: 'Soil nutrient holding capacity' }
      }
    },
    soilTexture: {
      title: 'Soil Texture',
      nutrients: {
        sand: { label: 'Sand Content', unit: '%', range: '20-60', description: 'Coarse particles affecting drainage' },
        clay: { label: 'Clay Content', unit: '%', range: '10-40', description: 'Fine particles affecting water retention' },
        silt: { label: 'Silt Content', unit: '%', range: '20-50', description: 'Medium particles affecting soil structure' }
      }
    },
    environmental: {
      title: 'Environmental Conditions',
      nutrients: {
        moisture: { label: 'Soil Moisture', unit: '%', range: '15-25', description: 'Current soil water content' },
        soilTemperature: { label: 'Soil Temperature', unit: '°C', range: '15-25', description: 'Temperature at root zone' },
        bulkDensity: { label: 'Bulk Density', unit: 'g/cm³', range: '1.0-1.6', description: 'Soil compaction indicator' },
        porosity: { label: 'Porosity', unit: '%', range: '40-60', description: 'Pore space for air and water' },
        waterHoldingCapacity: { label: 'Water Holding Capacity', unit: '%', range: '20-40', description: 'Maximum water retention capacity' },
        infiltrationRate: { label: 'Infiltration Rate', unit: 'mm/hr', range: '5-25', description: 'Water penetration rate' }
      }
    }
  };

  useEffect(() => {
    if (initialData) {
      setFormData(prev => ({
        ...prev,
        ...initialData,
        soilData: initialData.soilData || {},
        labInfo: initialData.labInfo || {},
        testDate: initialData.testDate ? new Date(initialData.testDate) : new Date()
      }));
    }
  }, [initialData]);

  const handleSoilDataChange = (nutrient, value) => {
    setFormData(prev => ({
      ...prev,
      soilData: {
        ...prev.soilData,
        [nutrient]: value === '' ? null : parseFloat(value)
      }
    }));
  };

  const handleLabInfoChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      labInfo: {
        ...prev.labInfo,
        [field]: value
      }
    }));
  };

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const renderNutrientFields = (categoryKey, category) => {
    return (
      <Grid container spacing={2}>
        {Object.entries(category.nutrients).map(([key, nutrient]) => (
          <Grid item xs={12} sm={6} md={4} key={key}>
            <Tooltip title={nutrient.description} arrow>
              <TextField
                fullWidth
                label={nutrient.label}
                type="number"
                value={formData.soilData[key] || ''}
                onChange={(e) => handleSoilDataChange(key, e.target.value)}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                        <Typography variant="caption" color="text.secondary">
                          {nutrient.unit}
                        </Typography>
                        <Typography variant="caption" color="success.main" sx={{ fontSize: '0.6rem' }}>
                          {nutrient.range}
                        </Typography>
                      </Box>
                    </InputAdornment>
                  )
                }}
                helperText={`Normal range: ${nutrient.range} ${nutrient.unit}`}
                size="small"
              />
            </Tooltip>
          </Grid>
        ))}
      </Grid>
    );
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const submitData = {
        ...formData,
        testDate: formData.testDate.toISOString(),
        dataQuality: 'high', // Manual entry is considered high quality
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      await onSubmit(submitData);
    } catch (error) {
      setError(error.message || 'Failed to save soil data');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box component="form" onSubmit={handleSubmit}>
        {/* Header */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            {isEdit ? 'Edit Soil Analysis Data' : 'Manual Soil Data Entry'}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Enter laboratory test results or field measurements for comprehensive soil analysis
          </Typography>
        </Box>

        {/* Data Source and Lab Information */}
        <Paper sx={{ p: 2, mb: 2 }}>
          <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
            Test Information
          </Typography>
          
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth size="small">
                <InputLabel>Data Source</InputLabel>
                <Select
                  value={formData.dataSource}
                  onChange={(e) => setFormData(prev => ({ ...prev, dataSource: e.target.value }))}
                  label="Data Source"
                >
                  <MenuItem value="manual">Manual Entry</MenuItem>
                  <MenuItem value="lab_testing">Laboratory Testing</MenuItem>
                  <MenuItem value="iot">IoT Sensors</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                size="small"
                label="Test Date"
                type="date"
                value={formData.testDate ? formData.testDate.toISOString().split('T')[0] : ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  testDate: e.target.value ? new Date(e.target.value) : new Date()
                }))}
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>

            {formData.dataSource === 'lab_testing' && (
              <>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Laboratory ID"
                    value={formData.labInfo.labId || ''}
                    onChange={(e) => handleLabInfoChange('labId', e.target.value)}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Analyst Name"
                    value={formData.labInfo.analyst || ''}
                    onChange={(e) => handleLabInfoChange('analyst', e.target.value)}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Test Method"
                    value={formData.labInfo.method || ''}
                    onChange={(e) => handleLabInfoChange('method', e.target.value)}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Accuracy (%)"
                    type="number"
                    value={formData.labInfo.accuracy || ''}
                    onChange={(e) => handleLabInfoChange('accuracy', e.target.value)}
                  />
                </Grid>
              </>
            )}
          </Grid>
        </Paper>

        {/* Nutrient Sections */}
        {Object.entries(nutrientDefinitions).map(([categoryKey, category]) => (
          <Accordion 
            key={categoryKey}
            expanded={expandedSections[categoryKey]}
            onChange={() => toggleSection(categoryKey)}
            sx={{ mb: 1 }}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <ScienceIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                  {category.title}
                </Typography>
                <Chip 
                  label={`${Object.keys(category.nutrients).length} parameters`}
                  size="small"
                  sx={{ ml: 2 }}
                />
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              {renderNutrientFields(categoryKey, category)}
            </AccordionDetails>
          </Accordion>
        ))}

        {/* Notes Section */}
        <Paper sx={{ p: 2, mb: 2 }}>
          <TextField
            fullWidth
            multiline
            rows={3}
            label="Additional Notes"
            value={formData.notes}
            onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
            placeholder="Enter any additional observations, field conditions, or remarks..."
          />
        </Paper>

        {/* Error Display */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Action Buttons */}
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
          <Button
            variant="outlined"
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
          >
            {loading ? 'Saving...' : (isEdit ? 'Update Data' : 'Save Soil Data')}
          </Button>
        </Box>
      </Box>
    );
};

export default EnhancedSoilDataForm;
