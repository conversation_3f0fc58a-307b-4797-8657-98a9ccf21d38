import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  TextField,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Grid,
  Paper,
  Divider,
  Alert,
  CircularProgress,
  Tab,
  Tabs,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Person as PersonIcon,
  Phone as PhoneIcon,
  Security as SecurityIcon,
  Visibility as VisibilityIcon,
  Chat as ChatIcon,
  Image as ImageIcon,
  Search as SearchIcon,
  AdminPanelSettings as AdminIcon
} from '@mui/icons-material';
import UserDisplay from './UserDisplay';

/**
 * TM User Management Component
 * Allows Territory Managers to view and manage farmer data
 */
const TMUserManagement = ({ currentUser }) => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [searchPhone, setSearchPhone] = useState('');
  const [selectedUser, setSelectedUser] = useState(null);
  const [userList, setUserList] = useState([]);
  const [chatHistory, setChatHistory] = useState([]);
  const [analysisHistory, setAnalysisHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showUserDetails, setShowUserDetails] = useState(false);

  // Check if current user is TM/Admin
  const isAdmin = currentUser?.role === 'TM' || currentUser?.role === 'Admin' || 
                  ['9611966747', '9999999999'].includes(currentUser?.phoneNumber || currentUser?.phone);

  useEffect(() => {
    if (isAdmin) {
      fetchUserList();
    }
  }, [isAdmin]);

  const fetchUserList = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/chat/admin/users');
      if (response.ok) {
        const data = await response.json();
        setUserList(data.data.users || []);
      } else {
        setError('Failed to fetch user list');
      }
    } catch (error) {
      console.error('Error fetching user list:', error);
      setError('Error fetching user list');
    } finally {
      setLoading(false);
    }
  };

  const fetchUserData = async (phoneNumber, userName) => {
    try {
      setLoading(true);
      setError('');
      
      // Fetch chat history
      const chatResponse = await fetch(`/api/chat/history?phoneNumber=${encodeURIComponent(phoneNumber)}`);
      if (chatResponse.ok) {
        const chatData = await chatResponse.json();
        setChatHistory(chatData.data.chatHistory || []);
      }

      // Fetch analysis history
      const analysisResponse = await fetch(`/api/chat/analysis-history?phoneNumber=${encodeURIComponent(phoneNumber)}`);
      if (analysisResponse.ok) {
        const analysisData = await analysisResponse.json();
        setAnalysisHistory(analysisData.data.analyses || []);
      }

      setSelectedUser({ phone: phoneNumber, name: userName });
      setShowUserDetails(true);
    } catch (error) {
      console.error('Error fetching user data:', error);
      setError('Error fetching user data');
    } finally {
      setLoading(false);
    }
  };

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    if (searchPhone.trim()) {
      const normalizedPhone = searchPhone.replace(/\D/g, '').slice(-10);
      const userName = getUserNameByPhone(normalizedPhone);
      fetchUserData(normalizedPhone, userName);
    }
  };

  const getUserNameByPhone = (phoneNumber) => {
    const phoneToNameMapping = {
      '9611966747': 'Manish Kumar',
      '9876543210': 'Test User',
      '9999999999': 'Admin User',
    };
    
    const normalizedPhone = phoneNumber?.replace(/\D/g, '').slice(-10);
    return phoneToNameMapping[normalizedPhone] || `User ${normalizedPhone?.slice(-4) || 'Unknown'}`;
  };

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  if (!isAdmin) {
    return (
      <Card>
        <CardContent>
          <Alert severity="warning">
            <Typography variant="h6">Access Restricted</Typography>
            <Typography>
              This feature is only available to Territory Managers and Administrators.
            </Typography>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Box sx={{ width: '100%', p: 2 }}>
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <AdminIcon sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h5" component="h2">
              Territory Manager - User Management
            </Typography>
          </Box>
          
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            View and manage farmer data, chat history, and image analysis results.
          </Typography>

          {/* Search Section */}
          <Paper sx={{ p: 2, mb: 3, bgcolor: 'primary.light', color: 'primary.contrastText' }}>
            <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
              <SearchIcon sx={{ mr: 1 }} />
              Search User by Phone Number
            </Typography>
            
            <Box component="form" onSubmit={handleSearchSubmit} sx={{ display: 'flex', gap: 2 }}>
              <TextField
                fullWidth
                variant="outlined"
                placeholder="Enter phone number (e.g., 9611966747)"
                value={searchPhone}
                onChange={(e) => setSearchPhone(e.target.value)}
                sx={{ bgcolor: 'white', borderRadius: 1 }}
                size="small"
              />
              <Button
                type="submit"
                variant="contained"
                color="secondary"
                disabled={loading}
                sx={{ minWidth: 120 }}
              >
                {loading ? <CircularProgress size={20} /> : 'Search'}
              </Button>
            </Box>
          </Paper>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {/* User List */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                  <PersonIcon sx={{ mr: 1 }} />
                  Registered Users ({userList.length})
                </Typography>
                
                <List sx={{ maxHeight: 400, overflow: 'auto' }}>
                  {userList.map((user, index) => (
                    <ListItem
                      key={index}
                      button
                      onClick={() => fetchUserData(user.phone, user.name)}
                      sx={{
                        border: '1px solid #e0e0e0',
                        borderRadius: 1,
                        mb: 1,
                        '&:hover': { bgcolor: 'action.hover' }
                      }}
                    >
                      <ListItemIcon>
                        <PersonIcon />
                      </ListItemIcon>
                      <ListItemText
                        primary={user.name}
                        secondary={
                          <Box>
                            <Typography variant="body2" component="span">
                              📱 {user.phone}
                            </Typography>
                            <Chip
                              label={user.role}
                              size="small"
                              color={user.role === 'TM' ? 'primary' : 'default'}
                              sx={{ ml: 1 }}
                            />
                          </Box>
                        }
                      />
                      <VisibilityIcon color="action" />
                    </ListItem>
                  ))}
                </List>
              </Paper>
            </Grid>

            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Quick Actions
                </Typography>
                
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Button
                    variant="outlined"
                    startIcon={<PersonIcon />}
                    onClick={fetchUserList}
                    disabled={loading}
                  >
                    Refresh User List
                  </Button>
                  
                  <Button
                    variant="outlined"
                    startIcon={<VisibilityIcon />}
                    onClick={() => fetchUserData(currentUser?.phoneNumber || '9611966747', currentUser?.name || 'Current User')}
                  >
                    View My Data
                  </Button>
                </Box>

                {selectedUser && (
                  <Box sx={{ mt: 3, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
                    <Typography variant="subtitle2" color="info.contrastText">
                      Currently Viewing:
                    </Typography>
                    <Typography variant="h6" color="info.contrastText">
                      {selectedUser.name} ({selectedUser.phone})
                    </Typography>
                    <Typography variant="body2" color="info.contrastText">
                      Chat Messages: {chatHistory.length} | Analysis: {analysisHistory.length}
                    </Typography>
                  </Box>
                )}
              </Paper>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* User Details Dialog */}
      <Dialog
        open={showUserDetails}
        onClose={() => setShowUserDetails(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <PersonIcon sx={{ mr: 1 }} />
            User Data: {selectedUser?.name} ({selectedUser?.phone})
          </Box>
        </DialogTitle>
        
        <DialogContent>
          <Tabs value={selectedTab} onChange={handleTabChange} sx={{ mb: 2 }}>
            <Tab icon={<ChatIcon />} label={`Chat History (${chatHistory.length})`} />
            <Tab icon={<ImageIcon />} label={`Image Analysis (${analysisHistory.length})`} />
          </Tabs>

          {selectedTab === 0 && (
            <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
              {chatHistory.length > 0 ? (
                chatHistory.map((chat, index) => (
                  <Paper key={index} sx={{ p: 2, mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      {new Date(chat.timestamp).toLocaleString()}
                    </Typography>
                    <Typography variant="body1" sx={{ mt: 1 }}>
                      <strong>User:</strong> {chat.userMessage}
                    </Typography>
                    <Typography variant="body1" sx={{ mt: 1 }}>
                      <strong>AI:</strong> {chat.aiResponse}
                    </Typography>
                  </Paper>
                ))
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No chat history found for this user.
                </Typography>
              )}
            </Box>
          )}

          {selectedTab === 1 && (
            <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
              {analysisHistory.length > 0 ? (
                analysisHistory.map((analysis, index) => (
                  <Paper key={index} sx={{ p: 2, mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      {new Date(analysis.created_at || analysis.createdAt).toLocaleString()}
                    </Typography>
                    <Typography variant="body1" sx={{ mt: 1 }}>
                      <strong>Image:</strong> {analysis.imageName || 'Unknown'}
                    </Typography>
                    <Typography variant="body1" sx={{ mt: 1 }}>
                      <strong>Analysis:</strong> {analysis.summary || 'Analysis completed'}
                    </Typography>
                  </Paper>
                ))
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No image analysis history found for this user.
                </Typography>
              )}
            </Box>
          )}
        </DialogContent>
        
        <DialogActions>
          <Button onClick={() => setShowUserDetails(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TMUserManagement;
