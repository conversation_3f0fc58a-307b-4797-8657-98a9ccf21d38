import React, { useState, useEffect } from 'react';
import {
  Person as User,
  Phone,
  Security as Shield,
  Visibility as Eye
} from '@mui/icons-material';

/**
 * UserDisplay Component
 * Shows user name based on phone number with admin features
 */
const UserDisplay = ({ 
  phoneNumber, 
  showPhone = true, 
  showRole = false, 
  isAdmin = false,
  onUserSelect = null 
}) => {
  const [userName, setUserName] = useState('');
  const [userRole, setUserRole] = useState('');
  const [loading, setLoading] = useState(true);

  // Phone number to name mapping (matches backend)
  const phoneToNameMapping = {
    '9611966747': 'Man<PERSON> Kumar',
    '9876543210': 'Test User',
    '9999999999': 'Admin User',
  };

  useEffect(() => {
    const fetchUserName = async () => {
      if (!phoneNumber) {
        setLoading(false);
        return;
      }

      try {
        // Normalize phone number
        const normalizedPhone = phoneNumber.replace(/\D/g, '').slice(-10);
        
        // Check local mapping first
        if (phoneToNameMapping[normalizedPhone]) {
          setUserName(phoneToNameMapping[normalizedPhone]);
          setUserRole('User');
          setLoading(false);
          return;
        }

        // Try to fetch from API
        const response = await fetch(`/api/users/by-phone/${normalizedPhone}`);
        if (response.ok) {
          const userData = await response.json();
          setUserName(userData.name || `User ${normalizedPhone.slice(-4)}`);
          setUserRole(userData.role || 'User');
        } else {
          // Fallback to default name
          setUserName(`User ${normalizedPhone.slice(-4)}`);
          setUserRole('User');
        }
      } catch (error) {
        console.error('Error fetching user name:', error);
        const normalizedPhone = phoneNumber.replace(/\D/g, '').slice(-10);
        setUserName(`User ${normalizedPhone.slice(-4)}`);
        setUserRole('User');
      } finally {
        setLoading(false);
      }
    };

    fetchUserName();
  }, [phoneNumber]);

  const handleUserClick = () => {
    if (isAdmin && onUserSelect) {
      onUserSelect(phoneNumber, userName);
    }
  };

  if (loading) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px', color: '#666' }}>
        <div style={{
          width: '16px',
          height: '16px',
          border: '2px solid #f3f3f3',
          borderTop: '2px solid #1976d2',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
        <span>Loading...</span>
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        padding: isAdmin && onUserSelect ? '8px' : '0',
        borderRadius: '4px',
        cursor: isAdmin && onUserSelect ? 'pointer' : 'default',
        backgroundColor: 'transparent'
      }}
      onClick={handleUserClick}
      onMouseEnter={(e) => {
        if (isAdmin && onUserSelect) {
          e.target.style.backgroundColor = '#f5f5f5';
        }
      }}
      onMouseLeave={(e) => {
        if (isAdmin && onUserSelect) {
          e.target.style.backgroundColor = 'transparent';
        }
      }}
    >
      <User style={{ fontSize: '16px', color: '#666' }} />
      <div style={{ display: 'flex', flexDirection: 'column' }}>
        <span style={{ fontWeight: 500, color: '#333' }}>{userName}</span>
        {showPhone && (
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px', fontSize: '14px', color: '#666' }}>
            <Phone style={{ fontSize: '12px' }} />
            <span>{phoneNumber}</span>
          </div>
        )}
        {showRole && userRole && (
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px', fontSize: '12px', color: '#1976d2' }}>
            <Shield style={{ fontSize: '12px' }} />
            <span>{userRole}</span>
          </div>
        )}
      </div>
      {isAdmin && onUserSelect && (
        <Eye style={{ fontSize: '16px', color: '#1976d2' }} />
      )}
    </div>
  );
};

/**
 * AdminUserSelector Component
 * Allows admin to select a user by phone number to view their data
 */
const AdminUserSelector = ({ onUserSelect, currentUser }) => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [userList, setUserList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showUserList, setShowUserList] = useState(false);

  const fetchUserList = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/chat/admin/users');
      if (response.ok) {
        const data = await response.json();
        setUserList(data.data.users || []);
        setShowUserList(true);
      }
    } catch (error) {
      console.error('Error fetching user list:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUserSelect = (phone, name) => {
    setPhoneNumber(phone);
    setShowUserList(false);
    if (onUserSelect) {
      onUserSelect(phone, name);
    }
  };

  const handlePhoneSubmit = (e) => {
    e.preventDefault();
    if (phoneNumber.trim()) {
      const normalizedPhone = phoneNumber.replace(/\D/g, '').slice(-10);
      if (onUserSelect) {
        onUserSelect(normalizedPhone, `User ${normalizedPhone.slice(-4)}`);
      }
    }
  };

  return (
    <div style={{
      backgroundColor: '#e3f2fd',
      border: '1px solid #bbdefb',
      borderRadius: '8px',
      padding: '16px',
      marginBottom: '16px'
    }}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '12px' }}>
        <Shield style={{ fontSize: '20px', color: '#1976d2' }} />
        <h3 style={{ fontWeight: 500, color: '#0d47a1', margin: 0 }}>Admin: View User Data</h3>
      </div>

      <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
        <form onSubmit={handlePhoneSubmit} style={{ display: 'flex', gap: '8px' }}>
          <input
            type="text"
            placeholder="Enter phone number (e.g., 9611966747)"
            value={phoneNumber}
            onChange={(e) => setPhoneNumber(e.target.value)}
            style={{
              flex: 1,
              padding: '8px 12px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              fontSize: '14px',
              outline: 'none'
            }}
            onFocus={(e) => e.target.style.borderColor = '#1976d2'}
            onBlur={(e) => e.target.style.borderColor = '#ccc'}
          />
          <button
            type="submit"
            style={{
              padding: '8px 16px',
              backgroundColor: '#1976d2',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
            onMouseEnter={(e) => e.target.style.backgroundColor = '#1565c0'}
            onMouseLeave={(e) => e.target.style.backgroundColor = '#1976d2'}
          >
            View User
          </button>
        </form>

        <div style={{ display: 'flex', gap: '8px' }}>
          <button
            onClick={fetchUserList}
            disabled={loading}
            style={{
              padding: '4px 12px',
              fontSize: '12px',
              backgroundColor: '#f5f5f5',
              color: '#666',
              border: 'none',
              borderRadius: '4px',
              cursor: loading ? 'not-allowed' : 'pointer',
              opacity: loading ? 0.5 : 1
            }}
            onMouseEnter={(e) => {
              if (!loading) e.target.style.backgroundColor = '#e0e0e0';
            }}
            onMouseLeave={(e) => {
              if (!loading) e.target.style.backgroundColor = '#f5f5f5';
            }}
          >
            {loading ? 'Loading...' : 'Show All Users'}
          </button>
          <button
            onClick={() => handleUserSelect(currentUser?.phone || '9611966747', currentUser?.name || 'Current User')}
            style={{
              padding: '4px 12px',
              fontSize: '12px',
              backgroundColor: '#e8f5e8',
              color: '#2e7d32',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
            onMouseEnter={(e) => e.target.style.backgroundColor = '#c8e6c9'}
            onMouseLeave={(e) => e.target.style.backgroundColor = '#e8f5e8'}
          >
            View My Data
          </button>
        </div>

        {showUserList && (
          <div style={{
            maxHeight: '240px',
            overflowY: 'auto',
            border: '1px solid #e0e0e0',
            borderRadius: '4px'
          }}>
            <div style={{
              padding: '8px',
              backgroundColor: '#f5f5f5',
              borderBottom: '1px solid #e0e0e0'
            }}>
              <span style={{ fontSize: '14px', fontWeight: 500, color: '#666' }}>Select User:</span>
            </div>
            {userList.map((user, index) => (
              <div
                key={index}
                style={{
                  padding: '8px',
                  borderBottom: '1px solid #f0f0f0',
                  cursor: 'pointer',
                  backgroundColor: 'white'
                }}
                onClick={() => handleUserSelect(user.phone, user.name)}
                onMouseEnter={(e) => e.target.style.backgroundColor = '#f5f5f5'}
                onMouseLeave={(e) => e.target.style.backgroundColor = 'white'}
              >
                <UserDisplay
                  phoneNumber={user.phone}
                  showPhone={true}
                  showRole={true}
                  isAdmin={false}
                />
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default UserDisplay;
export { AdminUserSelector };
