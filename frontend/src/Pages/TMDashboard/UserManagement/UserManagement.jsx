import React from 'react';
import { Box, Typography, Paper } from '@mui/material';
import { useAuth } from '../../../contexts/AuthContext';
import TMUserManagement from '../../../Components/TMUserManagement';

/**
 * User Management Page for TM Dashboard
 * Allows Territory Managers to view and manage farmer chat/image data
 */
const UserManagement = () => {
  const { currentUser } = useAuth();

  return (
    <Box sx={{ width: '100%' }}>
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          User Management
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
          View and manage farmer data, chat history, and image analysis results.
          As a Territory Manager, you can access any farmer's data to provide better support.
        </Typography>
      </Paper>

      <TMUserManagement currentUser={currentUser} />
    </Box>
  );
};

export default UserManagement;
