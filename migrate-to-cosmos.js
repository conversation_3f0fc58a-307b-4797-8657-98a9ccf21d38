#!/usr/bin/env node

/**
 * AgriCare 1.0 - Local MongoDB to Azure Cosmos DB Migration Script
 * This script migrates all data from local MongoDB to Azure Cosmos DB
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Connection strings
const LOCAL_MONGODB_URI = 'mongodb://localhost:27017/agricare';
const COSMOS_DB_URI = '*******************************************************************************************************************************************************************************************************************************************';

// Collections to migrate
const COLLECTIONS_TO_MIGRATE = [
  'users',
  'farms',
  'farmers',
  'aiconversations',
  'imageanalyses',
  'chathistory',
  'experts',
  'institutions',
  'research_centers',
  'weather_data',
  'soil_analysis',
  'market_data',
  'appointments',
  'notifications',
  'analytics',
  'tasks',
  'livestock',
  'healthrecords',
  'vetappointments',
  'farmfinancials'
];

class DataMigrator {
  constructor() {
    this.localConnection = null;
    this.cosmosConnection = null;
    this.migrationStats = {
      totalCollections: 0,
      migratedCollections: 0,
      totalDocuments: 0,
      migratedDocuments: 0,
      errors: []
    };
  }

  async connectToLocalMongoDB() {
    try {
      console.log('🔄 Connecting to local MongoDB...');
      this.localConnection = await mongoose.createConnection(LOCAL_MONGODB_URI, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        serverSelectionTimeoutMS: 5000
      });
      console.log('✅ Connected to local MongoDB');
      return true;
    } catch (error) {
      console.error('❌ Failed to connect to local MongoDB:', error.message);
      return false;
    }
  }

  async connectToCosmosDB() {
    try {
      console.log('🔄 Connecting to Azure Cosmos DB...');
      this.cosmosConnection = await mongoose.createConnection(COSMOS_DB_URI, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        serverSelectionTimeoutMS: 30000,
        socketTimeoutMS: 45000,
        maxPoolSize: 10,
        bufferMaxEntries: 0,
        retryWrites: false,
        ssl: true
      });
      console.log('✅ Connected to Azure Cosmos DB');
      
      // Test the connection
      await this.cosmosConnection.db.admin().ping();
      console.log('🏓 Cosmos DB ping successful');
      return true;
    } catch (error) {
      console.error('❌ Failed to connect to Azure Cosmos DB:', error.message);
      return false;
    }
  }

  async getCollectionData(connection, collectionName) {
    try {
      const collection = connection.db.collection(collectionName);
      const documents = await collection.find({}).toArray();
      console.log(`📊 Found ${documents.length} documents in ${collectionName}`);
      return documents;
    } catch (error) {
      console.error(`❌ Error reading ${collectionName}:`, error.message);
      return [];
    }
  }

  async insertDocuments(connection, collectionName, documents) {
    if (documents.length === 0) {
      console.log(`⏭️ Skipping ${collectionName} - no documents to migrate`);
      return 0;
    }

    try {
      const collection = connection.db.collection(collectionName);
      
      // Clear existing data in Cosmos DB collection
      await collection.deleteMany({});
      console.log(`🗑️ Cleared existing data in ${collectionName}`);
      
      // Insert documents in batches
      const batchSize = 100;
      let insertedCount = 0;
      
      for (let i = 0; i < documents.length; i += batchSize) {
        const batch = documents.slice(i, i + batchSize);
        const result = await collection.insertMany(batch, { ordered: false });
        insertedCount += result.insertedCount;
        console.log(`📝 Inserted batch ${Math.floor(i/batchSize) + 1} for ${collectionName}: ${result.insertedCount} documents`);
      }
      
      console.log(`✅ Successfully migrated ${insertedCount} documents to ${collectionName}`);
      return insertedCount;
    } catch (error) {
      console.error(`❌ Error inserting into ${collectionName}:`, error.message);
      this.migrationStats.errors.push(`${collectionName}: ${error.message}`);
      return 0;
    }
  }

  async migrateCollection(collectionName) {
    console.log(`\n🔄 Migrating collection: ${collectionName}`);
    
    try {
      // Get data from local MongoDB
      const documents = await this.getCollectionData(this.localConnection, collectionName);
      this.migrationStats.totalDocuments += documents.length;
      
      // Insert data into Cosmos DB
      const migratedCount = await this.insertDocuments(this.cosmosConnection, collectionName, documents);
      this.migrationStats.migratedDocuments += migratedCount;
      this.migrationStats.migratedCollections++;
      
      console.log(`✅ Migration completed for ${collectionName}: ${migratedCount}/${documents.length} documents`);
    } catch (error) {
      console.error(`❌ Failed to migrate ${collectionName}:`, error.message);
      this.migrationStats.errors.push(`${collectionName}: ${error.message}`);
    }
  }

  async createIndexes() {
    console.log('\n🔄 Creating indexes in Cosmos DB...');
    
    try {
      const db = this.cosmosConnection.db;
      
      // Create indexes for better performance
      const indexOperations = [
        { collection: 'users', index: { email: 1 }, options: { unique: true } },
        { collection: 'users', index: { mobile: 1 } },
        { collection: 'farms', index: { owner_id: 1 } },
        { collection: 'farms', index: { location: '2dsphere' } },
        { collection: 'experts', index: { specialization: 1 } },
        { collection: 'aiconversations', index: { userId: 1 } },
        { collection: 'aiconversations', index: { createdAt: 1 } },
        { collection: 'chathistory', index: { userId: 1 } },
        { collection: 'imageanalyses', index: { userId: 1 } },
        { collection: 'weather_data', index: { timestamp: 1 } },
        { collection: 'soil_analysis', index: { farm_id: 1 } }
      ];
      
      for (const { collection, index, options } of indexOperations) {
        try {
          await db.collection(collection).createIndex(index, options || {});
          console.log(`✅ Created index for ${collection}:`, index);
        } catch (error) {
          console.log(`⚠️ Index creation warning for ${collection}:`, error.message);
        }
      }
      
      console.log('✅ Index creation completed');
    } catch (error) {
      console.error('❌ Error creating indexes:', error.message);
    }
  }

  async verifyMigration() {
    console.log('\n🔍 Verifying migration...');
    
    try {
      const cosmosDb = this.cosmosConnection.db;
      const collections = await cosmosDb.listCollections().toArray();
      
      console.log(`📊 Collections in Cosmos DB: ${collections.length}`);
      
      for (const collection of collections) {
        const count = await cosmosDb.collection(collection.name).countDocuments();
        console.log(`📋 ${collection.name}: ${count} documents`);
      }
      
      console.log('✅ Migration verification completed');
    } catch (error) {
      console.error('❌ Error verifying migration:', error.message);
    }
  }

  async migrate() {
    console.log('🚀 Starting AgriCare 1.0 data migration...\n');
    
    // Connect to both databases
    const localConnected = await this.connectToLocalMongoDB();
    if (!localConnected) {
      console.error('❌ Cannot proceed without local MongoDB connection');
      return false;
    }
    
    const cosmosConnected = await this.connectToCosmosDB();
    if (!cosmosConnected) {
      console.error('❌ Cannot proceed without Cosmos DB connection');
      return false;
    }
    
    // Migrate each collection
    this.migrationStats.totalCollections = COLLECTIONS_TO_MIGRATE.length;
    
    for (const collectionName of COLLECTIONS_TO_MIGRATE) {
      await this.migrateCollection(collectionName);
    }
    
    // Create indexes
    await this.createIndexes();
    
    // Verify migration
    await this.verifyMigration();
    
    // Print migration summary
    this.printMigrationSummary();
    
    // Close connections
    await this.closeConnections();
    
    return this.migrationStats.errors.length === 0;
  }

  printMigrationSummary() {
    console.log('\n📊 MIGRATION SUMMARY');
    console.log('='.repeat(50));
    console.log(`Total Collections: ${this.migrationStats.totalCollections}`);
    console.log(`Migrated Collections: ${this.migrationStats.migratedCollections}`);
    console.log(`Total Documents: ${this.migrationStats.totalDocuments}`);
    console.log(`Migrated Documents: ${this.migrationStats.migratedDocuments}`);
    console.log(`Errors: ${this.migrationStats.errors.length}`);
    
    if (this.migrationStats.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      this.migrationStats.errors.forEach(error => console.log(`  - ${error}`));
    }
    
    if (this.migrationStats.errors.length === 0) {
      console.log('\n✅ MIGRATION COMPLETED SUCCESSFULLY!');
      console.log('🗄️ All data has been migrated to Azure Cosmos DB');
      console.log('🚀 Your application can now use the Cosmos DB connection');
    } else {
      console.log('\n⚠️ MIGRATION COMPLETED WITH ERRORS');
      console.log('Please review the errors above and retry if necessary');
    }
  }

  async closeConnections() {
    try {
      if (this.localConnection) {
        await this.localConnection.close();
        console.log('🔌 Local MongoDB connection closed');
      }
      if (this.cosmosConnection) {
        await this.cosmosConnection.close();
        console.log('🔌 Cosmos DB connection closed');
      }
    } catch (error) {
      console.error('❌ Error closing connections:', error.message);
    }
  }
}

// Run migration if this script is executed directly
if (require.main === module) {
  const migrator = new DataMigrator();
  migrator.migrate()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Migration failed:', error);
      process.exit(1);
    });
}

module.exports = DataMigrator;
