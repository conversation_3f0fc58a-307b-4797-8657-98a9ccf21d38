const admin = require('../config/firebase.config');
const User = require('../models/user.model');

const protect = async (req, res, next) => {
    try {
        const token = req.headers.authorization?.split('Bearer ')[1];

        if (!token) {
            return res.status(401).json({ message: 'Not authorized, no token' });
        }

        // Development bypass for testing
        if (token === 'test-token' && process.env.NODE_ENV !== 'production') {
            // Use test TM user for development
            let testUser = await User.findOne({ phoneNumber: 'tm_test_user' });
            if (!testUser) {
                // Create test user if it doesn't exist
                testUser = await User.create({
                    firebaseUid: 'test_tm_uid_123',
                    phoneNumber: 'tm_test_user',
                    name: 'Test TM User',
                    email: '<EMAIL>', // Add email to avoid null constraint
                    role: 'TM'
                });
                console.log('✅ Created test TM user for AgriExpert API');
            }
            req.user = testUser;
            return next();
        }

        // Verify Firebase token
        const decodedToken = await admin.auth().verifyIdToken(token);

        // Get or create user in our MongoDB
        let user = await User.findOne({ firebaseUid: decodedToken.uid });

        if (!user) {
            // Create new user if doesn't exist
            user = await User.create({
                firebaseUid: decodedToken.uid,
                phoneNumber: decodedToken.phone_number || `user_${decodedToken.uid.slice(-4)}`,
                name: decodedToken.name || `User${decodedToken.uid.slice(-4)}`,
                role: 'user'
            });
        }

        req.user = user;
        next();
    } catch (error) {
        console.error('Auth Error:', error);
        res.status(401).json({ message: 'Not authorized' });
    }
};

const isAdmin = (req, res, next) => {
    if (req.user && req.user.role === 'admin') {
        next();
    } else {
        res.status(401).json({ message: 'Not authorized as an admin' });
    }
};

module.exports = { protect, isAdmin }; 