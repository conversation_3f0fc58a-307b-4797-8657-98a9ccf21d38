const roleCheck = (allowedRoles) => {
    return (req, res, next) => {
        try {
            // Get user role from the authenticated request
            const userRole = req.user ? req.user.role : null;

            if (!userRole) {
                return res.status(401).json({
                    success: false,
                    message: 'Authentication required'
                });
            }

            // Check if user's role is in the allowed roles
            if (!allowedRoles.includes(userRole)) {
                return res.status(403).json({
                    success: false,
                    message: 'Access denied. Insufficient permissions'
                });
            }

            // Role check passed, proceed to the next middleware
            next();
        } catch (error) {
            console.error('Role check error:', error);
            return res.status(500).json({
                success: false,
                message: 'Internal server error during role verification'
            });
        }
    };
};

module.exports = roleCheck; 