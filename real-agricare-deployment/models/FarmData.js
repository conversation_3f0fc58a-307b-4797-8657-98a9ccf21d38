const mongoose = require('mongoose');

const farmDataSchema = new mongoose.Schema({
  farmId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Farm',
    required: true,
    index: true
  },
  soilData: {
    nitrogen: { type: Number, required: true },
    phosphorus: { type: Number, required: true },
    potassium: { type: Number, required: true },
    ph: { type: Number, required: true },
    moisture: { type: Number, required: true }
  },
  weatherData: {
    temperature: { type: Number, required: true },
    humidity: { type: Number, required: true },
    rainfall: { type: Number, default: 0 }
  },
  alerts: [{
    type: {
      type: String,
      enum: ['Critical', 'Warning', 'Info'],
      required: true
    },
    message: { type: String, required: true },
    timestamp: { type: Date, default: Date.now }
  }],
  tasks: [{
    title: { type: String, required: true },
    description: { type: String },
    status: {
      type: String,
      enum: ['Pending', 'In Progress', 'Completed'],
      default: 'Pending'
    },
    dueDate: { type: Date },
    priority: {
      type: String,
      enum: ['Low', 'Medium', 'High'],
      default: 'Medium'
    }
  }],
  timestamp: {
    type: Date,
    default: Date.now
  }
});

// Index for efficient querying
farmDataSchema.index({ farmId: 1, timestamp: -1 });

module.exports = mongoose.model('FarmData', farmDataSchema); 