const mongoose = require("mongoose");

const LivestockSchema = new mongoose.Schema({
  imageUrl: { type: String, required: true },
  analysis: { type: Object, required: true },
  year: { type: Number, default: new Date().getFullYear() },
  farmerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Farmer',
    required: true
  },
  livestockType: {
    type: String,
    enum: ['cattle', 'sheep', 'goat', 'pig', 'poultry', 'horse', 'other'],
    required: true
  },
  healthStatus: {
    type: String,
    enum: ['healthy', 'concerning', 'critical', 'sick', 'recovering', 'unknown'],
    default: 'unknown'
  },
  age: {
    type: Number,
    required: true
  },
  weight: {
    type: Number,
    required: true
  },
  breed: {
    type: String,
    required: true
  },
  notes: {
    type: String,
    default: ''
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Update the updatedAt timestamp before saving
LivestockSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Add indexes for better query performance
LivestockSchema.index({ farmerId: 1, year: -1 });
LivestockSchema.index({ livestockType: 1 });
LivestockSchema.index({ healthStatus: 1 });

module.exports = mongoose.model("Livestock", LivestockSchema);

