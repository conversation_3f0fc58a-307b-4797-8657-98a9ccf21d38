const mongoose = require('mongoose');

const soilDataSchema = new mongoose.Schema({
  location: {
    coordinates: {
      lat: { type: Number, required: true },
      lng: { type: Number, required: true }
    },
    state: String,
    district: String,
    block: String,
    village: String,
    plot: String,
    level: {
      type: String,
      enum: ['coordinates', 'state', 'district', 'block', 'village', 'plot'],
      required: true
    }
  },
  dataSource: {
    type: String,
    enum: ['satellite', 'iot', 'lab_testing', 'manual'],
    required: true
  },
  soilData: {
    // From SoilGrids API (real data)
    nitrogen: { type: Number }, // g/kg - from SoilGrids
    ph: { type: Number }, // pH units - from SoilGrids
    organicMatter: { type: Number }, // g/kg - from SoilGrids (SOC)
    sand: { type: Number }, // g/kg - from SoilGrids
    clay: { type: Number }, // g/kg - from SoilGrids
    silt: { type: Number }, // g/kg - from SoilGrids

    // From NASA POWER API (real data)
    moisture: { type: Number }, // mm/day - precipitation as moisture indicator
    temperature: { type: Number }, // °C - air temperature
    precipitation: { type: Number }, // mm/day - from NASA POWER
    humidity: { type: Number }, // % - relative humidity
    soilTemperature: { type: Number }, // °C - earth skin temperature

    // Not available from current APIs
    phosphorus: { type: Number }, // Not available in SoilGrids v2.0
    potassium: { type: Number } // Not available in SoilGrids v2.0
  },
  labInfo: {
    labId: String,
    analyst: String,
    testDate: Date,
    method: String,
    accuracy: Number
  },
  iotInfo: {
    sensorId: String,
    deviceId: String,
    readingTime: Date,
    batteryLevel: Number,
    signalStrength: Number
  },
  dataQuality: {
    type: String,
    enum: ['high', 'medium', 'low'],
    default: 'high'
  },
  interpretation: {
    nitrogen: { level: String, description: String },
    phosphorus: { level: String, description: String },
    potassium: { level: String, description: String },
    ph: { level: String, description: String },
    organicMatter: { level: String, description: String },
    moisture: { level: String, description: String }
  },
  recommendations: [{
    category: String,
    description: String,
    priority: String,
    implementation: String
  }],
  suitableCrops: [{
    name: String,
    suitability: String,
    reason: String,
    yield: String
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  verifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  verificationDate: Date,
  notes: String,
  isActive: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Indexes for efficient querying
soilDataSchema.index({ 'location.coordinates': '2dsphere' });
soilDataSchema.index({ 'location.state': 1, 'location.district': 1 });
soilDataSchema.index({ createdAt: -1 });
soilDataSchema.index({ dataSource: 1 });
soilDataSchema.index({ createdBy: 1 });

// Pre-save middleware to update updatedAt
soilDataSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

module.exports = mongoose.model('SoilData', soilDataSchema);

