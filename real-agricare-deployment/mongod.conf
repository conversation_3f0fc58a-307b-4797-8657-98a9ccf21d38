# MongoDB configuration file for Azure App Service
# Optimized for persistent storage and Azure environment

# Where and how to store data - using Azure mounted storage
storage:
  dbPath: /home/<USER>/data/mongodb/data
  journal:
    enabled: true
  wiredTiger:
    engineConfig:
      cacheSizeGB: 0.5
    collectionConfig:
      blockCompressor: snappy

# where to write logging data - using Azure mounted storage
systemLog:
  destination: file
  logAppend: true
  path: /home/<USER>/data/mongodb/logs/mongod.log

# network interfaces
net:
  port: 27017
  bindIp: 127.0.0.1

# process management for Azure App Service
processManagement:
  fork: false
  pidFilePath: /home/<USER>/data/mongodb/mongod.pid

# security configuration
security:
  authorization: disabled

# performance profiling
operationProfiling:
  slowOpThresholdMs: 100
  mode: slowOp
