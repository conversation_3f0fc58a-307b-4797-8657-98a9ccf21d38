# MongoDB Backup Directory

This directory is used to store MongoDB backups for the AgriCare application.

## Backup Structure

When creating a backup, the structure should be:

```
mongodb_backup.tar.gz
└── exports/
    └── agricare/
        ├── users.bson
        ├── users.metadata.json
        ├── farms.bson
        ├── farms.metadata.json
        └── ... (other collections)
```

## Creating a Backup

To create a backup of your MongoDB database:

1. Use the `mongodump` command:
   ```
   mongodump --uri="YOUR_MONGODB_URI" --out="exports"
   ```

2. Compress the exports directory:
   ```
   tar -czvf mongodb_backup.tar.gz exports/
   ```

3. Place the `mongodb_backup.tar.gz` file in this directory.

## Restoring from Backup

The Azure pipeline will automatically restore the MongoDB backup to the Azure CosmosDB instance during deployment.

If you need to manually restore the backup:

1. Extract the backup:
   ```
   tar -xzvf mongodb_backup.tar.gz
   ```

2. Use the `mongorestore` command:
   ```
   mongorestore --uri="YOUR_MONGODB_URI" --drop exports/agricare
   ```

## Important Notes

- For Azure CosmosDB with MongoDB API, use the following connection string parameters:
  ```
  ?retryWrites=false&w=majority
  ```

- CosmosDB has different indexing requirements than standard MongoDB. Some indexes might need to be created separately.

- Always test the backup and restoration process in a non-production environment first.
