const express = require('express');
const router = express.Router();
const { AgriExpert, AgriInstitution, ResearchInstitute } = require('../models/AgriExpert');
const { protect } = require('../middleware/auth.middleware');
const roleCheck = require('../middleware/roleCheck');

// Middleware to ensure only TM can access these routes
const tmOnly = roleCheck(['TM', 'admin']);

// Test endpoint
router.get('/test', (req, res) => {
  res.json({ success: true, message: 'AgriExpert API is working!' });
});

// @desc    Get all experts
// @route   GET /api/agri-expert/experts
// @access  Private (TM only)
router.get('/experts', protect, tmOnly, async (req, res) => {
  try {
    const { page = 1, limit = 10, search, state, district, specialization, status } = req.query;
    
    // Build query
    let query = {};
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { specialization: { $regex: search, $options: 'i' } },
        { designation: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (state) query.state = state;
    if (district) query.district = district;
    if (specialization) query.specialization = { $regex: specialization, $options: 'i' };
    if (status) query.status = status;
    
    // Execute query with pagination
    const experts = await AgriExpert.find(query)
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);
    
    const total = await AgriExpert.countDocuments(query);
    
    res.json({
      success: true,
      data: experts,
      pagination: {
        current: page,
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (error) {
    console.error('Error fetching experts:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch experts',
      error: error.message
    });
  }
});

// @desc    Get single expert
// @route   GET /api/agri-expert/experts/:id
// @access  Private (TM only)
router.get('/experts/:id', protect, tmOnly, async (req, res) => {
  try {
    const expert = await AgriExpert.findById(req.params.id)
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email');
    
    if (!expert) {
      return res.status(404).json({
        success: false,
        message: 'Expert not found'
      });
    }
    
    res.json({
      success: true,
      data: expert
    });
  } catch (error) {
    console.error('Error fetching expert:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch expert',
      error: error.message
    });
  }
});

// @desc    Create new expert
// @route   POST /api/agri-expert/experts
// @access  Private (TM only)
router.post('/experts', protect, tmOnly, async (req, res) => {
  try {
    const expertData = {
      ...req.body,
      createdBy: req.user._id
    };
    
    const expert = await AgriExpert.create(expertData);
    
    res.status(201).json({
      success: true,
      message: 'Expert created successfully',
      data: expert
    });
  } catch (error) {
    console.error('Error creating expert:', error);
    
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Expert with this email already exists'
      });
    }
    
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: messages
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Failed to create expert',
      error: error.message
    });
  }
});

// @desc    Update expert
// @route   PUT /api/agri-expert/experts/:id
// @access  Private (TM only)
router.put('/experts/:id', protect, tmOnly, async (req, res) => {
  try {
    const updateData = {
      ...req.body,
      updatedBy: req.user._id
    };
    
    const expert = await AgriExpert.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    );
    
    if (!expert) {
      return res.status(404).json({
        success: false,
        message: 'Expert not found'
      });
    }
    
    res.json({
      success: true,
      message: 'Expert updated successfully',
      data: expert
    });
  } catch (error) {
    console.error('Error updating expert:', error);
    
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Expert with this email already exists'
      });
    }
    
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: messages
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Failed to update expert',
      error: error.message
    });
  }
});

// @desc    Delete expert
// @route   DELETE /api/agri-expert/experts/:id
// @access  Private (TM only)
router.delete('/experts/:id', protect, tmOnly, async (req, res) => {
  try {
    const expert = await AgriExpert.findById(req.params.id);
    
    if (!expert) {
      return res.status(404).json({
        success: false,
        message: 'Expert not found'
      });
    }
    
    await AgriExpert.findByIdAndDelete(req.params.id);
    
    res.json({
      success: true,
      message: 'Expert deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting expert:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete expert',
      error: error.message
    });
  }
});

// @desc    Get all institutions
// @route   GET /api/agri-expert/institutions
// @access  Private (TM only)
router.get('/institutions', protect, tmOnly, async (req, res) => {
  try {
    const { page = 1, limit = 10, search, state, type, status } = req.query;
    
    // Build query
    let query = {};
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { type: { $regex: search, $options: 'i' } },
        { location: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (state) query.state = state;
    if (type) query.type = type;
    if (status) query.status = status;
    
    // Execute query with pagination
    const institutions = await AgriInstitution.find(query)
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);
    
    const total = await AgriInstitution.countDocuments(query);
    
    res.json({
      success: true,
      data: institutions,
      pagination: {
        current: page,
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (error) {
    console.error('Error fetching institutions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch institutions',
      error: error.message
    });
  }
});

// @desc    Create new institution
// @route   POST /api/agri-expert/institutions
// @access  Private (TM only)
router.post('/institutions', protect, tmOnly, async (req, res) => {
  try {
    const institutionData = {
      ...req.body,
      createdBy: req.user._id
    };
    
    const institution = await AgriInstitution.create(institutionData);
    
    res.status(201).json({
      success: true,
      message: 'Institution created successfully',
      data: institution
    });
  } catch (error) {
    console.error('Error creating institution:', error);
    
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: messages
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Failed to create institution',
      error: error.message
    });
  }
});

// @desc    Update institution
// @route   PUT /api/agri-expert/institutions/:id
// @access  Private (TM only)
router.put('/institutions/:id', protect, tmOnly, async (req, res) => {
  try {
    const updateData = {
      ...req.body,
      updatedBy: req.user._id
    };
    
    const institution = await AgriInstitution.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    );
    
    if (!institution) {
      return res.status(404).json({
        success: false,
        message: 'Institution not found'
      });
    }
    
    res.json({
      success: true,
      message: 'Institution updated successfully',
      data: institution
    });
  } catch (error) {
    console.error('Error updating institution:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update institution',
      error: error.message
    });
  }
});

// @desc    Delete institution
// @route   DELETE /api/agri-expert/institutions/:id
// @access  Private (TM only)
router.delete('/institutions/:id', protect, tmOnly, async (req, res) => {
  try {
    const institution = await AgriInstitution.findById(req.params.id);
    
    if (!institution) {
      return res.status(404).json({
        success: false,
        message: 'Institution not found'
      });
    }
    
    await AgriInstitution.findByIdAndDelete(req.params.id);
    
    res.json({
      success: true,
      message: 'Institution deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting institution:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete institution',
      error: error.message
    });
  }
});

// @desc    Get all research institutes
// @route   GET /api/agri-expert/research-institutes
// @access  Private (TM only)
router.get('/research-institutes', protect, tmOnly, async (req, res) => {
  try {
    const { page = 1, limit = 10, search, state, focus, status } = req.query;

    // Build query
    let query = {};

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { location: { $regex: search, $options: 'i' } },
        { focus: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    if (state) query.state = state;
    if (focus) query.focus = { $in: [new RegExp(focus, 'i')] };
    if (status) query.status = status;

    // Execute query with pagination
    const institutes = await ResearchInstitute.find(query)
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await ResearchInstitute.countDocuments(query);

    res.json({
      success: true,
      data: institutes,
      pagination: {
        current: page,
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (error) {
    console.error('Error fetching research institutes:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch research institutes',
      error: error.message
    });
  }
});

// @desc    Create new research institute
// @route   POST /api/agri-expert/research-institutes
// @access  Private (TM only)
router.post('/research-institutes', protect, tmOnly, async (req, res) => {
  try {
    const instituteData = {
      ...req.body,
      createdBy: req.user._id
    };

    const institute = await ResearchInstitute.create(instituteData);

    res.status(201).json({
      success: true,
      message: 'Research institute created successfully',
      data: institute
    });
  } catch (error) {
    console.error('Error creating research institute:', error);

    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: messages
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create research institute',
      error: error.message
    });
  }
});

// @desc    Update research institute
// @route   PUT /api/agri-expert/research-institutes/:id
// @access  Private (TM only)
router.put('/research-institutes/:id', protect, tmOnly, async (req, res) => {
  try {
    const updateData = {
      ...req.body,
      updatedBy: req.user._id
    };

    const institute = await ResearchInstitute.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    );

    if (!institute) {
      return res.status(404).json({
        success: false,
        message: 'Research institute not found'
      });
    }

    res.json({
      success: true,
      message: 'Research institute updated successfully',
      data: institute
    });
  } catch (error) {
    console.error('Error updating research institute:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update research institute',
      error: error.message
    });
  }
});

// @desc    Delete research institute
// @route   DELETE /api/agri-expert/research-institutes/:id
// @access  Private (TM only)
router.delete('/research-institutes/:id', protect, tmOnly, async (req, res) => {
  try {
    const institute = await ResearchInstitute.findById(req.params.id);

    if (!institute) {
      return res.status(404).json({
        success: false,
        message: 'Research institute not found'
      });
    }

    await ResearchInstitute.findByIdAndDelete(req.params.id);

    res.json({
      success: true,
      message: 'Research institute deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting research institute:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete research institute',
      error: error.message
    });
  }
});

// @desc    Get statistics for dashboard
// @route   GET /api/agri-expert/stats
// @access  Private (TM only)
router.get('/stats', protect, tmOnly, async (req, res) => {
  try {
    const [expertsCount, institutionsCount, researchInstitutesCount] = await Promise.all([
      AgriExpert.countDocuments({ status: 'Active' }),
      AgriInstitution.countDocuments({ status: 'Active' }),
      ResearchInstitute.countDocuments({ status: 'Active' })
    ]);

    // Get state-wise distribution
    const expertsByState = await AgriExpert.aggregate([
      { $match: { status: 'Active' } },
      { $group: { _id: '$state', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    // Get specialization distribution
    const expertsBySpecialization = await AgriExpert.aggregate([
      { $match: { status: 'Active' } },
      { $group: { _id: '$specialization', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    res.json({
      success: true,
      data: {
        counts: {
          experts: expertsCount,
          institutions: institutionsCount,
          researchInstitutes: researchInstitutesCount,
          total: expertsCount + institutionsCount + researchInstitutesCount
        },
        distributions: {
          expertsByState,
          expertsBySpecialization
        }
      }
    });
  } catch (error) {
    console.error('Error fetching stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch statistics',
      error: error.message
    });
  }
});

module.exports = router;
