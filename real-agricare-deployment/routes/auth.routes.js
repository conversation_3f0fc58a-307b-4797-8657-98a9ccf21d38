const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/auth.middleware');
const { sendOTP, verifyOTP, updateProfile, getProfile } = require('../controllers/auth.controller');
const { verifyIdToken } = require('../controllers/firebase-auth.controller');

// Public routes
router.post('/send-otp', sendOTP);
router.post('/verify-otp', verifyOTP);
router.post('/firebase-auth', verifyIdToken);

// Protected routes
router.get('/profile', protect, getProfile);
router.put('/profile', protect, updateProfile);

module.exports = router;