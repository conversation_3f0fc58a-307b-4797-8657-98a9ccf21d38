const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');

// Crop-specific thresholds and recommendations
const cropGuidelines = {
    rice: {
        tempRange: { min: 20, max: 35 },
        rainRequired: true,
        recommendations: {
            highTemp: 'Maintain standing water in fields. Consider additional irrigation.',
            lowTemp: 'Monitor for cold stress. Ensure proper drainage.',
            rain: 'Ensure proper drainage. Check for pest infestation.',
            drought: 'Increase irrigation frequency. Consider alternate wetting and drying.'
        }
    },
    wheat: {
        tempRange: { min: 15, max: 30 },
        rainRequired: false,
        recommendations: {
            highTemp: 'Provide light irrigation. Monitor for heat stress.',
            lowTemp: 'Protect from frost. Delay irrigation if frost is expected.',
            rain: 'Check for disease development. Ensure proper drainage.',
            drought: 'Schedule regular irrigation. Apply mulch if possible.'
        }
    },
    cotton: {
        tempRange: { min: 18, max: 35 },
        rainRequired: false,
        recommendations: {
            highTemp: 'Increase irrigation frequency. Monitor for pest attacks.',
            lowTemp: 'Protect young plants. Delay fertilizer application.',
            rain: 'Watch for boll rot. Ensure field drainage.',
            drought: 'Regular irrigation needed. Consider growth regulators.'
        }
    },
    sugarcane: {
        tempRange: { min: 20, max: 35 },
        rainRequired: true,
        recommendations: {
            highTemp: 'Maintain soil moisture. Consider trash mulching.',
            lowTemp: 'Protect ratoon crop. Delay irrigation in cold weather.',
            rain: 'Ensure proper drainage. Watch for disease outbreak.',
            drought: 'Regular irrigation needed. Consider skip-furrow irrigation.'
        }
    }
};

// Generate advisory based on weather conditions and crop types
const generateAdvisory = (crops, weather) => {
    const advisories = [];
    const { temperature, rainfall, forecast } = weather;

    crops.forEach(crop => {
        const guidelines = cropGuidelines[crop.toLowerCase()];
        if (!guidelines) return;

        const { tempRange, recommendations } = guidelines;

        // Temperature-based recommendations
        if (temperature > tempRange.max) {
            advisories.push(`${crop} (High Temperature): ${recommendations.highTemp}`);
        } else if (temperature < tempRange.min) {
            advisories.push(`${crop} (Low Temperature): ${recommendations.lowTemp}`);
        }

        // Rainfall-based recommendations
        if (rainfall > 0 || forecast.includes('rain')) {
            advisories.push(`${crop} (Rainfall Expected): ${recommendations.rain}`);
        } else if (forecast.includes('clear')) {
            advisories.push(`${crop} (Dry Weather): ${recommendations.drought}`);
        }
    });

    return advisories.join('\n');
};

// Get crop advisory
router.post('/', auth, async (req, res) => {
    try {
        const { crops, weather } = req.body;
        
        if (!crops || !weather || !Array.isArray(crops)) {
            return res.status(400).json({
                success: false,
                error: 'Invalid input data'
            });
        }

        const advisory = generateAdvisory(crops, weather);
        
        res.json({
            success: true,
            advisory: advisory || 'No specific advisory available for the given crops and conditions.'
        });
    } catch (error) {
        console.error('Error generating crop advisory:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to generate crop advisory'
        });
    }
});

module.exports = router; 