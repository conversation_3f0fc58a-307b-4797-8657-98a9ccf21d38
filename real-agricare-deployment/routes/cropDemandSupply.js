const express = require('express');
const router = express.Router();
const axios = require('axios');

// Cache for storing crop demand-supply data
const cropDsCache = {
  data: null,
  timestamp: 0,
  expirationTime: 30 * 60 * 1000 // 30 minutes
};

// Get crop demand and supply data
router.get('/', async (req, res) => {
  try {
    const { crop, state } = req.query;
    const forceRefresh = req.query.forceRefresh === 'true';
    
    // Check if we have cached data that's still valid
    if (!forceRefresh && cropDsCache.data && (Date.now() - cropDsCache.timestamp < cropDsCache.expirationTime)) {
      console.log('Using cached crop demand-supply data');
      
      // Filter cached data based on crop and state if provided
      let filteredData = [...cropDsCache.data];
      
      if (crop) {
        filteredData = filteredData.filter(item => item.crop.toLowerCase() === crop.toLowerCase());
      }
      
      if (state) {
        filteredData = filteredData.filter(item => item.state.toLowerCase() === state.toLowerCase());
      }
      
      return res.status(200).json({
        success: true,
        data: filteredData
      });
    }
    
    if (forceRefresh) {
      console.log('Force refreshing crop demand-supply data');
    }
    
    console.log('Fetching crop demand-supply data');
    
    // Generate mock data for crop demand and supply
    const crops = [
      'Rice', 'Wheat', 'Maize', 'Jowar', 'Bajra', 'Ragi', 
      'Pulses', 'Gram', 'Tur', 'Moong', 'Urad', 
      'Sugarcane', 'Cotton', 'Jute', 'Groundnut', 'Soybean', 
      'Sunflower', 'Mustard', 'Coconut', 'Tea', 'Coffee', 
      'Rubber', 'Potato', 'Onion', 'Tomato', 'Ajwain'
    ];
    
    const states = [
      'Andhra Pradesh', 'Arunachal Pradesh', 'Assam', 'Bihar', 
      'Chhattisgarh', 'Goa', 'Gujarat', 'Haryana', 'Himachal Pradesh', 
      'Jharkhand', 'Karnataka', 'Kerala', 'Madhya Pradesh', 'Maharashtra', 
      'Manipur', 'Meghalaya', 'Mizoram', 'Nagaland', 'Odisha', 'Punjab', 
      'Rajasthan', 'Sikkim', 'Tamil Nadu', 'Telangana', 'Tripura', 
      'Uttar Pradesh', 'Uttarakhand', 'West Bengal'
    ];
    
    // Price map for different crops (in ₹/kg)
    const cropPriceMap = {
      'Ajwain': 104.6,
      'Rice': 40,
      'Wheat': 30,
      'Maize': 22,
      'Jowar': 35,
      'Bajra': 32,
      'Ragi': 36,
      'Pulses': 105,
      'Gram': 80,
      'Tur': 95,
      'Moong': 110,
      'Urad': 105,
      'Sugarcane': 3.5,
      'Cotton': 65,
      'Jute': 45,
      'Groundnut': 90,
      'Soybean': 50,
      'Sunflower': 65,
      'Mustard': 55,
      'Coconut': 30,
      'Tea': 300,
      'Coffee': 400,
      'Rubber': 175,
      'Potato': 20,
      'Onion': 25,
      'Tomato': 35
    };
    
    // Generate data for each crop and state combination
    const data = [];
    
    for (const crop of crops) {
      for (const state of states) {
        // Generate random demand and supply values
        const demand = Math.floor(Math.random() * 100) + 1; // 1-100
        const supply = Math.floor(Math.random() * 100) + 1; // 1-100
        
        // Calculate gap (positive means demand > supply, negative means supply > demand)
        const gap = demand - supply;
        
        // Determine trend based on gap
        let trend;
        if (gap > 20) trend = 'high-demand';
        else if (gap < -20) trend = 'over-supply';
        else trend = 'balanced';
        
        // Get price from price map or generate a random price
        const price = cropPriceMap[crop] || (Math.floor(Math.random() * 50) + 20);
        
        // Add data point
        data.push({
          crop,
          state,
          demand,
          supply,
          gap,
          price,
          unit: 'kg',
          trend,
          lastUpdated: new Date().toISOString()
        });
      }
    }
    
    // Cache the data
    cropDsCache.data = data;
    cropDsCache.timestamp = Date.now();
    
    // Filter data based on crop and state if provided
    let filteredData = [...data];
    
    if (crop) {
      filteredData = filteredData.filter(item => item.crop.toLowerCase() === crop.toLowerCase());
    }
    
    if (state) {
      filteredData = filteredData.filter(item => item.state.toLowerCase() === state.toLowerCase());
    }
    
    res.status(200).json({
      success: true,
      data: filteredData
    });
  } catch (error) {
    console.error('Error getting crop demand-supply data:', error);
    
    // Try to use cached data even if it's expired
    if (cropDsCache.data) {
      console.log('Using expired cached crop demand-supply data due to error');
      
      // Filter cached data based on crop and state if provided
      let filteredData = [...cropDsCache.data];
      
      if (crop) {
        filteredData = filteredData.filter(item => item.crop.toLowerCase() === crop.toLowerCase());
      }
      
      if (state) {
        filteredData = filteredData.filter(item => item.state.toLowerCase() === state.toLowerCase());
      }
      
      return res.status(200).json({
        success: true,
        data: filteredData,
        fromCache: true
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Failed to get crop demand-supply data'
    });
  }
});

// Get top crops by demand
router.get('/top-demand', async (req, res) => {
  try {
    const { state, limit = 10 } = req.query;
    
    // Check if we have cached data
    if (!cropDsCache.data || (Date.now() - cropDsCache.timestamp > cropDsCache.expirationTime)) {
      // Trigger a refresh of the data
      await axios.get(`${req.protocol}://${req.get('host')}/api/crop-demand-supply`);
    }
    
    if (!cropDsCache.data) {
      return res.status(500).json({
        success: false,
        message: 'No crop data available'
      });
    }
    
    // Filter data based on state if provided
    let filteredData = [...cropDsCache.data];
    
    if (state) {
      filteredData = filteredData.filter(item => item.state.toLowerCase() === state.toLowerCase());
    }
    
    // Sort by demand (descending) and take the top N
    const topCrops = filteredData
      .sort((a, b) => b.demand - a.demand)
      .slice(0, parseInt(limit));
    
    res.status(200).json({
      success: true,
      data: topCrops
    });
  } catch (error) {
    console.error('Error getting top crops by demand:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get top crops by demand'
    });
  }
});

// Get crops with highest supply-demand gap
router.get('/highest-gap', async (req, res) => {
  try {
    const { state, limit = 10 } = req.query;
    
    // Check if we have cached data
    if (!cropDsCache.data || (Date.now() - cropDsCache.timestamp > cropDsCache.expirationTime)) {
      // Trigger a refresh of the data
      await axios.get(`${req.protocol}://${req.get('host')}/api/crop-demand-supply`);
    }
    
    if (!cropDsCache.data) {
      return res.status(500).json({
        success: false,
        message: 'No crop data available'
      });
    }
    
    // Filter data based on state if provided
    let filteredData = [...cropDsCache.data];
    
    if (state) {
      filteredData = filteredData.filter(item => item.state.toLowerCase() === state.toLowerCase());
    }
    
    // Sort by absolute gap (descending) and take the top N
    const gapCrops = filteredData
      .sort((a, b) => Math.abs(b.gap) - Math.abs(a.gap))
      .slice(0, parseInt(limit));
    
    res.status(200).json({
      success: true,
      data: gapCrops
    });
  } catch (error) {
    console.error('Error getting crops with highest gap:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get crops with highest gap'
    });
  }
});

module.exports = router;
