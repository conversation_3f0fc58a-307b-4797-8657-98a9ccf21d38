const express = require('express');
const router = express.Router();
const Event = require('../models/Event');
const auth = require('../middleware/auth');

// Get all events for the territory manager
router.get('/', auth, async (req, res) => {
    try {
        const events = await Event.find({ territoryManager: req.user._id })
            .populate('attendees', 'name location')
            .sort({ date: 1, time: 1 });
        res.json({ success: true, data: events });
    } catch (error) {
        console.error('Error fetching events:', error);
        res.status(500).json({ success: false, error: 'Failed to fetch events' });
    }
});

// Create a new event
router.post('/', auth, async (req, res) => {
    try {
        const event = new Event({
            ...req.body,
            territoryManager: req.user._id
        });
        await event.save();
        res.status(201).json({ success: true, data: event });
    } catch (error) {
        console.error('Error creating event:', error);
        res.status(400).json({ success: false, error: 'Failed to create event' });
    }
});

// Update an event
router.put('/:id', auth, async (req, res) => {
    try {
        const event = await Event.findOneAndUpdate(
            { _id: req.params.id, territoryManager: req.user._id },
            { ...req.body, updatedAt: Date.now() },
            { new: true }
        );
        if (!event) {
            return res.status(404).json({ success: false, error: 'Event not found' });
        }
        res.json({ success: true, data: event });
    } catch (error) {
        console.error('Error updating event:', error);
        res.status(400).json({ success: false, error: 'Failed to update event' });
    }
});

// Delete an event
router.delete('/:id', auth, async (req, res) => {
    try {
        const event = await Event.findOneAndDelete({
            _id: req.params.id,
            territoryManager: req.user._id
        });
        if (!event) {
            return res.status(404).json({ success: false, error: 'Event not found' });
        }
        res.json({ success: true, data: event });
    } catch (error) {
        console.error('Error deleting event:', error);
        res.status(500).json({ success: false, error: 'Failed to delete event' });
    }
});

// Update event status
router.patch('/:id/status', auth, async (req, res) => {
    try {
        const { status } = req.body;
        if (!['scheduled', 'completed', 'cancelled'].includes(status)) {
            return res.status(400).json({ success: false, error: 'Invalid status' });
        }

        const event = await Event.findOneAndUpdate(
            { _id: req.params.id, territoryManager: req.user._id },
            { status, updatedAt: Date.now() },
            { new: true }
        );
        if (!event) {
            return res.status(404).json({ success: false, error: 'Event not found' });
        }
        res.json({ success: true, data: event });
    } catch (error) {
        console.error('Error updating event status:', error);
        res.status(400).json({ success: false, error: 'Failed to update event status' });
    }
});

// Add attendees to an event
router.post('/:id/attendees', auth, async (req, res) => {
    try {
        const { attendees } = req.body;
        const event = await Event.findOneAndUpdate(
            { _id: req.params.id, territoryManager: req.user._id },
            { $addToSet: { attendees: { $each: attendees } }, updatedAt: Date.now() },
            { new: true }
        ).populate('attendees', 'name location');
        
        if (!event) {
            return res.status(404).json({ success: false, error: 'Event not found' });
        }
        res.json({ success: true, data: event });
    } catch (error) {
        console.error('Error adding attendees:', error);
        res.status(400).json({ success: false, error: 'Failed to add attendees' });
    }
});

module.exports = router; 