const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const Farm = require('../models/Farm');
const farmController = require('../controllers/farmController');

// Get all farms
router.get('/farms', auth, async (req, res) => {
  try {
    const farms = await Farm.find({});
    res.json(farms);
  } catch (error) {
    console.error('Error fetching all farms:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch farms'
    });
  }
});

// Get all farms for a farmer
router.get('/farmers/:farmerId/farms', auth, async (req, res) => {
  try {
    const farms = await farmController.getFarmerFarms(req.params.farmerId);
    res.json(farms);
  } catch (error) {
    console.error('Error fetching farms:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch farms'
    });
  }
});

// Get a single farm
router.get('/farms/:farmId', auth, async (req, res) => {
  try {
    const farm = await farmController.getFarmById(req.params.farmId);
    if (!farm) {
      return res.status(404).json({
        success: false,
        message: 'Farm not found'
      });
    }
    res.json(farm);
  } catch (error) {
    console.error('Error fetching farm:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch farm details'
    });
  }
});

// Create a new farm
router.post('/farms', auth, async (req, res) => {
  try {
    const farm = await farmController.createFarm(req.body);
    res.status(201).json(farm);
  } catch (error) {
    console.error('Error creating farm:', error);
    res.status(400).json({
      success: false,
      message: error.message || 'Failed to create farm'
    });
  }
});

// Update a farm
router.put('/farms/:farmId', auth, async (req, res) => {
  try {
    const farm = await farmController.updateFarm(req.params.farmId, req.body);
    if (!farm) {
      return res.status(404).json({
        success: false,
        message: 'Farm not found'
      });
    }
    res.json(farm);
  } catch (error) {
    console.error('Error updating farm:', error);
    res.status(400).json({
      success: false,
      message: error.message || 'Failed to update farm'
    });
  }
});

// Delete a farm
router.delete('/farms/:farmId', auth, async (req, res) => {
  try {
    const farm = await farmController.deleteFarm(req.params.farmId);
    if (!farm) {
      return res.status(404).json({
        success: false,
        message: 'Farm not found'
      });
    }
    res.json({
      success: true,
      message: 'Farm deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting farm:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete farm'
    });
  }
});

// Get farm statistics
router.get('/farmers/:farmerId/farm-stats', auth, async (req, res) => {
  try {
    const stats = await farmController.getFarmStats(req.params.farmerId);
    res.json(stats);
  } catch (error) {
    console.error('Error fetching farm statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch farm statistics'
    });
  }
});

// Search farms
router.get('/farms/search', auth, async (req, res) => {
  try {
    const farms = await farmController.searchFarms(req.query);
    res.json(farms);
  } catch (error) {
    console.error('Error searching farms:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to search farms'
    });
  }
});

// Get detailed farm data
router.get('/farms/:farmId/details', auth, async (req, res) => {
  try {
    const farmDetails = await farmController.getFarmDetails(req.params.farmId);
    res.json({
      success: true,
      data: farmDetails
    });
  } catch (error) {
    console.error('Error fetching farm details:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch farm details'
    });
  }
});

// Add sensor data to farm
router.post('/farms/:farmId/data', auth, async (req, res) => {
  try {
    const farmData = await farmController.addFarmData(req.params.farmId, req.body);
    res.status(201).json({
      success: true,
      data: farmData
    });
  } catch (error) {
    console.error('Error adding farm data:', error);
    res.status(400).json({
      success: false,
      message: error.message || 'Failed to add farm data'
    });
  }
});

// Add task to farm
router.post('/farms/:farmId/tasks', auth, async (req, res) => {
  try {
    const task = await farmController.addFarmTask(req.params.farmId, req.body);
    res.status(201).json({
      success: true,
      data: task
    });
  } catch (error) {
    console.error('Error adding task:', error);
    res.status(400).json({
      success: false,
      message: error.message || 'Failed to add task'
    });
  }
});

// Add alert to farm
router.post('/farms/:farmId/alerts', auth, async (req, res) => {
  try {
    const alert = await farmController.addFarmAlert(req.params.farmId, req.body);
    res.status(201).json({
      success: true,
      data: alert
    });
  } catch (error) {
    console.error('Error adding alert:', error);
    res.status(400).json({
      success: false,
      message: error.message || 'Failed to add alert'
    });
  }
});

// Subscribe to services
router.post('/farms/:farmId/services/subscribe', auth, async (req, res) => {
  try {
    const { services } = req.body; // Array of service names to subscribe
    const farm = await Farm.findById(req.params.farmId);
    
    if (!farm) {
      return res.status(404).json({
        success: false,
        message: 'Farm not found'
      });
    }

    // Update service subscriptions
    services.forEach(serviceName => {
      if (farm.services[serviceName]) {
        farm.services[serviceName].isSubscribed = true;
        farm.services[serviceName].isActive = false; // TM needs to activate
      }
    });

    await farm.save();
    res.json({
      success: true,
      data: farm.services
    });
  } catch (error) {
    console.error('Error subscribing to services:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to subscribe to services'
    });
  }
});

// Activate/deactivate services
router.post('/farms/:farmId/services/activate', auth, async (req, res) => {
  try {
    const { services, activate } = req.body; // services: array of service names, activate: boolean
    const farm = await Farm.findById(req.params.farmId);
    
    if (!farm) {
      return res.status(404).json({
        success: false,
        message: 'Farm not found'
      });
    }

    // Update service activation status
    services.forEach(serviceName => {
      if (farm.services[serviceName] && farm.services[serviceName].isSubscribed) {
        farm.services[serviceName].isActive = activate;
        if (activate) {
          farm.services[serviceName].activatedAt = new Date();
        }
      }
    });

    await farm.save();
    res.json({
      success: true,
      data: farm.services
    });
  } catch (error) {
    console.error('Error updating service activation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update service activation'
    });
  }
});

// Get service status and history
router.get('/farms/:farmId/services', auth, async (req, res) => {
  try {
    const farm = await Farm.findById(req.params.farmId);
    
    if (!farm) {
      return res.status(404).json({
        success: false,
        message: 'Farm not found'
      });
    }

    res.json({
      success: true,
      data: farm.services
    });
  } catch (error) {
    console.error('Error fetching service status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch service status'
    });
  }
});

module.exports = router; 