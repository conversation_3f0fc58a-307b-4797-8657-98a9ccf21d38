const express = require("express");
const router = express.Router();
const { 
    analyzeLivestock, 
    getHistoricalData,
    upload 
} = require("../controllers/livestockController");
const auth = require("../middleware/auth");
const Livestock = require("../models/Livestock");

// Apply auth middleware to all routes
router.use(auth);

// Analyze livestock image (with file upload)
router.post("/analyze", upload.single('image'), analyzeLivestock);

// Get historical livestock data
router.get("/history", getHistoricalData);

// Get livestock statistics
router.get("/stats", async (req, res) => {
    try {
        const stats = await Livestock.aggregate([
            {
                $match: {
                    farmerId: req.user.id // Assuming user ID is available in req.user
                }
            },
            {
                $group: {
                    _id: "$livestockType",
                    count: { $sum: 1 },
                    averageWeight: { $avg: "$weight" },
                    averageAge: { $avg: "$age" },
                    healthStatus: {
                        $push: "$healthStatus"
                    }
                }
            }
        ]);

        res.json({
            success: true,
            message: "Statistics retrieved successfully",
            stats
        });
    } catch (error) {
        console.error("Error in getStats:", error);
        res.status(500).json({
            success: false,
            message: "Error fetching statistics",
            error: error.message
        });
    }
});

// Get livestock by ID
router.get("/:id", async (req, res) => {
    try {
        const livestock = await Livestock.findOne({
            _id: req.params.id,
            farmerId: req.user.id
        });

        if (!livestock) {
            return res.status(404).json({
                success: false,
                message: "Livestock not found"
            });
        }

        res.json({
            success: true,
            message: "Livestock retrieved successfully",
            livestock
        });
    } catch (error) {
        console.error("Error in getLivestockById:", error);
        res.status(500).json({
            success: false,
            message: "Error fetching livestock",
            error: error.message
        });
    }
});

// Update livestock
router.put("/:id", async (req, res) => {
    try {
        const livestock = await Livestock.findOneAndUpdate(
            {
                _id: req.params.id,
                farmerId: req.user.id
            },
            req.body,
            { new: true, runValidators: true }
        );

        if (!livestock) {
            return res.status(404).json({
                success: false,
                message: "Livestock not found"
            });
        }

        res.json({
            success: true,
            message: "Livestock updated successfully",
            livestock
        });
    } catch (error) {
        console.error("Error in updateLivestock:", error);
        res.status(500).json({
            success: false,
            message: "Error updating livestock",
            error: error.message
        });
    }
});

// Delete livestock
router.delete("/:id", async (req, res) => {
    try {
        const livestock = await Livestock.findOneAndDelete({
            _id: req.params.id,
            farmerId: req.user.id
        });

        if (!livestock) {
            return res.status(404).json({
                success: false,
                message: "Livestock not found"
            });
        }

        res.json({
            success: true,
            message: "Livestock deleted successfully"
        });
    } catch (error) {
        console.error("Error in deleteLivestock:", error);
        res.status(500).json({
            success: false,
            message: "Error deleting livestock",
            error: error.message
        });
    }
});

module.exports = router;

