/**
 * Location Data Routes
 * Provides endpoints for accessing location-based data
 */

const express = require('express');
const router = express.Router();

/**
 * @route GET /api/location/districts
 * @desc Get list of available districts
 * @access Public
 */
router.get('/districts', (req, res) => {
  // List of districts for which we have data, organized by state
  const districtsByState = {
    'Andhra Pradesh': ['Visakhapatnam', 'Vijayawada', 'Guntur', 'Nellore', 'Kurnool'],
    'Arunachal Pradesh': ['Itanagar', 'Tawang', 'Ziro', 'Pasighat', 'Bomdila'],
    'Assam': ['Guwahati', 'Silchar', 'Dibrugarh', 'Jorhat', 'Nagaon'],
    'Bihar': ['Patna', 'Gaya', 'Muzaffarpur', 'Bhagalpur', 'Darbhanga'],
    'Chhattisgarh': ['Raipur', 'Bhilai', 'Bilaspur', 'Korba', 'Durg'],
    'Goa': ['<PERSON><PERSON><PERSON>', '<PERSON>ga<PERSON>', '<PERSON><PERSON> Gama', 'Ponda', 'Mapusa'],
    'Gujarat': ['Ahmedabad', 'Surat', 'Vadodara', 'Rajkot', 'Bhavnagar'],
    'Haryana': ['Gurugram', 'Faridabad', 'Hisar', 'Panipat', 'Ambala'],
    'Himachal Pradesh': ['Shimla', 'Mandi', 'Dharamshala', 'Solan', 'Kullu'],
    'Jharkhand': ['Ranchi', 'Jamshedpur', 'Dhanbad', 'Bokaro', 'Hazaribagh'],
    'Karnataka': ['Bangalore Urban', 'Mysore', 'Hubli-Dharwad', 'Mangalore', 'Belgaum'],
    'Kerala': ['Thiruvananthapuram', 'Kochi', 'Kozhikode', 'Thrissur', 'Kollam'],
    'Madhya Pradesh': ['Bhopal', 'Indore', 'Jabalpur', 'Gwalior', 'Ujjain', 'Sagar', 'Dewas', 'Satna', 'Ratlam', 'Rewa'],
    'Maharashtra': ['Mumbai', 'Pune', 'Nagpur', 'Thane', 'Nashik'],
    'Manipur': ['Imphal', 'Thoubal', 'Bishnupur', 'Churachandpur', 'Ukhrul'],
    'Meghalaya': ['Shillong', 'Tura', 'Jowai', 'Nongpoh', 'Williamnagar'],
    'Mizoram': ['Aizawl', 'Lunglei', 'Champhai', 'Kolasib', 'Serchhip'],
    'Nagaland': ['Kohima', 'Dimapur', 'Mokokchung', 'Tuensang', 'Wokha'],
    'Odisha': ['Bhubaneswar', 'Cuttack', 'Rourkela', 'Berhampur', 'Sambalpur'],
    'Punjab': ['Ludhiana', 'Amritsar', 'Jalandhar', 'Patiala', 'Bathinda'],
    'Rajasthan': ['Jaipur', 'Jodhpur', 'Udaipur', 'Kota', 'Ajmer'],
    'Sikkim': ['Gangtok', 'Namchi', 'Mangan', 'Gyalshing', 'Rangpo'],
    'Tamil Nadu': ['Chennai', 'Coimbatore', 'Madurai', 'Tiruchirappalli', 'Salem'],
    'Telangana': ['Hyderabad', 'Warangal', 'Nizamabad', 'Karimnagar', 'Khammam'],
    'Tripura': ['Agartala', 'Udaipur', 'Dharmanagar', 'Kailashahar', 'Belonia'],
    'Uttar Pradesh': ['Lucknow', 'Kanpur', 'Agra', 'Varanasi', 'Meerut'],
    'Uttarakhand': ['Dehradun', 'Haridwar', 'Roorkee', 'Haldwani', 'Rudrapur'],
    'West Bengal': ['Kolkata', 'Howrah', 'Durgapur', 'Asansol', 'Siliguri']
  };

  // Get the state parameter from the query
  const { state } = req.query;

  // If state is provided, return districts for that state
  if (state && districtsByState[state]) {
    return res.status(200).json({
      success: true,
      state,
      districts: districtsByState[state]
    });
  }

  // If no state is provided or state is not found, return all states and districts
  const states = Object.keys(districtsByState);
  const districts = Object.values(districtsByState).flat();

  res.status(200).json({
    success: true,
    states,
    districts
  });
});

/**
 * @route GET /api/location/blocks
 * @desc Get blocks within a district
 * @access Public
 */
router.get('/blocks', (req, res) => {
  const { district } = req.query;

  if (!district) {
    return res.status(400).json({
      success: false,
      message: 'District parameter is required'
    });
  }

  // Generate mock blocks for any district
  let mockBlocks = {};

  // Predefined blocks for known districts
  const knownBlocks = {
    'Bhopal': ['Berasia', 'Huzur', 'Phanda', 'Kolar', 'Misrod'],
    'Indore': ['Mhow', 'Depalpur', 'Sanwer', 'Hatod', 'Rau'],
    'Ludhiana': ['Ludhiana East', 'Ludhiana West', 'Jagraon', 'Khanna', 'Samrala'],
    'Bangalore Urban': ['Anekal', 'Bangalore North', 'Bangalore South', 'Bangalore East', 'Yelahanka'],
    'Chennai': ['Egmore', 'Adyar', 'Mylapore', 'T. Nagar', 'Anna Nagar']
  };

  // If we have predefined blocks for this district, use them
  if (knownBlocks[district]) {
    mockBlocks = knownBlocks;
  } else {
    // Otherwise generate generic blocks for this district
    mockBlocks[district] = [
      `${district} North`,
      `${district} South`,
      `${district} East`,
      `${district} West`,
      `${district} Central`
    ];
  }

  const blocks = mockBlocks[district] || [];

  res.status(200).json({
    success: true,
    district,
    blocks
  });
});

/**
 * @route GET /api/location/villages
 * @desc Get villages within a block
 * @access Public
 */
router.get('/villages', (req, res) => {
  const { district, block } = req.query;

  if (!district || !block) {
    return res.status(400).json({
      success: false,
      message: 'District and block parameters are required'
    });
  }

  // Generate mock villages for any block
  let mockVillages = {};

  // Predefined villages for known blocks
  const knownVillages = {
    'Berasia': ['Sukhi Sewania', 'Ratua Ratanpur', 'Jhiri', 'Kararia', 'Chanderi'],
    'Huzur': ['Bairagarh', 'Kokta', 'Misrod', 'Bhauri', 'Bawadia Kalan'],
    'Ludhiana East': ['Giaspura', 'Dhandari Kalan', 'Mundian Kalan', 'Bhamian Kalan', 'Jamalpur'],
    'Bangalore North': ['Yelahanka', 'Jakkur', 'Amruthahalli', 'Hebbal', 'Kodigehalli'],
    'Egmore': ['Chetpet', 'Kilpauk', 'Purasawalkam', 'Periamet', 'Vepery']
  };

  // If we have predefined villages for this block, use them
  if (knownVillages[block]) {
    mockVillages = knownVillages;
  } else {
    // Otherwise generate generic villages for this block
    mockVillages[block] = [
      `${block} Village 1`,
      `${block} Village 2`,
      `${block} Village 3`,
      `${block} Village 4`,
      `${block} Village 5`
    ];
  }

  const villages = mockVillages[block] || [];

  res.status(200).json({
    success: true,
    district,
    block,
    villages
  });
});

/**
 * @route GET /api/location/plots
 * @desc Get plots within a village
 * @access Public
 */
router.get('/plots', (req, res) => {
  const { district, block, village } = req.query;

  if (!district || !block || !village) {
    return res.status(400).json({
      success: false,
      message: 'District, block, and village parameters are required'
    });
  }

  // Generate mock plots for any village
  let mockPlots = {};

  // Predefined plots for known villages
  const knownPlots = {
    'Sukhi Sewania': ['Plot 101', 'Plot 102', 'Plot 103', 'Plot 104', 'Plot 105'],
    'Bairagarh': ['Plot 201', 'Plot 202', 'Plot 203', 'Plot 204', 'Plot 205'],
    'Giaspura': ['Plot 301', 'Plot 302', 'Plot 303', 'Plot 304', 'Plot 305'],
    'Yelahanka': ['Plot 401', 'Plot 402', 'Plot 403', 'Plot 404', 'Plot 405'],
    'Chetpet': ['Plot 501', 'Plot 502', 'Plot 503', 'Plot 504', 'Plot 505']
  };

  // If we have predefined plots for this village, use them
  if (knownPlots[village]) {
    mockPlots = knownPlots;
  } else {
    // Otherwise generate generic plots for this village
    const baseNumber = Math.floor(Math.random() * 900) + 100; // Random 3-digit number between 100-999
    mockPlots[village] = [
      `Plot ${baseNumber + 1}`,
      `Plot ${baseNumber + 2}`,
      `Plot ${baseNumber + 3}`,
      `Plot ${baseNumber + 4}`,
      `Plot ${baseNumber + 5}`
    ];
  }

  const plots = mockPlots[village] || [];

  res.status(200).json({
    success: true,
    district,
    block,
    village,
    plots
  });
});

/**
 * @route GET /api/location/soil-data
 * @desc Get soil data for a specific location
 * @access Public
 */
router.get('/soil-data', async (req, res) => {
  const { state, district, block, village, plot, level, latitude, longitude } = req.query;
  
  // Check if user provided coordinates
  const hasUserCoordinates = latitude && longitude;
  if (hasUserCoordinates) {
    console.log(`User provided coordinates: ${latitude}, ${longitude}`);
  }

  if (!level) {
    return res.status(400).json({
      success: false,
      message: 'Level parameter is required'
    });
  }

  // For state level, we only need the state parameter
  if (level === 'state' && !state) {
    return res.status(400).json({
      success: false,
      message: 'State parameter is required for state-level analysis'
    });
  }

  // For district level and below, we need the district parameter
  if (level !== 'state' && level !== 'coordinates' && !district) {
    return res.status(400).json({
      success: false,
      message: 'District parameter is required for district-level analysis and below'
    });
  }

  // Validate level-specific parameters
  if ((level === 'block' && !block) ||
      (level === 'village' && (!block || !village)) ||
      (level === 'plot' && (!block || !village || !plot))) {
    return res.status(400).json({
      success: false,
      message: `Parameters required for level '${level}' are missing`
    });
  }

  try {
    // Create a location object with all available location data
    const locationObj = {
      state,
      district,
      block,
      village,
      plot,
      level
    };


    // Use user-provided coordinates if available, otherwise get coordinates from location
    let coordinates;
    if (hasUserCoordinates) {
      // Convert string coordinates to numbers
      const lat = parseFloat(latitude);
      const lng = parseFloat(longitude);
      
      if (isNaN(lat) || isNaN(lng)) {
        throw new Error(`Invalid coordinates: latitude=${latitude}, longitude=${longitude}`);
      }
      
      coordinates = { lat, lng };
      console.log(`Using user-provided coordinates: ${coordinates.lat}, ${coordinates.lng}`);
    } else {
      const geocoding = require('../utils/geocoding');
      coordinates = geocoding.getCoordinates(locationObj);
      console.log(`Using geocoded coordinates for ${level}-level analysis: ${JSON.stringify(coordinates)}`);
    }
    // Try to get real data from NASA POWER API
    const satelliteService = require('../services/satelliteDataService');

    // Attempt to fetch NASA POWER data with the specific location
    const nasaData = await satelliteService.fetchNasaPowerData(coordinates, locationObj);

    // If we successfully got NASA data, use it for moisture and temperature
    if (nasaData) {
      console.log(`Successfully retrieved NASA POWER data for ${level}-level analysis`);

      // Generate NPK values based on location and NASA data
      // These values are adjusted based on the location specifics
      const npkValues = generateNPKValues(locationObj, nasaData);

      // Base values that will be adjusted based on location
      const baseValues = {
        nitrogen: npkValues.nitrogen, // ppm
        phosphorus: npkValues.phosphorus, // ppm
        potassium: npkValues.potassium, // ppm
        ph: npkValues.ph,
        organicMatter: npkValues.organicMatter, // percentage
        moisture: nasaData.soilMoisture || 22, // Use NASA data if available
        temperature: nasaData.temperature || 24, // Use NASA data if available
      };

      // Continue with the rest of the function using the NASA data
      processLocationData(req, res, baseValues, nasaData);
    } else {
      console.log(`Failed to retrieve NASA POWER data for ${level}-level analysis, using mock data`);

      // If NASA data fetch failed, fall back to mock data
      const baseValues = {
        nitrogen: 280, // ppm
        phosphorus: 45, // ppm
        potassium: 190, // ppm
        ph: 6.5,
        organicMatter: 3.2, // percentage
        moisture: 22, // percentage
        temperature: 24, // celsius
      };

      processLocationData(req, res, baseValues);
    }
  } catch (error) {
    console.error('Error fetching NASA POWER data:', error);

    // Fall back to mock data if NASA API fails
    const baseValues = {
      nitrogen: 280, // ppm
      phosphorus: 45, // ppm
      potassium: 190, // ppm
      ph: 6.5,
      organicMatter: 3.2, // percentage
      moisture: 22, // percentage
      temperature: 24, // celsius
    };

    // Create a custom error message to explain why we're using mock data
    let errorReason = 'Unknown error';
    if (error.message.includes('NASA POWER API request failed')) {
      errorReason = 'NASA POWER API request failed';
    } else if (error.message.includes('Invalid coordinates')) {
      errorReason = 'Invalid coordinates for location';
    } else if (error.message.includes('timeout')) {
      errorReason = 'API request timed out';
    } else if (error.message.includes('Network Error')) {
      errorReason = 'Network connectivity issue';
    } else if (error.response && error.response.status) {
      errorReason = `API returned status code ${error.response.status}`;
    }

    // Pass the error reason to the processLocationData function
    processLocationData(req, res, baseValues, null, errorReason);
  }
});

/**
 * Generate NPK values based on location and NASA data
 * This function creates realistic NPK values that vary by location
 * @param {Object} location - Location object with hierarchical data
 * @param {Object} nasaData - NASA POWER API data
 * @returns {Object} - NPK values and other soil properties
 */
function generateNPKValues(location, nasaData) {
  // Base values
  const baseValues = {
    nitrogen: 280, // ppm
    phosphorus: 45, // ppm
    potassium: 190, // ppm
    ph: 6.5,
    organicMatter: 3.2, // percentage
  };

  // Adjust based on NASA data if available
  if (nasaData) {
    // Soil moisture affects nitrogen availability
    if (nasaData.soilMoisture) {
      // Optimal moisture is around 25-30%
      const moistureFactor = 1 + (Math.abs(nasaData.soilMoisture - 27.5) / 100);
      baseValues.nitrogen *= moistureFactor;
    }

    // Temperature affects microbial activity and nutrient availability
    if (nasaData.temperature) {
      // Optimal temperature is around 25°C
      const tempFactor = 1 + (Math.abs(nasaData.temperature - 25) / 100);
      baseValues.phosphorus *= tempFactor;
    }

    // Precipitation affects leaching of potassium
    if (nasaData.precipitation) {
      // Higher precipitation can lead to more leaching
      const precipFactor = 1 - (nasaData.precipitation / 1000); // Slight decrease with more rain
      baseValues.potassium *= Math.max(0.8, precipFactor); // Don't reduce by more than 20%
    }
  }

  // Apply location-specific adjustments
  // These factors simulate how soil properties vary by region
  const locationFactors = {
    // State factors (base adjustments)
    'Madhya Pradesh': { nitrogen: 1.0, phosphorus: 1.0, potassium: 1.0, ph: 1.0, organicMatter: 1.0 },
    'Punjab': { nitrogen: 1.1, phosphorus: 0.9, potassium: 1.05, ph: 1.02, organicMatter: 1.05 },
    'Karnataka': { nitrogen: 0.95, phosphorus: 1.1, potassium: 1.02, ph: 0.98, organicMatter: 1.02 },
    'Tamil Nadu': { nitrogen: 0.92, phosphorus: 1.08, potassium: 1.1, ph: 0.95, organicMatter: 1.0 },

    // District factors
    'Bhopal': { nitrogen: 0.8, phosphorus: 0.9, potassium: 0.85, ph: 1.05, organicMatter: 0.8 },
    'Indore': { nitrogen: 1.1, phosphorus: 1.0, potassium: 0.9, ph: 1.0, organicMatter: 0.9 },
    'Ludhiana': { nitrogen: 1.2, phosphorus: 0.9, potassium: 1.1, ph: 1.05, organicMatter: 1.1 },
    'Bangalore Urban': { nitrogen: 0.85, phosphorus: 1.2, potassium: 1.1, ph: 0.95, organicMatter: 0.9 },
    'Chennai': { nitrogen: 0.9, phosphorus: 1.1, potassium: 1.2, ph: 0.9, organicMatter: 1.0 },

    // Block factors (additional adjustments)
    'Berasia': { nitrogen: 1.1, phosphorus: 0.9, potassium: 1.0 },
    'Huzur': { nitrogen: 0.9, phosphorus: 1.1, potassium: 1.0 },
    'Ludhiana East': { nitrogen: 1.1, phosphorus: 1.0, potassium: 1.1 },
    'Bangalore North': { nitrogen: 0.9, phosphorus: 1.2, potassium: 1.0 },
    'Egmore': { nitrogen: 1.0, phosphorus: 1.1, potassium: 1.2 },

    // Village factors (additional adjustments)
    'Sukhi Sewania': { nitrogen: 1.05, phosphorus: 0.95, potassium: 1.02 },
    'Bairagarh': { nitrogen: 0.98, phosphorus: 1.03, potassium: 0.97 },
    'Giaspura': { nitrogen: 1.02, phosphorus: 0.98, potassium: 1.05 },
    'Yelahanka': { nitrogen: 0.97, phosphorus: 1.04, potassium: 0.99 },
    'Chetpet': { nitrogen: 1.01, phosphorus: 0.99, potassium: 1.03 },

    // Plot factors (additional adjustments)
    'Plot 101': { nitrogen: 1.02, phosphorus: 0.98, potassium: 1.01 },
    'Plot 201': { nitrogen: 0.99, phosphorus: 1.01, potassium: 0.98 },
    'Plot 301': { nitrogen: 1.03, phosphorus: 0.97, potassium: 1.02 },
    'Plot 401': { nitrogen: 0.98, phosphorus: 1.02, potassium: 0.99 },
    'Plot 501': { nitrogen: 1.01, phosphorus: 0.99, potassium: 1.03 }
  };

  // Get location factors
  const stateFactor = locationFactors[location.state] || { nitrogen: 1.0, phosphorus: 1.0, potassium: 1.0, ph: 1.0, organicMatter: 1.0 };
  const districtFactor = locationFactors[location.district] || { nitrogen: 1.0, phosphorus: 1.0, potassium: 1.0, ph: 1.0, organicMatter: 1.0 };
  const blockFactor = locationFactors[location.block] || { nitrogen: 1.0, phosphorus: 1.0, potassium: 1.0 };
  const villageFactor = locationFactors[location.village] || { nitrogen: 1.0, phosphorus: 1.0, potassium: 1.0 };
  const plotFactor = locationFactors[location.plot] || { nitrogen: 1.0, phosphorus: 1.0, potassium: 1.0 };

  // Apply all factors
  const result = {
    nitrogen: baseValues.nitrogen * stateFactor.nitrogen * districtFactor.nitrogen * blockFactor.nitrogen * villageFactor.nitrogen * plotFactor.nitrogen,
    phosphorus: baseValues.phosphorus * stateFactor.phosphorus * districtFactor.phosphorus * blockFactor.phosphorus * villageFactor.phosphorus * plotFactor.phosphorus,
    potassium: baseValues.potassium * stateFactor.potassium * districtFactor.potassium * blockFactor.potassium * villageFactor.potassium * plotFactor.potassium,
    ph: baseValues.ph * stateFactor.ph * districtFactor.ph,
    organicMatter: baseValues.organicMatter * stateFactor.organicMatter * districtFactor.organicMatter
  };

  // Add some random variation (±5%) to make it more realistic
  // But use a deterministic approach based on location name to ensure consistency
  const locationString = `${location.state}-${location.district}-${location.block}-${location.village}-${location.plot}`;
  const hash = hashString(locationString);

  // Use the hash to generate consistent random adjustments
  const randomFactor = (hash % 1000) / 10000; // Between -0.05 and 0.05

  result.nitrogen *= (1 + randomFactor);
  result.phosphorus *= (1 - randomFactor);
  result.potassium *= (1 + randomFactor * 0.5);
  result.ph *= (1 + randomFactor * 0.2);
  result.organicMatter *= (1 - randomFactor * 0.3);

  return result;
}

/**
 * Simple hash function to convert a string to a number
 * Used to generate deterministic variations for locations
 * @param {string} str - String to hash
 * @returns {number} - Hash value
 */
function hashString(str) {
  if (!str) return 0;

  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return Math.abs(hash);
}

/**
 * Process location data and send response
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Object} baseValues - Base soil values
 * @param {Object} nasaData - NASA POWER API data (optional)
 * @param {string} errorReason - Reason for using mock data (optional)
 */
function processLocationData(req, res, baseValues, nasaData = null, errorReason = null) {
  const { state, district, block, village, plot, level } = req.query;

  // Location-specific adjustments (simplified)
  const locationFactors = {
    // District factors
    'Bhopal': { nitrogen: 0.8, phosphorus: 0.9, potassium: 0.85, ph: 1.05, organicMatter: 0.8, moisture: 0.7, temperature: 1.1 },
    'Indore': { nitrogen: 1.1, phosphorus: 1.0, potassium: 0.9, ph: 1.0, organicMatter: 0.9, moisture: 0.8, temperature: 1.05 },
    'Ludhiana': { nitrogen: 1.2, phosphorus: 0.9, potassium: 1.1, ph: 1.05, organicMatter: 1.1, moisture: 0.8, temperature: 1.05 },
    'Bangalore Urban': { nitrogen: 0.85, phosphorus: 1.2, potassium: 1.1, ph: 0.95, organicMatter: 0.9, moisture: 0.8, temperature: 1.05 },
    'Chennai': { nitrogen: 0.9, phosphorus: 1.1, potassium: 1.2, ph: 0.9, organicMatter: 1.0, moisture: 1.2, temperature: 1.1 },

    // Block factors (additional adjustments)
    'Berasia': { nitrogen: 1.1, phosphorus: 0.9, potassium: 1.0 },
    'Huzur': { nitrogen: 0.9, phosphorus: 1.1, potassium: 1.0 },
    'Ludhiana East': { nitrogen: 1.1, phosphorus: 1.0, potassium: 1.1 },
    'Bangalore North': { nitrogen: 0.9, phosphorus: 1.2, potassium: 1.0 },
    'Egmore': { nitrogen: 1.0, phosphorus: 1.1, potassium: 1.2 },

    // Village factors (additional adjustments)
    'Sukhi Sewania': { nitrogen: 1.05, phosphorus: 0.95, potassium: 1.02 },
    'Bairagarh': { nitrogen: 0.98, phosphorus: 1.03, potassium: 0.97 },
    'Giaspura': { nitrogen: 1.02, phosphorus: 0.98, potassium: 1.05 },
    'Yelahanka': { nitrogen: 0.97, phosphorus: 1.04, potassium: 0.99 },
    'Chetpet': { nitrogen: 1.01, phosphorus: 0.99, potassium: 1.03 },

    // Plot factors (additional adjustments)
    'Plot 101': { nitrogen: 1.02, phosphorus: 0.98, potassium: 1.01 },
    'Plot 201': { nitrogen: 0.99, phosphorus: 1.01, potassium: 0.98 },
    'Plot 301': { nitrogen: 1.03, phosphorus: 0.97, potassium: 1.02 },
    'Plot 401': { nitrogen: 0.98, phosphorus: 1.02, potassium: 0.99 },
    'Plot 501': { nitrogen: 1.01, phosphorus: 0.99, potassium: 1.03 }
  };

  // Get location factors
  const districtFactor = locationFactors[district] || { nitrogen: 1.0, phosphorus: 1.0, potassium: 1.0, ph: 1.0, organicMatter: 1.0, moisture: 1.0, temperature: 1.0 };
  const blockFactor = block ? (locationFactors[block] || { nitrogen: 1.0, phosphorus: 1.0, potassium: 1.0 }) : { nitrogen: 1.0, phosphorus: 1.0, potassium: 1.0 };
  const villageFactor = village ? (locationFactors[village] || { nitrogen: 1.0, phosphorus: 1.0, potassium: 1.0 }) : { nitrogen: 1.0, phosphorus: 1.0, potassium: 1.0 };
  const plotFactor = plot ? (locationFactors[plot] || { nitrogen: 1.0, phosphorus: 1.0, potassium: 1.0 }) : { nitrogen: 1.0, phosphorus: 1.0, potassium: 1.0 };

  // Apply location-specific adjustments with 4 decimal precision
  const adjustedData = {
    nitrogen: Number((baseValues.nitrogen).toFixed(4)),
    phosphorus: Number((baseValues.phosphorus).toFixed(4)),
    potassium: Number((baseValues.potassium).toFixed(4)),
    ph: Number((baseValues.ph).toFixed(4)),
    organicMatter: Number((baseValues.organicMatter).toFixed(4)),
    moisture: Number((baseValues.moisture).toFixed(4)),
    temperature: Number((baseValues.temperature).toFixed(4)),
    cec: 15.2,
    sand: 42,
    silt: 38,
    clay: 20,
    bulkDensity: 1.3,
    precipitation: nasaData ? Number(nasaData.precipitation.toFixed(4)) : 85,
    humidity: nasaData ? Number(nasaData.humidity.toFixed(4)) : 65
  };

  // Generate interpretation
  const interpretation = {
    nitrogen: interpretNutrientLevel(adjustedData.nitrogen, 'nitrogen'),
    phosphorus: interpretNutrientLevel(adjustedData.phosphorus, 'phosphorus'),
    potassium: interpretNutrientLevel(adjustedData.potassium, 'potassium'),
    ph: interpretPHLevel(adjustedData.ph),
    organicMatter: interpretOrganicMatter(adjustedData.organicMatter),
    moisture: interpretMoisture(adjustedData.moisture)
  };

  // Generate recommendations
  const recommendations = [
    "Apply nitrogen-rich fertilizers to improve soil fertility",
    "Consider crop rotation with legumes to naturally fix nitrogen",
    "Maintain soil moisture through proper irrigation practices",
    "Add organic matter to improve soil structure and nutrient retention",
    "Monitor pH levels and adjust if necessary with appropriate amendments"
  ];

  // Generate suitable crops
  const suitableCrops = [
    { crop: "Wheat", suitabilityScore: 85, notes: "Excellent for this soil profile" },
    { crop: "Soybean", suitabilityScore: 78, notes: "Good nitrogen fixer" },
    { crop: "Maize", suitabilityScore: 72, notes: "Suitable with proper fertilization" },
    { crop: "Cotton", suitabilityScore: 68, notes: "Moderate suitability" },
    { crop: "Chickpea", suitabilityScore: 65, notes: "Good for crop rotation" }
  ];

  // Helper functions for interpretation
  function interpretNutrientLevel(value, nutrient) {
    if (nutrient === 'nitrogen') {
      if (value < 150) return { level: 'Very Low', description: 'Severe deficiency, immediate supplementation required' };
      if (value < 250) return { level: 'Low', description: 'Deficient, supplementation recommended' };
      if (value < 350) return { level: 'Medium', description: 'Adequate for most crops' };
      if (value < 450) return { level: 'High', description: 'Abundant, no supplementation needed' };
      return { level: 'Very High', description: 'Excessive, may cause imbalances' };
    } else if (nutrient === 'phosphorus') {
      if (value < 20) return { level: 'Very Low', description: 'Severe deficiency, immediate supplementation required' };
      if (value < 40) return { level: 'Low', description: 'Deficient, supplementation recommended' };
      if (value < 60) return { level: 'Medium', description: 'Adequate for most crops' };
      if (value < 80) return { level: 'High', description: 'Abundant, no supplementation needed' };
      return { level: 'Very High', description: 'Excessive, may cause imbalances' };
    } else if (nutrient === 'potassium') {
      if (value < 100) return { level: 'Very Low', description: 'Severe deficiency, immediate supplementation required' };
      if (value < 175) return { level: 'Low', description: 'Deficient, supplementation recommended' };
      if (value < 250) return { level: 'Medium', description: 'Adequate for most crops' };
      if (value < 325) return { level: 'High', description: 'Abundant, no supplementation needed' };
      return { level: 'Very High', description: 'Excessive, may cause imbalances' };
    }
    return { level: 'Unknown', description: 'Could not determine level' };
  }

  function interpretPHLevel(ph) {
    if (ph < 5.5) return { level: 'Acidic', description: 'Too acidic for most crops. Consider liming to raise pH.' };
    if (ph < 6.0) return { level: 'Moderately Acidic', description: 'Slightly acidic, suitable for acid-loving crops.' };
    if (ph < 7.5) return { level: 'Neutral', description: 'Ideal pH range for most crops.' };
    if (ph < 8.5) return { level: 'Moderately Alkaline', description: 'Slightly alkaline, monitor for nutrient availability.' };
    return { level: 'Alkaline', description: 'Too alkaline for most crops. Consider amendments to lower pH.' };
  }

  function interpretOrganicMatter(value) {
    if (value < 1.0) return { level: 'Very Low', description: 'Severely depleted, add organic amendments immediately.' };
    if (value < 2.0) return { level: 'Low', description: 'Insufficient, add compost or other organic matter.' };
    if (value < 4.0) return { level: 'Medium', description: 'Adequate for most crops, maintain with good practices.' };
    if (value < 6.0) return { level: 'High', description: 'Good organic matter content, excellent soil health.' };
    return { level: 'Very High', description: 'Excellent organic matter content, focus on maintenance.' };
  }

  function interpretMoisture(value) {
    if (value < 10) return { level: 'Very Dry', description: 'Immediate irrigation needed.' };
    if (value < 20) return { level: 'Dry', description: 'Irrigation recommended soon.' };
    if (value < 30) return { level: 'Moderate', description: 'Adequate moisture for most crops.' };
    if (value < 40) return { level: 'Moist', description: 'Good moisture level, monitor for changes.' };
    return { level: 'Wet', description: 'Excessive moisture, improve drainage if persistent.' };
  }

  // Determine the data source
  let source = 'Mock Data (Demonstration Only)';
  let isMockData = true;
  let dataQuality = 'low';
  let mockReason = errorReason ? `Mock data used because: ${errorReason}` : null;

  if (nasaData) {
    source = 'NASA POWER API (Real Data) & Derived NPK Values';
    isMockData = false;
    dataQuality = 'high';
    mockReason = null;
  }

  // Format coordinates for display with 6 decimal places
  const formattedCoordinates = nasaData && nasaData.coordinates ? {
    lat: Number(nasaData.coordinates.lat.toFixed(6)),
    lng: Number(nasaData.coordinates.lng.toFixed(6))
  } : null;

  // Calculate precision values based on location level
  // More specific locations have higher precision (lower uncertainty)
  const precisionFactors = {
    state: 0.15,      // 15% uncertainty at state level
    district: 0.10,   // 10% uncertainty at district level
    block: 0.07,      // 7% uncertainty at block level
    village: 0.05,    // 5% uncertainty at village level
    plot: 0.02        // 2% uncertainty at plot level
  };

  // Get the precision factor based on the most specific level available
  const precisionFactor = precisionFactors[level] || 0.10;

  // Calculate precision values (uncertainty ranges)
  const precision = {
    nitrogen: Number((adjustedData.nitrogen * precisionFactor).toFixed(4)),
    phosphorus: Number((adjustedData.phosphorus * precisionFactor).toFixed(4)),
    potassium: Number((adjustedData.potassium * precisionFactor).toFixed(4)),
    ph: Number((adjustedData.ph * (precisionFactor / 2)).toFixed(4)), // pH has lower uncertainty
    organicMatter: Number((adjustedData.organicMatter * precisionFactor).toFixed(4)),
    moisture: Number((adjustedData.moisture * (precisionFactor * 1.5)).toFixed(4)) // Moisture has higher uncertainty
  };

  res.status(200).json({
    success: true,
    location: {
      state,
      district,
      block,
      village,
      plot,
      level
    },
    coordinates: formattedCoordinates,
    data: adjustedData,
    precision: precision, // Add precision information
    interpretation,
    recommendations,
    suitableCrops,
    lastUpdated: new Date().toISOString(),
    source: source,
    dataSource: nasaData ? 'NASA POWER API' : 'Estimated data', // More specific data source
    isMockData: isMockData,
    dataQuality: dataQuality,
    mockReason: mockReason, // Add reason for using mock data
    // Include additional NASA data if available
    nasaData: nasaData ? {
      soilMoisture: Number(nasaData.soilMoisture.toFixed(4)),
      rootZoneMoisture: Number(nasaData.rootZoneMoisture.toFixed(4)),
      profileMoisture: Number(nasaData.profileMoisture.toFixed(4)),
      soilTemperature: Number(nasaData.soilTemperature.toFixed(4)),
      temperature: Number(nasaData.temperature.toFixed(4)),
      precipitation: Number(nasaData.precipitation.toFixed(4)),
      humidity: Number(nasaData.humidity.toFixed(4)),
      // Include metadata about the data source
      metadata: {
        dataSource: 'NASA POWER API',
        datasetName: 'POWER Agroclimatology',
        spatialResolution: '0.5 x 0.5 degree',
        temporalResolution: 'Daily',
        units: {
          soilMoisture: 'Percent',
          rootZoneMoisture: 'Percent',
          profileMoisture: 'Percent',
          soilTemperature: 'Celsius',
          temperature: 'Celsius',
          precipitation: 'mm/day',
          humidity: 'Percent'
        },
        coordinates: formattedCoordinates,
        locationLevel: level
      }
    } : null
  });
}

module.exports = router;
