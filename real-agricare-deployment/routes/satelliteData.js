/**
 * Satellite Data Routes
 * Provides endpoints for accessing satellite-based agricultural data
 */

const express = require('express');
const router = express.Router();

// Import the satellite data service with error handling
let satelliteDataService;
try {
  satelliteDataService = require('../services/satelliteDataService');
} catch (error) {
  console.warn('SatelliteDataService not found, using fallback');
  
  // Fallback service
  satelliteDataService = {
    getSoilNPKData: async (region, forceRefresh) => {
      return {
        region: region,
        nitrogen: 280,
        phosphorus: 45,
        potassium: 190,
        ph: 6.5,
        organicMatter: 3.2,
        moisture: 25,
        lastUpdated: new Date().toISOString(),
        dataSource: 'Fallback Mock Data'
      };
    }
  };
}

/**
 * @route GET /api/satellite/soil-npk
 * @desc Get soil NPK data for a specific region
 * @access Public
 */
router.get('/soil-npk', async (req, res) => {
  try {
    const { region = 'Madhya Pradesh', forceRefresh = false } = req.query;
    console.log(`Fetching soil NPK data for region: ${region}`);

    const soilData = await satelliteDataService.getSoilNPKData(
      region,
      forceRefresh === 'true'
    );

    res.status(200).json({
      success: true,
      region,
      data: soilData
    });
  } catch (error) {
    console.error('Error fetching soil NPK data:', error);
    
    // Return fallback data instead of error
    const fallbackData = {
      region: req.query.region || 'Madhya Pradesh',
      nitrogen: 280,
      phosphorus: 45,
      potassium: 190,
      ph: 6.5,
      organicMatter: 3.2,
      moisture: 25,
      lastUpdated: new Date().toISOString(),
      dataSource: 'Fallback Data (Service Error)'
    };

    res.status(200).json({
      success: true,
      region: req.query.region || 'Madhya Pradesh',
      data: fallbackData,
      warning: 'Using fallback data due to service error'
    });
  }
});

/**
 * @route GET /api/satellite/regions
 * @desc Get list of available regions for soil data
 * @access Public
 */
router.get('/regions', (req, res) => {
  const regions = [
    'Punjab',
    'Haryana',
    'Uttar Pradesh',
    'Tamil Nadu',
    'Karnataka',
    'Kerala',
    'Madhya Pradesh',
    'West Bengal',
    'Gujarat',
    'Maharashtra'
  ];

  res.status(200).json({
    success: true,
    regions
  });
});

module.exports = router;
