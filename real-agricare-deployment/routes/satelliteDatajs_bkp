/**
 * Satellite Data Routes
 * Provides endpoints for accessing satellite-based agricultural data
 */

const express = require('express');
const router = express.Router();
const satelliteDataService = require('../services/satelliteDataService');

/**
 * @route GET /api/satellite/soil-npk
 * @desc Get soil NPK data for a specific region
 * @access Public
 */
router.get('/soil-npk', async (req, res) => {
  try {
    const { region = 'Madhya Pradesh', forceRefresh = false } = req.query;

    console.log(`Fetching soil NPK data for region: ${region}`);

    const soilData = await satelliteDataService.getSoilNPKData(
      region,
      forceRefresh === 'true'
    );

    res.status(200).json({
      success: true,
      region,
      data: soilData
    });
  } catch (error) {
    console.error('Error fetching soil NPK data:', error);

    // Determine appropriate status code based on error type
    let statusCode = 500;
    if (error.message.includes('API rate limit')) {
      statusCode = 429; // Too Many Requests
    } else if (error.message.includes('Network connection error')) {
      statusCode = 503; // Service Unavailable
    } else if (error.message.includes('not found')) {
      statusCode = 404; // Not Found
    }

    res.status(statusCode).json({
      success: false,
      message: 'Failed to fetch soil NPK data',
      error: error.message,
      dataSource: 'real-time',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * @route GET /api/satellite/regions
 * @desc Get list of available regions for soil data
 * @access Public
 */
router.get('/regions', (req, res) => {
  // List of regions for which we have soil data
  const regions = [
    'Punjab',
    'Haryana',
    'Uttar Pradesh',
    'Tamil Nadu',
    'Karnataka',
    'Kerala',
    'Madhya Pradesh',
    'West Bengal',
    'Gujarat',
    'Maharashtra'
  ];

  res.status(200).json({
    success: true,
    regions
  });
});

module.exports = router;
