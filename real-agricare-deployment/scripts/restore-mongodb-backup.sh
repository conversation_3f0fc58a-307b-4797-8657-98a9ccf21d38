#!/bin/bash
# MongoDB Restore Script for AgriCare
# This script restores a MongoDB backup from the compressed file

# Exit on error
set -e

# Configuration
BACKUP_DIR="../mongodb_backup"
TEMP_DIR="$BACKUP_DIR/temp"
MONGODB_URI="${MONGODB_URI:-mongodb://localhost:27017/agricare}"
DATABASE_NAME="agricare"
BACKUP_FILENAME="mongodb_backup.tar.gz"
BACKUP_PATH="$BACKUP_DIR/$BACKUP_FILENAME"

# Check if backup file exists
if [ ! -f "$BACKUP_PATH" ]; then
    echo "❌ Backup file not found: $BACKUP_PATH"
    exit 1
fi

# Create temp directory if it doesn't exist
mkdir -p "$TEMP_DIR"

echo "🔄 Restoring MongoDB backup..."
echo "📂 Backup file: $BACKUP_PATH"
echo "🗄️ Database: $DATABASE_NAME"

# Check if mongorestore is installed
if ! command -v mongorestore &> /dev/null; then
    echo "❌ mongorestore command not found. Please install MongoDB Database Tools."
    exit 1
fi

# Extract the backup
echo "🔄 Extracting backup..."
tar -xzvf "$BACKUP_PATH" -C "$TEMP_DIR"

# Check if extraction was successful
if [ $? -ne 0 ]; then
    echo "❌ Extraction failed"
    exit 1
fi

# Verify the extracted files
if [ ! -d "$TEMP_DIR/exports/$DATABASE_NAME" ]; then
    echo "❌ Expected directory structure not found in backup"
    echo "Contents of extraction directory:"
    find "$TEMP_DIR" -type d | sort
    exit 1
fi

# Restore the backup
echo "🔄 Running mongorestore..."
mongorestore --uri="$MONGODB_URI" --drop "$TEMP_DIR/exports/$DATABASE_NAME"

# Check if restore was successful
if [ $? -ne 0 ]; then
    echo "❌ mongorestore failed"
    exit 1
fi

# Clean up temp directory
echo "🔄 Cleaning up temporary files..."
rm -rf "$TEMP_DIR"

echo "✅ MongoDB backup restored successfully"

# Verify the restoration
echo "🔄 Verifying restoration..."
echo "🔄 Running health check script..."
node health-check.js

echo "✅ Restore process completed"
