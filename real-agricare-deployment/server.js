require("dotenv").config(); // ✅ Load environment variables
const express = require("express");
const cors = require("cors");
const morgan = require('morgan');
const path = require('path');
const connectDB = require('./config/database');
require('./config/firebase'); // Initialize Firebase Admin
const http = require('http');
const mongoose = require('mongoose');
const IOTWebSocket = require('./websocket/iotWebSocket');
const iotController = require('./controllers/iotController');

// Import API Routes
const farmRoutes = require("./routes/farms");
const weatherRoutes = require('./routes/weather');
const hrRoutes = require('./routes/hr');
const tmRoutes = require('./routes/tm');
const farmerRoutes = require('./routes/farmers');
const livestockRoutes = require('./routes/livestockRoutes'); // Updated import path
const aiRoutes = require('./routes/ai');
const dashboardRoutes = require('./routes/dashboard');
const chatRoutes = require('./routes/chatRoutes'); // Image analysis chatbot routes
const authRoutes = require('./routes/auth.routes'); // Add auth routes import
const userRoutes = require('./routes/userRoutes'); // Add user routes import
const vetAppointmentRoutes = require('./routes/vetAppointmentRoutes'); // Add vet appointment routes
const healthRecordRoutes = require('./routes/healthRecordRoutes'); // Add health record routes
const farmFinancialRoutes = require('./routes/farmFinancialRoutes'); // Add farm financial routes
const marketRoutes = require('./routes/market'); // Add market routes for demand and supply
const computerVisionRoutes = require('./routes/computerVision'); // Add Computer Vision routes
const externalDataRoutes = require('./routes/externalData'); // Add external data routes
const cropDemandSupplyRoutes = require('./routes/cropDemandSupply'); // Add crop demand and supply routes
const realMarketDataRoutes = require('./routes/realMarketData'); // Add real market data routes
const satelliteDataRoutes = require('./routes/satelliteData'); // Add satellite data routes for soil NPK
const locationDataRoutes = require('./routes/locationData'); // Add location-based data routes
const agriExpertRoutes = require('./routes/agriExpert'); // Add AgriExpert management routes

const app = express();
const server = http.createServer(app);

// ✅ Enable CORS
const corsOptions = {
  origin: function (origin, callback) {
    if (!origin) return callback(null, true);
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:5173',
      'http://localhost:5174',
      'http://127.0.0.1:5173',
      'http://127.0.0.1:5174'
    ];
    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      console.warn(`⚠️ Origin ${origin} not allowed by CORS`);
      callback(null, true);
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
};

app.use(cors(corsOptions));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(morgan('dev'));

// Serve static files from uploads directory
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));
console.log('Serving static files from:', path.join(__dirname, 'uploads'));

// ✅ Debugging Middleware (Logs All Requests)
app.use((req, res, next) => {
  console.log(`📩 [${req.method}] ${req.url}`);
  next();
});

// ✅ Register Routes
app.use("/api/farms", farmRoutes);
app.use("/api/weather", weatherRoutes);
app.use("/api/hr", hrRoutes);
app.use("/api/tm", tmRoutes);
app.use("/api/farmers", farmerRoutes);
app.use("/api/livestock", livestockRoutes);
app.use("/api/ai", aiRoutes);
app.use("/api/dashboard", dashboardRoutes);
app.use("/api/chat", chatRoutes);
app.use("/api/auth", authRoutes); // Add auth routes registration
app.use("/api/users", userRoutes); // Add user routes registration
app.use("/api/vet-appointments", vetAppointmentRoutes); // Add vet appointment routes
app.use("/api/health-records", healthRecordRoutes); // Add health record routes
app.use("/api/financial", farmFinancialRoutes); // Add farm financial routes
app.use("/api/market", marketRoutes); // Add market routes for demand and supply
app.use("/api/computer-vision", computerVisionRoutes); // Add Computer Vision routes
app.use("/api/external", externalDataRoutes); // Add external data routes
app.use("/api/crop-demand-supply", cropDemandSupplyRoutes); // Add crop demand and supply routes
app.use("/api/real-market", realMarketDataRoutes); // Add real market data routes
app.use("/api/satellite", satelliteDataRoutes); // Add satellite data routes for soil NPK
app.use("/api/location", locationDataRoutes); // Add location-based data routes
app.use("/api/agri-expert", agriExpertRoutes); // Add AgriExpert management routes

// Market Trends API
app.get('/api/market-trends', (req, res) => {
  try {
    // Generate mock data for the last 30 days
    const data = {
      currentPrice: 150.25,
      priceChange: 2.5,
      volume: 12500,
      dates: Array.from({ length: 30 }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - i);
        return date.toISOString().split('T')[0];
      }).reverse(),
      prices: Array.from({ length: 30 }, () => Math.random() * 50 + 100),
      historicalData: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        price: (Math.random() * 50 + 100).toFixed(2),
        volume: Math.floor(Math.random() * 5000 + 10000),
        change: (Math.random() * 10 - 5).toFixed(2)
      }))
    };
    res.json(data);
  } catch (error) {
    console.error('Error fetching market trends:', error);
    res.status(500).json({ error: 'Failed to fetch market trends data' });
  }
});

// Initialize WebSocket server
const iotWebSocket = new IOTWebSocket(server);
iotController.initializeWebSocket(iotWebSocket);

// Routes
app.use('/api/iot', require('./routes/iot'));

// ✅ Connect to MongoDB
connectDB();

// ✅ Default Route
app.get("/", (req, res) => {
  res.json({ status: "ok", message: "API is running 🚀", timestamp: new Date().toISOString() });
});

// ✅ Health Check Endpoint for Azure App Service
app.get("/api/health", async (req, res) => {
  try {
    // Check MongoDB connection
    const dbStatus = mongoose.connection.readyState === 1 ? 'connected' : 'disconnected';

    // Basic system info
    const systemInfo = {
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      nodeVersion: process.version,
      environment: process.env.NODE_ENV || 'development'
    };

    // Response object
    const healthStatus = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      database: {
        status: dbStatus,
        name: mongoose.connection.name || 'unknown'
      },
      system: systemInfo
    };

    // If database is disconnected, change overall status
    if (dbStatus !== 'connected') {
      healthStatus.status = 'warning';
    }

    res.json(healthStatus);
  } catch (error) {
    console.error('Health check failed:', error);
    res.status(500).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

// ✅ Error Handling Middleware
app.use((err, req, res, next) => {
  console.error("❌ Server Error:", err);
  res.status(500).json({ error: "Internal Server Error", message: err.message });
});

// ✅ Start Server
const PORT = process.env.PORT || 8000;
server.listen(PORT, () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);
});

// Handle uncaught exceptions and rejections
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
  process.exit(1);
});

process.on('unhandledRejection', (err) => {
  console.error('Unhandled Rejection:', err);
  process.exit(1);
});

// Handle graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received. Shutting down gracefully...');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received. Shutting down gracefully...');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});


// ✅ Serve React Frontend Static Files
const path = require('path');
app.use(express.static(path.join(__dirname, 'public')));

// ✅ Serve React App for all non-API routes (SPA routing)
app.get('*', (req, res) => {
  // Don't serve React for API routes
  if (req.path.startsWith('/api/')) {
    return res.status(404).json({ error: 'API endpoint not found' });
  }
  
  // Serve React app for all other routes
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});
