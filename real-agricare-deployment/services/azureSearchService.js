// Azure Search Service
const axios = require('axios');
const config = require('../config/config');

// Azure Search configuration
const searchEndpoint = process.env.AZURE_SEARCH_ENDPOINT || config.azureSearch?.endpoint || 'https://agricare.search.windows.net';
const searchApiKey = process.env.AZURE_SEARCH_API_KEY || config.azureSearch?.apiKey || 'G9tkoQ5gY5EuHqLQ4GZCCzIWp6f0x734sNFzc7kJyrc2galHuktiJQQJ99BDAC77bzfXJ3w3AAABACOGwRYy';
const searchIndexName = process.env.AZURE_SEARCH_INDEX_NAME || config.azureSearch?.indexName || 'agricare-index';

// Initialize Azure Search client
console.log(`Azure Search Service initialized with endpoint: ${searchEndpoint}`);

/**
 * Search the Azure Search index
 * @param {string} query - Search query
 * @param {Object} options - Search options
 * @returns {Promise<Object>} - Search results
 */
const searchDocuments = async (query, options = {}) => {
  try {
    const searchUrl = `${searchEndpoint}/indexes/${searchIndexName}/docs/search?api-version=2023-07-01-Preview`;

    const searchPayload = {
      search: query,
      queryType: options.queryType || 'simple',
      searchFields: options.searchFields || ['content', 'title', 'category'],
      select: options.select || ['id', 'title', 'content', 'category', 'url', 'lastUpdated'],
      top: options.top || 10,
      skip: options.skip || 0,
      orderby: options.orderby || null,
      filter: options.filter || null,
      facets: options.facets || null,
      highlight: options.highlight || null,
      scoringProfile: options.scoringProfile || null,
      scoringParameters: options.scoringParameters || null
    };

    const response = await axios.post(searchUrl, searchPayload, {
      headers: {
        'Content-Type': 'application/json',
        'api-key': searchApiKey
      }
    });

    return response.data;
  } catch (error) {
    console.error('Error searching Azure Search:', error.message);
    throw new Error('Failed to search documents');
  }
};

/**
 * Get document by ID from Azure Search
 * @param {string} documentId - Document ID
 * @returns {Promise<Object>} - Document
 */
const getDocumentById = async (documentId) => {
  try {
    const lookupUrl = `${searchEndpoint}/indexes/${searchIndexName}/docs/${documentId}?api-version=2023-07-01-Preview`;

    const response = await axios.get(lookupUrl, {
      headers: {
        'Content-Type': 'application/json',
        'api-key': searchApiKey
      }
    });

    return response.data;
  } catch (error) {
    console.error('Error getting document from Azure Search:', error.message);
    throw new Error('Failed to get document');
  }
};

/**
 * Get suggestions from Azure Search
 * @param {string} query - Partial query for suggestions
 * @param {Object} options - Suggestion options
 * @returns {Promise<Object>} - Suggestions
 */
const getSuggestions = async (query, options = {}) => {
  try {
    const suggestUrl = `${searchEndpoint}/indexes/${searchIndexName}/docs/suggest?api-version=2023-07-01-Preview`;

    const suggestPayload = {
      search: query,
      suggesterName: options.suggesterName || 'sg',
      searchFields: options.searchFields || ['title', 'content'],
      select: options.select || ['id', 'title'],
      top: options.top || 5,
      filter: options.filter || null,
      fuzzy: options.fuzzy || true
    };

    const response = await axios.post(suggestUrl, suggestPayload, {
      headers: {
        'Content-Type': 'application/json',
        'api-key': searchApiKey
      }
    });

    return response.data;
  } catch (error) {
    console.error('Error getting suggestions from Azure Search:', error.message);
    throw new Error('Failed to get suggestions');
  }
};

/**
 * Search agricultural knowledge base
 * @param {string} query - Search query
 * @param {string} region - Optional region filter (e.g., 'South India', 'North India')
 * @returns {Promise<Array>} - Search results
 */
const searchAgricultureKnowledge = async (query, region = null) => {
  try {
    console.log(`Searching agriculture knowledge for: ${query}${region ? ` in ${region}` : ''}`);

    // Extract region information from the query if not explicitly provided
    if (!region) {
      if (query.toLowerCase().includes('south india') ||
          query.toLowerCase().includes('southern india') ||
          query.toLowerCase().includes('tamil nadu') ||
          query.toLowerCase().includes('kerala') ||
          query.toLowerCase().includes('karnataka') ||
          query.toLowerCase().includes('andhra') ||
          query.toLowerCase().includes('telangana')) {
        region = 'South India';
      } else if (query.toLowerCase().includes('north india') ||
                query.toLowerCase().includes('northern india') ||
                query.toLowerCase().includes('punjab') ||
                query.toLowerCase().includes('haryana') ||
                query.toLowerCase().includes('uttar pradesh') ||
                query.toLowerCase().includes('uttarakhand') ||
                query.toLowerCase().includes('delhi') ||
                query.toLowerCase().includes('rajasthan')) {
        region = 'North India';
      }
    }

    // Enhance query with region if available
    const enhancedQuery = region ? `${query} ${region}` : query;

    // Use the real Azure Search API with semantic search
    const searchOptions = {
      queryType: 'semantic',
      searchFields: ['content', 'title', 'category', 'description', 'state', 'region'],
      select: ['id', 'title', 'content', 'category', 'description', 'source', 'lastUpdated', 'state', 'region'],
      top: 15, // Increased to get more results
      highlight: 'content,title',
      semanticConfiguration: 'default'
    };

    // Try Azure Search first
    let searchResults;
    try {
      searchResults = await searchDocuments(enhancedQuery, searchOptions);
    } catch (searchError) {
      console.error('Azure Search error:', searchError.message);
      searchResults = null;
    }

    // If we have results from Azure Search, process and return them
    if (searchResults && searchResults.value && searchResults.value.length > 0) {
      console.log(`Got ${searchResults.value.length} results from Azure Search`);

      // Process and return the Azure Search results
      return searchResults.value.map(result => ({
        id: result.id || `result-${Math.random().toString(36).substring(2, 9)}`,
        title: result.title || 'Agricultural Information',
        content: result.content || result.description || 'No content available',
        category: result.category || 'general',
        source: result.source || 'Quamin AI Search',
        lastUpdated: result.lastUpdated || new Date().toISOString().split('T')[0],
        confidence: result['@search.score'] ? result['@search.score'] / 10 : 0.8,
        highlights: result['@search.highlights'] || null,
        state: result.state || null,
        region: result.region || region || null
      }));
    }

    // If no results from Azure Search, try to get data from India Agriculture API
    console.log('No results from Azure Search, falling back to India Agriculture API');

    // Define states based on region
    const southIndianStates = ['Tamil Nadu', 'Kerala', 'Karnataka', 'Andhra Pradesh', 'Telangana'];
    const northIndianStates = ['Punjab', 'Haryana', 'Uttar Pradesh', 'Uttarakhand', 'Delhi', 'Rajasthan', 'Himachal Pradesh', 'Jammu and Kashmir'];

    // Make a request to the India Agriculture API
    const apiUrl = 'https://api.data.gov.in/resource/9ef84268-d588-465a-a308-a864a43d0070';
    const apiKey = process.env.INDIA_AGRI_API_KEY || config.indiaAgriApiKey || '579b464db66ec23bdd000001cdd3946e44ce4aad7209ff7b23ac571b';

    try {
      const response = await axios.get(apiUrl, {
        params: {
          'api-key': apiKey,
          format: 'json',
          limit: 100, // Increased to get more data for filtering
          offset: 0
        },
        timeout: 10000 // 10 second timeout
      });

      if (response.data && response.data.records && Array.isArray(response.data.records)) {
        console.log(`Fetched ${response.data.records.length} records from India Agriculture API`);

        // Filter records based on region if specified
        let filteredRecords = [...response.data.records];

        if (region === 'South India') {
          filteredRecords = filteredRecords.filter(record =>
            southIndianStates.includes(record.state)
          );
          console.log(`Filtered to ${filteredRecords.length} records from South India`);
        } else if (region === 'North India') {
          filteredRecords = filteredRecords.filter(record =>
            northIndianStates.includes(record.state)
          );
          console.log(`Filtered to ${filteredRecords.length} records from North India`);
        }

        // Further filter by query terms if needed
        if (query && query.trim() !== '' && query.toLowerCase() !== 'agriculture') {
          const queryTerms = query.toLowerCase().split(/\s+/).filter(term =>
            !['in', 'the', 'and', 'or', 'of', 'for', 'south', 'north', 'india', 'indian'].includes(term)
          );

          if (queryTerms.length > 0) {
            filteredRecords = filteredRecords.filter(record => {
              const recordText = `${record.state} ${record.district} ${record.market} ${record.commodity}`.toLowerCase();
              return queryTerms.some(term => recordText.includes(term));
            });
            console.log(`Further filtered to ${filteredRecords.length} records based on query terms`);
          }
        }

        // If we still have too many records, limit to the most relevant ones
        if (filteredRecords.length > 15) {
          filteredRecords = filteredRecords.slice(0, 15);
        }

        // If we have records after filtering, transform and return them
        if (filteredRecords.length > 0) {
          // Transform the API response to match our expected format
          return filteredRecords.map(record => {
            // Determine region based on state
            let recordRegion = null;
            if (southIndianStates.includes(record.state)) {
              recordRegion = 'South India';
            } else if (northIndianStates.includes(record.state)) {
              recordRegion = 'North India';
            }

            return {
              id: `agri-${record.state}-${record.district}-${record.commodity}`.replace(/\s+/g, '-').toLowerCase(),
              title: record.commodity || 'Agricultural Product',
              content: `${record.commodity} from ${record.state}, ${record.district}. Market: ${record.market}. Modal Price: ₹${record.modal_price} per quintal.`,
              category: 'market-data',
              source: 'India Agriculture Data Portal',
              lastUpdated: record.arrival_date || new Date().toISOString().split('T')[0],
              confidence: 0.95,
              price: parseFloat(record.modal_price) / 100, // Convert from quintal to kg
              state: record.state,
              district: record.district,
              market: record.market,
              region: recordRegion
            };
          });
        }
      }
    } catch (apiError) {
      console.error('Error fetching from India Agriculture API:', apiError.message);
    }

    // Try to get data from Agmarknet directly as a last resort
    try {
      console.log('Trying to fetch data from Agmarknet directly');

      // Use HTTPS instead of HTTP to avoid mixed content issues
      const agmarknetUrl = 'https://agmarknet.gov.in/PriceAndArrivals/CommodityDailyStateWise_Latest.aspx';

      const response = await axios.get(agmarknetUrl, {
        timeout: 10000
      });

      if (response.data) {
        console.log('Successfully fetched data from Agmarknet');

        // Extract relevant information from the HTML response
        // This is a simplified example - in reality, you would need to parse the HTML
        const commodities = extractCommoditiesFromHTML(response.data, region);

        if (commodities && commodities.length > 0) {
          return commodities.map(commodity => ({
            id: `agmarknet-${commodity.name}`.replace(/\s+/g, '-').toLowerCase(),
            title: commodity.name,
            content: `${commodity.name} - Price: ₹${commodity.price}/kg. ${commodity.description}`,
            category: 'market-data',
            source: 'Agmarknet',
            lastUpdated: new Date().toISOString().split('T')[0],
            confidence: 0.9,
            price: commodity.price,
            state: commodity.state,
            region: commodity.region
          }));
        }
      }
    } catch (agmarknetError) {
      console.error('Error fetching from Agmarknet:', agmarknetError.message);
    }

    // If all data sources fail, generate region-specific fallback data
    console.log('All data sources failed, generating region-specific fallback data');
    return generateRegionSpecificFallbackData(query, region);
  } catch (error) {
    console.error('Error searching agriculture knowledge:', error.message);

    // Return a fallback result with error information
    return [
      {
        id: 'error-result',
        title: 'Search Error',
        content: `We encountered an error while searching. Please try again later. Error: ${error.message}`,
        category: 'error',
        confidence: 0.5,
        isError: true
      }
    ];
  }
};

/**
 * Extract commodities from HTML response
 * @param {string} html - HTML content from Agmarknet
 * @param {string} region - Region filter
 * @returns {Array} - Extracted commodities
 */
const extractCommoditiesFromHTML = (html, region) => {
  // This is a placeholder function - in reality, you would use a library like cheerio to parse the HTML
  // For now, we'll return some sample data based on the region

  if (region === 'South India') {
    return [
      { name: 'Rice', price: 38.5, description: 'High-quality rice from Tamil Nadu', state: 'Tamil Nadu', region: 'South India' },
      { name: 'Coconut', price: 25.0, description: 'Fresh coconuts from Kerala', state: 'Kerala', region: 'South India' },
      { name: 'Coffee', price: 350.0, description: 'Premium coffee beans from Karnataka', state: 'Karnataka', region: 'South India' },
      { name: 'Turmeric', price: 120.0, description: 'Organic turmeric from Andhra Pradesh', state: 'Andhra Pradesh', region: 'South India' },
      { name: 'Red Chilli', price: 95.0, description: 'Spicy red chillies from Telangana', state: 'Telangana', region: 'South India' }
    ];
  } else if (region === 'North India') {
    return [
      { name: 'Wheat', price: 28.5, description: 'Premium wheat from Punjab', state: 'Punjab', region: 'North India' },
      { name: 'Basmati Rice', price: 85.0, description: 'Aromatic basmati rice from Haryana', state: 'Haryana', region: 'North India' },
      { name: 'Potato', price: 18.0, description: 'Fresh potatoes from Uttar Pradesh', state: 'Uttar Pradesh', region: 'North India' },
      { name: 'Apples', price: 120.0, description: 'Juicy apples from Himachal Pradesh', state: 'Himachal Pradesh', region: 'North India' },
      { name: 'Mustard', price: 45.0, description: 'Quality mustard from Rajasthan', state: 'Rajasthan', region: 'North India' }
    ];
  } else {
    return [
      { name: 'Rice', price: 35.0, description: 'Common variety rice', state: 'Various States', region: 'All India' },
      { name: 'Wheat', price: 25.0, description: 'Standard wheat variety', state: 'Various States', region: 'All India' },
      { name: 'Potato', price: 15.0, description: 'Fresh potatoes', state: 'Various States', region: 'All India' },
      { name: 'Onion', price: 30.0, description: 'Red onions', state: 'Various States', region: 'All India' },
      { name: 'Tomato', price: 25.0, description: 'Fresh tomatoes', state: 'Various States', region: 'All India' }
    ];
  }
};

/**
 * Generate region-specific fallback data
 * @param {string} query - Search query
 * @param {string} region - Region filter
 * @returns {Array} - Fallback data
 */
const generateRegionSpecificFallbackData = (query, region) => {
  if (region === 'South India') {
    return [
      {
        id: 'south-rice',
        title: 'Rice Production in South India',
        content: 'South India is known for its rice production, particularly in states like Tamil Nadu and Andhra Pradesh. The region produces varieties like Samba, Ponni, and Sona Masuri.',
        category: 'crops',
        source: 'Quamin AI Search',
        lastUpdated: new Date().toISOString().split('T')[0],
        confidence: 0.9,
        state: 'Tamil Nadu',
        region: 'South India'
      },
      {
        id: 'south-coconut',
        title: 'Coconut Farming in Kerala',
        content: 'Kerala is the largest coconut producing state in India, accounting for over 45% of the country\'s total production. The average yield is around 10,000 nuts per hectare.',
        category: 'crops',
        source: 'Quamin AI Search',
        lastUpdated: new Date().toISOString().split('T')[0],
        confidence: 0.9,
        state: 'Kerala',
        region: 'South India'
      },
      {
        id: 'south-coffee',
        title: 'Coffee Plantations in Karnataka',
        content: 'Karnataka accounts for about 70% of India\'s coffee production. The main varieties grown are Arabica and Robusta, primarily in the districts of Kodagu, Chikmagalur, and Hassan.',
        category: 'crops',
        source: 'Quamin AI Search',
        lastUpdated: new Date().toISOString().split('T')[0],
        confidence: 0.9,
        state: 'Karnataka',
        region: 'South India'
      }
    ];
  } else if (region === 'North India') {
    return [
      {
        id: 'north-wheat',
        title: 'Wheat Production in Punjab',
        content: 'Punjab is known as the "Wheat Bowl of India" and contributes significantly to the country\'s wheat production. The average yield is around 4,500 kg per hectare.',
        category: 'crops',
        source: 'Quamin AI Search',
        lastUpdated: new Date().toISOString().split('T')[0],
        confidence: 0.9,
        state: 'Punjab',
        region: 'North India'
      },
      {
        id: 'north-basmati',
        title: 'Basmati Rice from Haryana',
        content: 'Haryana is famous for its aromatic Basmati rice, particularly from districts like Karnal, Kurukshetra, and Ambala. The state contributes about 60% of India\'s Basmati exports.',
        category: 'crops',
        source: 'Quamin AI Search',
        lastUpdated: new Date().toISOString().split('T')[0],
        confidence: 0.9,
        state: 'Haryana',
        region: 'North India'
      },
      {
        id: 'north-sugarcane',
        title: 'Sugarcane Farming in Uttar Pradesh',
        content: 'Uttar Pradesh is the largest sugarcane producing state in India, accounting for about 40% of the country\'s total production. The state has over 30 sugar mills.',
        category: 'crops',
        source: 'Quamin AI Search',
        lastUpdated: new Date().toISOString().split('T')[0],
        confidence: 0.9,
        state: 'Uttar Pradesh',
        region: 'North India'
      }
    ];
  } else {
    // Generic fallback data
    return [
      {
        id: 'no-results',
        title: 'No Specific Results Found',
        content: `No specific results found for query: "${query}"${region ? ` in ${region}` : ''}. Please try a different search term or check back later.`,
        category: 'info',
        confidence: 0.5,
        source: 'Quamin AI Search',
        lastUpdated: new Date().toISOString().split('T')[0]
      }
    ];
  }
};

module.exports = {
  searchDocuments,
  getDocumentById,
  getSuggestions,
  searchAgricultureKnowledge,
  extractCommoditiesFromHTML,
  generateRegionSpecificFallbackData
};
