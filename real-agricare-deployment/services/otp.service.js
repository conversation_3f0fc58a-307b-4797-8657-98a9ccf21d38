// OTP Service
const crypto = require('crypto');

// In-memory OTP storage (replace with database in production)
const otpStore = new Map();

/**
 * Generate a random OTP
 * @param {number} length - Length of the OTP
 * @returns {string} - Generated OTP
 */
const generateOTP = (length = 6) => {
  // Generate a random 6-digit OTP
  return Math.floor(100000 + Math.random() * 900000).toString();
};

/**
 * Store OTP for a phone number
 * @param {string} phone - Phone number
 * @param {string} otp - OTP to store
 * @param {number} expiryMinutes - Expiry time in minutes
 */
const storeOTP = (phone, otp, expiryMinutes = 10) => {
  const expiryTime = new Date();
  expiryTime.setMinutes(expiryTime.getMinutes() + expiryMinutes);
  
  otpStore.set(phone, {
    otp,
    expiry: expiryTime
  });
  
  console.log(`OTP stored for ${phone}: ${otp} (expires in ${expiryMinutes} minutes)`);
  
  // Set timeout to remove OTP after expiry
  setTimeout(() => {
    if (otpStore.has(phone) && otpStore.get(phone).otp === otp) {
      otpStore.delete(phone);
      console.log(`OTP for ${phone} expired and removed from store`);
    }
  }, expiryMinutes * 60 * 1000);
  
  return true;
};

/**
 * Verify OTP for a phone number
 * @param {string} phone - Phone number
 * @param {string} otp - OTP to verify
 * @returns {boolean} - Whether OTP is valid
 */
const verifyOTP = (phone, otp) => {
  if (!otpStore.has(phone)) {
    console.log(`No OTP found for ${phone}`);
    return false;
  }
  
  const storedData = otpStore.get(phone);
  
  // Check if OTP has expired
  if (new Date() > storedData.expiry) {
    console.log(`OTP for ${phone} has expired`);
    otpStore.delete(phone);
    return false;
  }
  
  // Check if OTP matches
  const isValid = storedData.otp === otp;
  
  if (isValid) {
    console.log(`OTP verified successfully for ${phone}`);
    // Remove OTP after successful verification
    otpStore.delete(phone);
  } else {
    console.log(`Invalid OTP provided for ${phone}`);
  }
  
  return isValid;
};

/**
 * Send OTP via SMS (mock implementation)
 * @param {string} phone - Phone number
 * @param {string} otp - OTP to send
 * @returns {Promise<boolean>} - Whether OTP was sent successfully
 */
const sendOTP = async (phone, otp) => {
  // Mock implementation - in production, integrate with SMS gateway
  console.log(`[MOCK SMS] Sending OTP ${otp} to ${phone}`);
  
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  return true;
};

module.exports = {
  generateOTP,
  storeOTP,
  verifyOTP,
  sendOTP
};
