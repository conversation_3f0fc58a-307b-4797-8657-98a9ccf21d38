/**
 * Satellite Data Service
 * Provides access to satellite-based soil data including NPK values
 */

const axios = require('axios');
const config = require('../config/config');
const geocoding = require('../utils/geocoding');

// Cache for satellite data to reduce API calls
const dataCache = {
  soilData: {},
  timestamp: {},
  expirationTime: 24 * 60 * 60 * 1000 // 24 hours
};

/**
 * Get soil NPK data for a specific region
 * @param {string} region - Region name (state or district)
 * @param {boolean} forceRefresh - Whether to force refresh the data
 * @returns {Promise<Object>} - Soil data including NPK values
 */
const getSoilNPKData = async (region, forceRefresh = false) => {
  try {
    // Create a cache key based on the region
    const cacheKey = `npk-${region}`;

    // Check if we have cached data that's still valid
    if (!forceRefresh &&
        dataCache.soilData[cacheKey] &&
        (Date.now() - dataCache.timestamp[cacheKey] < dataCache.expirationTime)) {
      console.log(`Using cached soil NPK data for ${region}`);
      return dataCache.soilData[cacheKey];
    }

    console.log(`Fetching soil NPK data for ${region}`);

    // Get coordinates for the region
    const coordinates = geocoding.getCoordinates(region);
    console.log(`Coordinates for ${region}: lat=${coordinates.lat}, lng=${coordinates.lng}`);

    try {
      // Fetch real data from NASA POWER API
      const nasaData = await fetchNasaPowerData(coordinates, region);

      // Fetch soil data from SoilGrids API
      const soilGridsData = await fetchSoilGridsData(coordinates, region);

      // Combine data from different sources
      const combinedData = combineDataSources(nasaData, soilGridsData, region);

      // Add a flag to indicate if this is mock data
      combinedData.isMockData = false;

      // Cache the data
      dataCache.soilData[cacheKey] = combinedData;
      dataCache.timestamp[cacheKey] = Date.now();

      return combinedData;
    } catch (apiError) {
      console.error(`Error fetching data from APIs for ${region}:`, apiError);

      // Check if we should use mock data
      if (apiError.message.includes('SoilGrids API returned status code 500')) {
        console.log(`Using mock data for ${region} due to SoilGrids API error`);

        // Generate mock data
        const mockData = generateMockSoilData(region);

        // Add a flag to indicate this is mock data
        mockData.isMockData = true;
        mockData.source = 'Mock Data (SoilGrids API Unavailable)';

        // Cache the mock data
        dataCache.soilData[cacheKey] = mockData;
        dataCache.timestamp[cacheKey] = Date.now();

        return mockData;
      }

      // If not using mock data, throw a meaningful error
      let errorMessage = 'Unable to fetch satellite data for soil analysis.';

      if (apiError.response && apiError.response.status === 422) {
        errorMessage = 'NASA POWER API rejected the request with status 422. This may be due to invalid parameters or authentication issues.';
        console.log('NASA POWER API error details:', apiError.response.data);
      } else if (apiError.message.includes('NASA POWER')) {
        errorMessage = 'Unable to connect to NASA POWER API for weather and moisture data. Please try again later.';
      } else if (apiError.message.includes('SoilGrids')) {
        errorMessage = 'Unable to connect to SoilGrids API for soil NPK data. Please try again later.';
      } else if (apiError.response && apiError.response.status === 429) {
        errorMessage = 'API rate limit exceeded. Please try again in a few minutes.';
      } else if (apiError.code === 'ECONNREFUSED' || apiError.code === 'ENOTFOUND') {
        errorMessage = 'Network connection error. Please check your internet connection and try again.';
      }

      throw new Error(errorMessage);
    }
  } catch (error) {
    console.error(`Error in getSoilNPKData for ${region}:`, error);

    // Instead of falling back to mock data, propagate the error with a meaningful message
    if (error.message.includes('Unable to')) {
      // If it's already a meaningful error from our inner catch block, just propagate it
      throw error;
    } else {
      // Otherwise, provide a generic but informative error message
      throw new Error('Unable to process soil data. Please try again later or contact support if the issue persists.');
    }
  }
};

/**
 * Fetch data from NASA POWER API
 * @param {Object} coordinates - Coordinates {lat, lng}
 * @param {string} region - Region name
 * @returns {Promise<Object>} - NASA POWER data
 */
/**
 * Fetch data from NASA POWER API for a specific location
 * @param {Object} coordinates - Coordinates {lat, lng}
 * @param {string|Object} location - Location name or location object with hierarchical data
 * @returns {Promise<Object>} - NASA POWER data
 */
const fetchNasaPowerData = async (coordinates, location) => {
  try {
    // Use current date for more relevant data
    // Get the date for yesterday to ensure data is available
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    // Format dates as YYYYMMDD
    const endDateStr = yesterday.toISOString().split('T')[0].replace(/-/g, '');

    // Get data for the last 30 days
    const startDate = new Date(yesterday);
    startDate.setDate(startDate.getDate() - 30);
    const startDateStr = startDate.toISOString().split('T')[0].replace(/-/g, '');

    // Format location for logging
    const locationStr = typeof location === 'object'
      ? formatLocationString(location)
      : location;

    console.log(`Using date range: ${startDateStr} to ${endDateStr} for NASA POWER API`);

    // Make sure coordinates are valid numbers
    if (!coordinates || typeof coordinates.lat !== 'number' || typeof coordinates.lng !== 'number' ||
        isNaN(coordinates.lat) || isNaN(coordinates.lng)) {
      console.error(`Invalid coordinates for ${locationStr}:`, coordinates);
      throw new Error('Invalid coordinates for NASA POWER API');
    }

    // Ensure coordinates are within valid ranges
    const validLat = Math.max(-90, Math.min(90, coordinates.lat));
    const validLng = Math.max(-180, Math.min(180, coordinates.lng));

    // NASA POWER API endpoint
    const url = config.satellite.nasa.endpoint;

    // Parameters for the NASA POWER API
    const params = {
      start: startDateStr,
      end: endDateStr,
      latitude: validLat.toFixed(6),
      longitude: validLng.toFixed(6),
      parameters: config.satellite.nasa.parameters.join(','),
      community: 'AG',
      format: 'JSON',
      user: 'quaminagricare'
    };

    console.log(`Fetching NASA POWER data for ${locationStr} at coordinates: ${validLat.toFixed(6)}, ${validLng.toFixed(6)}`);
    console.log('NASA POWER API parameters:', JSON.stringify(params));

    // Make the API request with a timeout
    const response = await axios.get(url, {
      params,
      timeout: 30000 // 30 second timeout
    });

    // Check if the response has the expected structure
    if (response.data && response.data.properties && response.data.properties.parameter) {
      console.log(`Successfully fetched NASA POWER data for ${locationStr}`);

      // Process the NASA POWER API response
      const processedData = processNasaPowerData(response.data);

      // Add location metadata to the processed data
      processedData.coordinates = {
        lat: validLat,
        lng: validLng
      };
      processedData.locationInfo = typeof location === 'object' ? location : { region: location };

      return processedData;
    } else {
      console.error(`Invalid response format from NASA POWER API for ${locationStr}:`, response.data);
      throw new Error('Invalid response format from NASA POWER API');
    }
  } catch (error) {
    // Format location for logging
    const locationStr = typeof location === 'object'
      ? formatLocationString(location)
      : location;

    console.error(`Error fetching NASA POWER data for ${locationStr}:`, error);

    // Log more detailed error information
    if (error.response) {
      console.error('NASA POWER API error response:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });
    }

    // Instead of silently falling back to mock data, throw an error
    // This will allow the calling function to decide whether to use mock data
    console.log(`NASA POWER API request failed for ${locationStr}`);
    throw new Error(`NASA POWER API request failed: ${error.message}`);
  }
};

/**
 * Create mock NASA POWER API data for testing
 * @param {Object} coordinates - Coordinates {lat, lng}
 * @param {Object|string} location - Location information
 * @returns {Object} - Mock NASA POWER data
 */
const createMockNasaData = (coordinates, location) => {
  // Base values for mock data
  const mockData = {
    soilMoisture: 25 + (Math.random() * 10 - 5),
    rootZoneMoisture: 30 + (Math.random() * 10 - 5),
    profileMoisture: 35 + (Math.random() * 10 - 5),
    soilTemperature: 22 + (Math.random() * 6 - 3),
    temperature: 25 + (Math.random() * 6 - 3),
    precipitation: 5 + (Math.random() * 3),
    humidity: 65 + (Math.random() * 10 - 5),
    coordinates: coordinates,
    locationInfo: typeof location === 'object' ? location : { region: location }
  };

  return mockData;
};

/**
 * Format a location object into a readable string
 * @param {Object} location - Location object with hierarchical data
 * @returns {string} - Formatted location string
 */
const formatLocationString = (location) => {
  if (location.plot) {
    return `${location.plot}, ${location.village}, ${location.block}, ${location.district}, ${location.state}`;
  } else if (location.village) {
    return `${location.village}, ${location.block}, ${location.district}, ${location.state}`;
  } else if (location.block) {
    return `${location.block}, ${location.district}, ${location.state}`;
  } else if (location.district) {
    return `${location.district}, ${location.state}`;
  } else if (location.state) {
    return location.state;
  } else {
    return JSON.stringify(location);
  }
};

/**
 * Process NASA POWER API response
 * @param {Object} data - NASA POWER API response
 * @returns {Object} - Processed data
 */
const processNasaPowerData = (data) => {
  try {
    const parameters = data.properties.parameter;

    // Calculate averages for the period using the correct parameter names
    const soilMoisture = calculateAverage(parameters.GWETTOP) * 100; // Convert to percentage (0-100)
    const rootZoneMoisture = calculateAverage(parameters.GWETROOT) * 100; // Convert to percentage (0-100)
    const profileMoisture = calculateAverage(parameters.GWETPROF) * 100; // Convert to percentage (0-100)
    const soilTemperature = calculateAverage(parameters.TS); // Surface skin temperature
    const temperature = calculateAverage(parameters.T2M);
    const precipitation = calculateSum(parameters.PRECTOTCORR);
    const humidity = calculateAverage(parameters.RH2M);

    return {
      soilMoisture,       // Surface soil moisture (0-5cm)
      rootZoneMoisture,   // Root zone soil moisture (0-100cm)
      profileMoisture,    // Profile soil moisture
      soilTemperature,    // Surface skin temperature
      temperature,        // Air temperature at 2m
      precipitation,      // Precipitation
      humidity            // Relative humidity
    };
  } catch (error) {
    console.error('Error processing NASA POWER data:', error);
    throw error;
  }
};

/**
 * Calculate average value from a data object
 * @param {Object} dataObj - Object with date keys and numeric values
 * @returns {number} - Average value
 */
const calculateAverage = (dataObj) => {
  if (!dataObj) return null;

  const values = Object.values(dataObj).filter(val => val !== -999.0);
  if (values.length === 0) return null;

  const sum = values.reduce((acc, val) => acc + val, 0);
  return sum / values.length;
};

/**
 * Calculate sum of values from a data object
 * @param {Object} dataObj - Object with date keys and numeric values
 * @returns {number} - Sum of values
 */
const calculateSum = (dataObj) => {
  if (!dataObj) return null;

  const values = Object.values(dataObj).filter(val => val !== -999.0);
  if (values.length === 0) return null;

  return values.reduce((acc, val) => acc + val, 0);
};

/**
 * Fetch data from SoilGrids API
 * @param {Object} coordinates - Coordinates {lat, lng}
 * @param {string} region - Region name
 * @returns {Promise<Object>} - SoilGrids data
 */
const fetchSoilGridsData = async (coordinates, region) => {
  try {
    const url = config.satellite.soilgrids.endpoint;

    const params = {
      lat: coordinates.lat,
      lon: coordinates.lng,
      property: config.satellite.soilgrids.properties.join(','),
      depth: '0-30cm',
      value: 'mean'
    };

    console.log(`Fetching SoilGrids data for ${region} at coordinates: ${coordinates.lat}, ${coordinates.lng}`);

    const response = await axios.get(url, {
      params,
      timeout: 10000, // 10 second timeout
      validateStatus: function (status) {
        return status < 500; // Only reject if the status code is 500 or greater
      }
    });

    // Check if we got a successful response
    if (response.status !== 200) {
      throw new Error(`SoilGrids API returned status code ${response.status}`);
    }

    if (response.data && response.data.properties) {
      console.log(`Successfully fetched SoilGrids data for ${region}`);
      return processSoilGridsData(response.data);
    } else {
      throw new Error('Invalid response format from SoilGrids API');
    }
  } catch (error) {
    console.error(`Error fetching SoilGrids data for ${region}:`, error);

    // Generate mock data for SoilGrids when the API fails
    console.log('Generating mock SoilGrids data due to API failure');

    // Create mock soil data based on the region
    const mockSoilData = {
      ph: 6.5,
      organicMatter: 3.2,
      nitrogen: 280,
      phosphorus: 45,
      potassium: 190,
      cec: 15.2,
      sand: 42,
      silt: 38,
      clay: 20,
      bulkDensity: 1.3
    };

    // Apply region-specific adjustments
    const regionFactors = getRegionFactors(region);
    mockSoilData.nitrogen *= regionFactors.nitrogen;
    mockSoilData.phosphorus *= regionFactors.phosphorus;
    mockSoilData.potassium *= regionFactors.potassium;
    mockSoilData.ph *= regionFactors.ph;
    mockSoilData.organicMatter *= regionFactors.organicMatter;

    return mockSoilData;
  }
};

/**
 * Process SoilGrids API response
 * @param {Object} data - SoilGrids API response
 * @returns {Object} - Processed data
 */
const processSoilGridsData = (data) => {
  try {
    const properties = data.properties;

    // Extract soil properties
    // Note: SoilGrids provides values in different units than our application
    // We need to convert them to our units

    // pH is directly usable
    const ph = properties.phh2o && properties.phh2o.values ?
      properties.phh2o.values[0].value / 10 : null;

    // Soil organic carbon needs to be converted to organic matter
    // Organic matter ≈ 1.724 × Organic Carbon
    const organicCarbon = properties.soc && properties.soc.values ?
      properties.soc.values[0].value / 10 : null;
    const organicMatter = organicCarbon ? organicCarbon * 1.724 / 10 : null;

    // Nitrogen is in cg/kg, convert to ppm (mg/kg)
    // 1 cg/kg = 100 mg/kg = 100 ppm
    const nitrogen = properties.nitrogen && properties.nitrogen.values ?
      properties.nitrogen.values[0].value * 100 : null;

    // Potassium is in cmol/kg, convert to ppm
    // 1 cmol/kg of K = 391 ppm
    const potassium = properties.potassium && properties.potassium.values ?
      properties.potassium.values[0].value * 391 : null;

    // CEC (Cation Exchange Capacity) in cmol/kg
    const cec = properties.cec && properties.cec.values ?
      properties.cec.values[0].value / 10 : null;

    // Soil texture components in g/kg, convert to percentage
    const sand = properties.sand && properties.sand.values ?
      properties.sand.values[0].value / 10 : null;
    const silt = properties.silt && properties.silt.values ?
      properties.silt.values[0].value / 10 : null;
    const clay = properties.clay && properties.clay.values ?
      properties.clay.values[0].value / 10 : null;

    // Bulk density in cg/cm³, convert to g/cm³
    const bulkDensity = properties.bdod && properties.bdod.values ?
      properties.bdod.values[0].value / 100 : null;

    // Estimate phosphorus based on other soil properties
    // This is a rough estimation as SoilGrids doesn't provide phosphorus directly
    // Phosphorus often correlates with organic matter and pH
    const phosphorus = estimatePhosphorus(organicMatter, ph, cec);

    return {
      ph,
      organicMatter,
      nitrogen,
      phosphorus,
      potassium,
      cec,
      sand,
      silt,
      clay,
      bulkDensity
    };
  } catch (error) {
    console.error('Error processing SoilGrids data:', error);
    throw error;
  }
};

/**
 * Estimate phosphorus content based on other soil properties
 * This is a simplified model as we don't have direct phosphorus measurements
 * @param {number} organicMatter - Organic matter percentage
 * @param {number} ph - Soil pH
 * @param {number} cec - Cation Exchange Capacity
 * @returns {number} - Estimated phosphorus in ppm
 */
const estimatePhosphorus = (organicMatter, ph, cec) => {
  if (!organicMatter || !ph) return null;

  // Base value
  let phosphorus = 20;

  // Adjust based on organic matter (positive correlation)
  if (organicMatter > 0) {
    phosphorus += organicMatter * 5;
  }

  // Adjust based on pH (phosphorus availability peaks around pH 6.5)
  if (ph > 0) {
    const phFactor = 1 - Math.abs(ph - 6.5) * 0.2;
    phosphorus *= Math.max(0.5, phFactor);
  }

  // Adjust based on CEC (positive correlation)
  if (cec > 0) {
    phosphorus *= (1 + cec * 0.02);
  }

  return Math.round(phosphorus);
};

/**
 * Combine data from different sources
 * @param {Object} nasaData - Data from NASA POWER API
 * @param {Object} soilGridsData - Data from SoilGrids API
 * @param {string} region - Region name
 * @returns {Object} - Combined data
 */
const combineDataSources = (nasaData, soilGridsData, region) => {
  // Check if we have both NASA and SoilGrids data
  if (!nasaData && !soilGridsData) {
    throw new Error('No data available from satellite sources. Please try again later.');
  } else if (!nasaData) {
    throw new Error('Weather and moisture data unavailable from NASA POWER API. Please try again later.');
  } else if (!soilGridsData) {
    throw new Error('Soil NPK data unavailable from SoilGrids API. Please try again later.');
  }

  // Combine the data from both sources
  const combinedData = {
    region,
    data: {
      // NPK values from SoilGrids - no fallbacks to ensure we're using real data
      nitrogen: soilGridsData.nitrogen,
      phosphorus: soilGridsData.phosphorus,
      potassium: soilGridsData.potassium,

      // Soil properties from SoilGrids
      ph: soilGridsData.ph,
      organicMatter: soilGridsData.organicMatter,

      // Soil moisture from NASA POWER
      moisture: nasaData.soilMoisture,
      rootZoneMoisture: nasaData.rootZoneMoisture,
      profileMoisture: nasaData.profileMoisture,

      // Temperature from NASA POWER
      temperature: nasaData.temperature,
      soilTemperature: nasaData.soilTemperature,

      // Additional properties
      cec: soilGridsData.cec,
      sand: soilGridsData.sand,
      silt: soilGridsData.silt,
      clay: soilGridsData.clay,
      bulkDensity: soilGridsData.bulkDensity,
      precipitation: nasaData.precipitation,
      humidity: nasaData.humidity
    },
    source: 'Satellite Data (NASA POWER & SoilGrids)',
    lastUpdated: new Date().toISOString()
  };

  // Check if any essential data is missing
  const essentialProperties = ['nitrogen', 'phosphorus', 'potassium', 'ph', 'organicMatter', 'moisture'];
  const missingProperties = essentialProperties.filter(prop =>
    combinedData.data[prop] === null || combinedData.data[prop] === undefined
  );

  if (missingProperties.length > 0) {
    throw new Error(`Essential soil data missing: ${missingProperties.join(', ')}. Please try a different region or try again later.`);
  }

  // Add interpretation of values
  combinedData.interpretation = {
    nitrogen: interpretNutrientLevel(combinedData.data.nitrogen, 'nitrogen'),
    phosphorus: interpretNutrientLevel(combinedData.data.phosphorus, 'phosphorus'),
    potassium: interpretNutrientLevel(combinedData.data.potassium, 'potassium'),
    ph: interpretPHLevel(combinedData.data.ph),
    organicMatter: interpretOrganicMatter(combinedData.data.organicMatter),
    moisture: interpretMoisture(combinedData.data.moisture)
  };

  // Add recommendations based on soil data
  combinedData.recommendations = generateRecommendations(combinedData.data, region);

  // Add suitable crops based on soil data
  combinedData.suitableCrops = getSuitableCrops(combinedData.data, region);

  return combinedData;
};

/**
 * Generate mock soil data for testing
 * @param {string} region - Region name
 * @returns {Object} - Mock soil data
 */
const generateMockSoilData = (region) => {
  // Base values that will be adjusted based on region
  const baseValues = {
    nitrogen: 280, // ppm
    phosphorus: 45, // ppm
    potassium: 190, // ppm
    ph: 6.5,
    organicMatter: 3.2, // percentage
    moisture: 22, // percentage
    temperature: 24, // celsius
  };

  // Adjust values based on region characteristics
  const regionFactors = getRegionFactors(region);

  // Apply region-specific adjustments
  const adjustedData = {
    nitrogen: Math.round(baseValues.nitrogen * regionFactors.nitrogen),
    phosphorus: Math.round(baseValues.phosphorus * regionFactors.phosphorus),
    potassium: Math.round(baseValues.potassium * regionFactors.potassium),
    ph: Number((baseValues.ph * regionFactors.ph).toFixed(1)),
    organicMatter: Number((baseValues.organicMatter * regionFactors.organicMatter).toFixed(1)),
    moisture: Math.round(baseValues.moisture * regionFactors.moisture),
    temperature: Math.round(baseValues.temperature * regionFactors.temperature),
  };

  // Add interpretation of values
  const interpretation = {
    nitrogen: interpretNutrientLevel(adjustedData.nitrogen, 'nitrogen'),
    phosphorus: interpretNutrientLevel(adjustedData.phosphorus, 'phosphorus'),
    potassium: interpretNutrientLevel(adjustedData.potassium, 'potassium'),
    ph: interpretPHLevel(adjustedData.ph),
    organicMatter: interpretOrganicMatter(adjustedData.organicMatter),
    moisture: interpretMoisture(adjustedData.moisture),
  };

  // Add recommendations based on soil data
  const recommendations = generateRecommendations(adjustedData, region);

  return {
    region,
    data: adjustedData,
    interpretation,
    recommendations,
    suitableCrops: getSuitableCrops(adjustedData, region),
    lastUpdated: new Date().toISOString(),
    source: 'Satellite Soil Analysis'
  };
};

/**
 * Get region-specific adjustment factors
 * @param {string} region - Region name
 * @returns {Object} - Adjustment factors for the region
 */
const getRegionFactors = (region) => {
  // Default factors (no adjustment)
  const defaultFactors = {
    nitrogen: 1.0,
    phosphorus: 1.0,
    potassium: 1.0,
    ph: 1.0,
    organicMatter: 1.0,
    moisture: 1.0,
    temperature: 1.0
  };

  // Region-specific factors
  const regionMap = {
    // North India
    'Punjab': {
      nitrogen: 1.2,
      phosphorus: 0.9,
      potassium: 1.1,
      ph: 1.05,
      organicMatter: 1.1,
      moisture: 0.8,
      temperature: 1.05
    },
    'Haryana': {
      nitrogen: 1.1,
      phosphorus: 0.85,
      potassium: 1.0,
      ph: 1.1,
      organicMatter: 0.9,
      moisture: 0.7,
      temperature: 1.1
    },
    'Uttar Pradesh': {
      nitrogen: 1.0,
      phosphorus: 0.8,
      potassium: 0.9,
      ph: 1.0,
      organicMatter: 0.85,
      moisture: 0.9,
      temperature: 1.0
    },
    // South India
    'Tamil Nadu': {
      nitrogen: 0.9,
      phosphorus: 1.1,
      potassium: 1.2,
      ph: 0.9,
      organicMatter: 1.0,
      moisture: 1.2,
      temperature: 1.1
    },
    'Karnataka': {
      nitrogen: 0.85,
      phosphorus: 1.2,
      potassium: 1.1,
      ph: 0.95,
      organicMatter: 0.9,
      moisture: 0.8,
      temperature: 1.05
    },
    'Kerala': {
      nitrogen: 1.1,
      phosphorus: 1.3,
      potassium: 1.3,
      ph: 0.85,
      organicMatter: 1.3,
      moisture: 1.4,
      temperature: 1.0
    },
    // Central India
    'Madhya Pradesh': {
      nitrogen: 0.8,
      phosphorus: 0.9,
      potassium: 0.85,
      ph: 1.05,
      organicMatter: 0.8,
      moisture: 0.7,
      temperature: 1.1
    },
    // East India
    'West Bengal': {
      nitrogen: 1.1,
      phosphorus: 1.0,
      potassium: 1.1,
      ph: 0.9,
      organicMatter: 1.1,
      moisture: 1.3,
      temperature: 1.0
    },
    // West India
    'Gujarat': {
      nitrogen: 0.7,
      phosphorus: 0.8,
      potassium: 0.9,
      ph: 1.1,
      organicMatter: 0.7,
      moisture: 0.6,
      temperature: 1.2
    },
    'Maharashtra': {
      nitrogen: 0.75,
      phosphorus: 0.85,
      potassium: 0.9,
      ph: 1.05,
      organicMatter: 0.8,
      moisture: 0.7,
      temperature: 1.15
    }
  };

  return regionMap[region] || defaultFactors;
};

/**
 * Interpret nutrient level
 * @param {number} value - Nutrient value in ppm
 * @param {string} nutrient - Nutrient name
 * @returns {string} - Interpretation
 */
const interpretNutrientLevel = (value, nutrient) => {
  const ranges = {
    nitrogen: [
      { max: 150, level: 'Very Low', description: 'Severe deficiency, immediate supplementation required' },
      { max: 250, level: 'Low', description: 'Deficient, supplementation recommended' },
      { max: 350, level: 'Medium', description: 'Adequate for most crops' },
      { max: 450, level: 'High', description: 'Abundant, no supplementation needed' },
      { max: Infinity, level: 'Very High', description: 'Excessive, may cause imbalances' }
    ],
    phosphorus: [
      { max: 20, level: 'Very Low', description: 'Severe deficiency, immediate supplementation required' },
      { max: 40, level: 'Low', description: 'Deficient, supplementation recommended' },
      { max: 60, level: 'Medium', description: 'Adequate for most crops' },
      { max: 80, level: 'High', description: 'Abundant, no supplementation needed' },
      { max: Infinity, level: 'Very High', description: 'Excessive, may cause imbalances' }
    ],
    potassium: [
      { max: 100, level: 'Very Low', description: 'Severe deficiency, immediate supplementation required' },
      { max: 175, level: 'Low', description: 'Deficient, supplementation recommended' },
      { max: 250, level: 'Medium', description: 'Adequate for most crops' },
      { max: 325, level: 'High', description: 'Abundant, no supplementation needed' },
      { max: Infinity, level: 'Very High', description: 'Excessive, may cause imbalances' }
    ]
  };

  const range = ranges[nutrient];
  if (!range) return 'Unknown';

  for (const { max, level, description } of range) {
    if (value <= max) {
      return { level, description };
    }
  }

  return { level: 'Unknown', description: 'Could not determine level' };
};

/**
 * Interpret pH level
 * @param {number} ph - pH value
 * @returns {Object} - Interpretation
 */
const interpretPHLevel = (ph) => {
  if (ph < 5.5) {
    return {
      level: 'Acidic',
      description: 'Too acidic for most crops. Consider liming to raise pH.'
    };
  } else if (ph >= 5.5 && ph < 6.5) {
    return {
      level: 'Moderately Acidic',
      description: 'Suitable for acid-loving crops. Slight liming may benefit other crops.'
    };
  } else if (ph >= 6.5 && ph < 7.5) {
    return {
      level: 'Neutral',
      description: 'Ideal pH for most crops. Excellent nutrient availability.'
    };
  } else if (ph >= 7.5 && ph < 8.5) {
    return {
      level: 'Moderately Alkaline',
      description: 'May limit availability of some nutrients. Consider adding organic matter.'
    };
  } else {
    return {
      level: 'Alkaline',
      description: 'Too alkaline for most crops. Consider adding sulfur or acidifying amendments.'
    };
  }
};

/**
 * Interpret organic matter level
 * @param {number} value - Organic matter percentage
 * @returns {Object} - Interpretation
 */
const interpretOrganicMatter = (value) => {
  if (value < 1.5) {
    return {
      level: 'Very Low',
      description: 'Severely depleted soil. Add significant organic matter through compost, manure, or cover crops.'
    };
  } else if (value >= 1.5 && value < 2.5) {
    return {
      level: 'Low',
      description: 'Below optimal levels. Regular additions of organic matter recommended.'
    };
  } else if (value >= 2.5 && value < 4) {
    return {
      level: 'Medium',
      description: 'Adequate for most crops. Continue regular organic matter additions to maintain.'
    };
  } else if (value >= 4 && value < 6) {
    return {
      level: 'High',
      description: 'Excellent organic matter content. Maintain current practices.'
    };
  } else {
    return {
      level: 'Very High',
      description: 'Exceptional organic matter content. Ideal for most crops.'
    };
  }
};

/**
 * Interpret soil moisture level
 * @param {number} value - Moisture percentage
 * @returns {Object} - Interpretation
 */
const interpretMoisture = (value) => {
  if (value < 10) {
    return {
      level: 'Very Dry',
      description: 'Drought conditions. Immediate irrigation needed for most crops.'
    };
  } else if (value >= 10 && value < 20) {
    return {
      level: 'Dry',
      description: 'Below optimal moisture. Irrigation recommended for most crops.'
    };
  } else if (value >= 20 && value < 30) {
    return {
      level: 'Moderate',
      description: 'Adequate moisture for most crops. Monitor during dry periods.'
    };
  } else if (value >= 30 && value < 40) {
    return {
      level: 'Moist',
      description: 'Good moisture level. Ideal for most crops.'
    };
  } else {
    return {
      level: 'Wet',
      description: 'Excessive moisture. May cause root problems for some crops. Improve drainage if persistent.'
    };
  }
};

/**
 * Generate recommendations based on soil data
 * @param {Object} soilData - Soil data
 * @param {string} region - Region name
 * @returns {Array} - Recommendations
 */
const generateRecommendations = (soilData, region) => {
  const recommendations = [];

  // Nitrogen recommendations
  if (soilData.nitrogen < 150) {
    recommendations.push({
      nutrient: 'Nitrogen',
      action: 'Apply nitrogen-rich fertilizers such as urea or ammonium sulfate',
      dosage: '120-150 kg/ha',
      timing: 'Split application: 50% at planting, 50% during vegetative growth'
    });
  } else if (soilData.nitrogen < 250) {
    recommendations.push({
      nutrient: 'Nitrogen',
      action: 'Moderate nitrogen application needed',
      dosage: '80-100 kg/ha',
      timing: 'Split application recommended'
    });
  }

  // Phosphorus recommendations
  if (soilData.phosphorus < 20) {
    recommendations.push({
      nutrient: 'Phosphorus',
      action: 'Apply phosphate fertilizers such as DAP or SSP',
      dosage: '80-100 kg/ha',
      timing: 'Apply before planting'
    });
  } else if (soilData.phosphorus < 40) {
    recommendations.push({
      nutrient: 'Phosphorus',
      action: 'Moderate phosphorus application needed',
      dosage: '50-70 kg/ha',
      timing: 'Apply before planting'
    });
  }

  // Potassium recommendations
  if (soilData.potassium < 100) {
    recommendations.push({
      nutrient: 'Potassium',
      action: 'Apply potassium fertilizers such as MOP',
      dosage: '80-100 kg/ha',
      timing: 'Apply before planting'
    });
  } else if (soilData.potassium < 175) {
    recommendations.push({
      nutrient: 'Potassium',
      action: 'Moderate potassium application needed',
      dosage: '50-70 kg/ha',
      timing: 'Apply before planting'
    });
  }

  // pH recommendations
  if (soilData.ph < 5.5) {
    recommendations.push({
      nutrient: 'pH',
      action: 'Apply agricultural lime to raise pH',
      dosage: '2-3 tons/ha',
      timing: 'Apply 2-3 months before planting'
    });
  } else if (soilData.ph > 8.0) {
    recommendations.push({
      nutrient: 'pH',
      action: 'Apply gypsum or elemental sulfur to lower pH',
      dosage: '1-2 tons/ha of gypsum or 500-1000 kg/ha of sulfur',
      timing: 'Apply 2-3 months before planting'
    });
  }

  // Organic matter recommendations
  if (soilData.organicMatter < 2.0) {
    recommendations.push({
      nutrient: 'Organic Matter',
      action: 'Add compost, manure, or incorporate cover crops',
      dosage: '10-15 tons/ha of compost or manure',
      timing: 'Apply before planting and incorporate into soil'
    });
  }

  // Moisture recommendations
  if (soilData.moisture < 15) {
    recommendations.push({
      nutrient: 'Moisture',
      action: 'Implement irrigation and mulching',
      dosage: 'Maintain soil moisture at 50-75% of field capacity',
      timing: 'Regular monitoring recommended'
    });
  } else if (soilData.moisture > 35) {
    recommendations.push({
      nutrient: 'Moisture',
      action: 'Improve drainage and avoid over-irrigation',
      dosage: 'Consider raised beds or drainage channels',
      timing: 'Implement before rainy season'
    });
  }

  return recommendations;
};

/**
 * Get suitable crops based on soil data
 * @param {Object} soilData - Soil data
 * @param {string} region - Region name
 * @returns {Array} - Suitable crops
 */
const getSuitableCrops = (soilData, region) => {
  // Base suitability score for common crops
  const cropSuitability = {
    'Rice': {
      baseScore: 70,
      nitrogenFactor: 0.2,
      phosphorusFactor: 0.1,
      potassiumFactor: 0.15,
      phIdeal: 6.0,
      phTolerance: 1.5,
      moistureIdeal: 35,
      moistureTolerance: 15
    },
    'Wheat': {
      baseScore: 65,
      nitrogenFactor: 0.15,
      phosphorusFactor: 0.15,
      potassiumFactor: 0.1,
      phIdeal: 7.0,
      phTolerance: 1.0,
      moistureIdeal: 25,
      moistureTolerance: 10
    },
    'Maize': {
      baseScore: 60,
      nitrogenFactor: 0.2,
      phosphorusFactor: 0.15,
      potassiumFactor: 0.1,
      phIdeal: 6.5,
      phTolerance: 1.5,
      moistureIdeal: 30,
      moistureTolerance: 12
    },
    'Pulses': {
      baseScore: 55,
      nitrogenFactor: 0.05,
      phosphorusFactor: 0.2,
      potassiumFactor: 0.15,
      phIdeal: 6.5,
      phTolerance: 1.0,
      moistureIdeal: 25,
      moistureTolerance: 10
    },
    'Cotton': {
      baseScore: 50,
      nitrogenFactor: 0.15,
      phosphorusFactor: 0.1,
      potassiumFactor: 0.2,
      phIdeal: 6.0,
      phTolerance: 1.5,
      moistureIdeal: 25,
      moistureTolerance: 15
    },
    'Sugarcane': {
      baseScore: 60,
      nitrogenFactor: 0.2,
      phosphorusFactor: 0.15,
      potassiumFactor: 0.2,
      phIdeal: 6.5,
      phTolerance: 1.5,
      moistureIdeal: 35,
      moistureTolerance: 10
    },
    'Vegetables': {
      baseScore: 65,
      nitrogenFactor: 0.2,
      phosphorusFactor: 0.2,
      potassiumFactor: 0.15,
      phIdeal: 6.5,
      phTolerance: 1.0,
      moistureIdeal: 30,
      moistureTolerance: 10
    },
    'Fruits': {
      baseScore: 60,
      nitrogenFactor: 0.15,
      phosphorusFactor: 0.15,
      potassiumFactor: 0.2,
      phIdeal: 6.5,
      phTolerance: 1.5,
      moistureIdeal: 30,
      moistureTolerance: 15
    },
    'Oilseeds': {
      baseScore: 55,
      nitrogenFactor: 0.1,
      phosphorusFactor: 0.2,
      potassiumFactor: 0.15,
      phIdeal: 6.5,
      phTolerance: 1.0,
      moistureIdeal: 25,
      moistureTolerance: 10
    }
  };

  // Calculate suitability scores
  const scores = Object.entries(cropSuitability).map(([crop, factors]) => {
    // Base score
    let score = factors.baseScore;

    // Adjust for NPK levels
    score += (soilData.nitrogen / 300) * 100 * factors.nitrogenFactor;
    score += (soilData.phosphorus / 50) * 100 * factors.phosphorusFactor;
    score += (soilData.potassium / 200) * 100 * factors.potassiumFactor;

    // Adjust for pH (penalty for being far from ideal)
    const phDifference = Math.abs(soilData.ph - factors.phIdeal);
    if (phDifference > factors.phTolerance) {
      score -= (phDifference - factors.phTolerance) * 10;
    }

    // Adjust for moisture (penalty for being far from ideal)
    const moistureDifference = Math.abs(soilData.moisture - factors.moistureIdeal);
    if (moistureDifference > factors.moistureTolerance) {
      score -= (moistureDifference - factors.moistureTolerance) * 2;
    }

    // Region-specific adjustments
    const regionBonus = getRegionCropBonus(region, crop);
    score += regionBonus;

    return {
      crop,
      suitabilityScore: Math.max(0, Math.min(100, Math.round(score))),
      suitabilityLevel: getSuitabilityLevel(score)
    };
  });

  // Sort by suitability score (descending)
  return scores.sort((a, b) => b.suitabilityScore - a.suitabilityScore);
};

/**
 * Get region-specific crop bonus
 * @param {string} region - Region name
 * @param {string} crop - Crop name
 * @returns {number} - Bonus score
 */
const getRegionCropBonus = (region, crop) => {
  const bonusMap = {
    'Punjab': { 'Wheat': 15, 'Rice': 10, 'Cotton': 5 },
    'Haryana': { 'Wheat': 15, 'Rice': 10, 'Cotton': 5 },
    'Uttar Pradesh': { 'Wheat': 10, 'Rice': 10, 'Sugarcane': 15 },
    'Tamil Nadu': { 'Rice': 15, 'Sugarcane': 10, 'Cotton': 5 },
    'Karnataka': { 'Rice': 5, 'Pulses': 10, 'Cotton': 5, 'Fruits': 10 },
    'Kerala': { 'Rice': 15, 'Fruits': 15, 'Vegetables': 10 },
    'Madhya Pradesh': { 'Wheat': 10, 'Pulses': 15, 'Oilseeds': 10 },
    'West Bengal': { 'Rice': 20, 'Vegetables': 10, 'Pulses': 5 },
    'Gujarat': { 'Cotton': 20, 'Oilseeds': 15, 'Pulses': 5 },
    'Maharashtra': { 'Cotton': 10, 'Sugarcane': 15, 'Pulses': 10, 'Fruits': 5 }
  };

  return (bonusMap[region] && bonusMap[region][crop]) || 0;
};

/**
 * Get suitability level based on score
 * @param {number} score - Suitability score
 * @returns {string} - Suitability level
 */
const getSuitabilityLevel = (score) => {
  if (score >= 80) return 'Excellent';
  if (score >= 60) return 'Good';
  if (score >= 40) return 'Moderate';
  if (score >= 20) return 'Poor';
  return 'Unsuitable';
};

module.exports = {
  getSoilNPKData,
  fetchNasaPowerData
};
