/**
 * SMS Service
 * Handles sending SMS messages through various providers
 */
const axios = require('axios');
require('dotenv').config();

// SMS Provider Types
const SMS_PROVIDERS = {
  CONSOLE: 'console', // For development - logs to console
  MESSAGEBIRD: 'messagebird', // MessageBird SMS API
  CUSTOM: 'custom'    // Custom SMS API
};

// Get the configured SMS provider from environment variables
const SMS_PROVIDER = process.env.SMS_PROVIDER || SMS_PROVIDERS.CONSOLE;

/**
 * Send SMS using the configured provider
 * @param {string} phoneNumber - Recipient phone number
 * @param {string} message - SMS message content
 * @returns {Promise<boolean>} - Whether SMS was sent successfully
 */
const sendSMS = async (phoneNumber, message) => {
  try {
    switch (SMS_PROVIDER) {
      case SMS_PROVIDERS.CONSOLE:
        return sendConsoleMessage(phoneNumber, message);

      case SMS_PROVIDERS.MESSAGEBIRD:
        return sendMessageBirdMessage(phoneNumber, message);

      case SMS_PROVIDERS.CUSTOM:
        return sendCustomMessage(phoneNumber, message);

      default:
        console.warn(`Unknown SMS provider: ${SMS_PROVIDER}. Falling back to console.`);
        return sendConsoleMessage(phoneNumber, message);
    }
  } catch (error) {
    console.error('Error sending SMS:', error);
    return false;
  }
};

/**
 * Log SMS to console (for development)
 * @param {string} phoneNumber - Recipient phone number
 * @param {string} message - SMS message content
 * @returns {Promise<boolean>} - Always returns true
 */
const sendConsoleMessage = async (phoneNumber, message) => {
  console.log('===== DEVELOPMENT SMS =====');
  console.log(`To: ${phoneNumber}`);
  console.log(`Message: ${message}`);
  console.log('===========================');
  return true;
};

/**
 * Send SMS using MessageBird
 * @param {string} phoneNumber - Recipient phone number
 * @param {string} message - SMS message content
 * @returns {Promise<boolean>} - Whether SMS was sent successfully
 */
const sendMessageBirdMessage = async (phoneNumber, message) => {
  try {
    // Uncomment and configure when you have MessageBird credentials
    /*
    const messagebird = require('messagebird')(process.env.MESSAGEBIRD_API_KEY);

    const result = await new Promise((resolve, reject) => {
      messagebird.messages.create({
        originator: process.env.MESSAGEBIRD_ORIGINATOR,
        recipients: [phoneNumber],
        body: message
      }, (err, response) => {
        if (err) {
          reject(err);
        } else {
          resolve(response);
        }
      });
    });

    return !!result.id;
    */

    // For now, fall back to console
    return sendConsoleMessage(phoneNumber, message);
  } catch (error) {
    console.error('MessageBird SMS error:', error);
    return false;
  }
};

/**
 * Send SMS using a custom API
 * @param {string} phoneNumber - Recipient phone number
 * @param {string} message - SMS message content
 * @returns {Promise<boolean>} - Whether SMS was sent successfully
 */
const sendCustomMessage = async (phoneNumber, message) => {
  try {
    // Implement your custom SMS API integration here
    // Example:
    /*
    const response = await axios.post(process.env.CUSTOM_SMS_API_URL, {
      apiKey: process.env.CUSTOM_SMS_API_KEY,
      phone: phoneNumber,
      message: message
    });

    return response.data.success;
    */

    // For now, fall back to console
    return sendConsoleMessage(phoneNumber, message);
  } catch (error) {
    console.error('Custom SMS API error:', error);
    return false;
  }
};

module.exports = {
  sendSMS,
  SMS_PROVIDERS
};
