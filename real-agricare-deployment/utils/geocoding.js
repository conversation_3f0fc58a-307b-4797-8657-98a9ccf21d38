/**
 * Geocoding utility
 * Provides functions to convert region names to coordinates
 */

// Map of Indian states/regions to their approximate central coordinates
const regionCoordinates = {
  // North India
  'Punjab': { lat: 31.1471, lng: 75.3412 },
  'Haryana': { lat: 29.0588, lng: 76.0856 },
  'Uttar Pradesh': { lat: 26.8467, lng: 80.9462 },
  'Uttarakhand': { lat: 30.0668, lng: 79.0193 },
  'Himachal Pradesh': { lat: 31.1048, lng: 77.1734 },
  'Jammu and Kashmir': { lat: 33.7782, lng: 76.5762 },
  'Delhi': { lat: 28.7041, lng: 77.1025 },

  // Central India
  'Madhya Pradesh': { lat: 22.9734, lng: 78.6569 },
  'Chhattisgarh': { lat: 21.2787, lng: 81.8661 },

  // East India
  'Bihar': { lat: 25.0961, lng: 85.3131 },
  'Jharkhand': { lat: 23.6102, lng: 85.2799 },
  'West Bengal': { lat: 22.9868, lng: 87.8550 },
  'Odisha': { lat: 20.9517, lng: 85.0985 },

  // West India
  'Rajasthan': { lat: 27.0238, lng: 74.2179 },
  'Gujarat': { lat: 22.2587, lng: 71.1924 },
  'Maharashtra': { lat: 19.7515, lng: 75.7139 },
  'Goa': { lat: 15.2993, lng: 74.1240 },

  // South India
  'Karnataka': { lat: 15.3173, lng: 75.7139 },
  'Telangana': { lat: 18.1124, lng: 79.0193 },
  'Andhra Pradesh': { lat: 15.9129, lng: 79.7400 },
  'Tamil Nadu': { lat: 11.1271, lng: 78.6569 },
  'Kerala': { lat: 10.8505, lng: 76.2711 },

  // Northeast India
  'Assam': { lat: 26.2006, lng: 92.9376 },
  'Sikkim': { lat: 27.5330, lng: 88.5122 },
  'Meghalaya': { lat: 25.4670, lng: 91.3662 },
  'Arunachal Pradesh': { lat: 28.2180, lng: 94.7278 },
  'Nagaland': { lat: 26.1584, lng: 94.5624 },
  'Manipur': { lat: 24.6637, lng: 93.9063 },
  'Mizoram': { lat: 23.1645, lng: 92.9376 },
  'Tripura': { lat: 23.9408, lng: 91.9882 }
};

// Map of Indian districts to their coordinates
// This is a partial list of major agricultural districts
const districtCoordinates = {
  // Punjab
  'Ludhiana': { lat: 30.9010, lng: 75.8573 },
  'Amritsar': { lat: 31.6340, lng: 74.8723 },
  'Jalandhar': { lat: 31.3260, lng: 75.5762 },

  // Uttar Pradesh
  'Lucknow': { lat: 26.8467, lng: 80.9462 },
  'Kanpur': { lat: 26.4499, lng: 80.3319 },
  'Varanasi': { lat: 25.3176, lng: 82.9739 },

  // Maharashtra
  'Pune': { lat: 18.5204, lng: 73.8567 },
  'Nagpur': { lat: 21.1458, lng: 79.0882 },
  'Nashik': { lat: 19.9975, lng: 73.7898 },

  // Karnataka
  'Bangalore': { lat: 12.9716, lng: 77.5946 },
  'Bangalore Urban': { lat: 12.9716, lng: 77.5946 },
  'Mysore': { lat: 12.2958, lng: 76.6394 },
  'Hubli': { lat: 15.3647, lng: 75.1240 },

  // Tamil Nadu
  'Chennai': { lat: 13.0827, lng: 80.2707 },
  'Coimbatore': { lat: 11.0168, lng: 76.9558 },
  'Madurai': { lat: 9.9252, lng: 78.1198 },

  // Gujarat
  'Ahmedabad': { lat: 23.0225, lng: 72.5714 },
  'Surat': { lat: 21.1702, lng: 72.8311 },
  'Vadodara': { lat: 22.3072, lng: 73.1812 },

  // Madhya Pradesh
  'Bhopal': { lat: 23.2599, lng: 77.4126 },
  'Indore': { lat: 22.7196, lng: 75.8577 },
  'Jabalpur': { lat: 23.1815, lng: 79.9864 },

  // Bihar
  'Patna': { lat: 25.5941, lng: 85.1376 },
  'Gaya': { lat: 24.7914, lng: 84.9994 },

  // West Bengal
  'Kolkata': { lat: 22.5726, lng: 88.3639 },
  'Howrah': { lat: 22.5958, lng: 88.2636 },

  // Rajasthan
  'Jaipur': { lat: 26.9124, lng: 75.7873 },
  'Jodhpur': { lat: 26.2389, lng: 73.0243 },

  // Andhra Pradesh
  'Hyderabad': { lat: 17.3850, lng: 78.4867 },
  'Visakhapatnam': { lat: 17.6868, lng: 83.2185 }
};

/**
 * Get coordinates for a location at any level (state, district, block, village, plot)
 * @param {string|Object} location - Location name or location object with hierarchical data
 * @returns {Object} - Coordinates {lat, lng}
 */
const getCoordinates = (location) => {
  // If location is an object with hierarchical data
  if (typeof location === 'object') {
    console.log(`Getting coordinates for location:`, location);

    // Check if coordinates are directly provided
    if (location.coordinates &&
        typeof location.coordinates.lat === 'number' &&
        typeof location.coordinates.lng === 'number') {
      console.log(`Using provided coordinates:`, location.coordinates);
      return {
        lat: location.coordinates.lat,
        lng: location.coordinates.lng
      };
    }

    // Define specific coordinates for known plots (for testing and demonstration)
    const knownPlotCoordinates = {
      // Madhya Pradesh plots
      'Plot 101': { lat: 23.2599, lng: 77.4126 }, // Near Bhopal
      'Plot 102': { lat: 23.2650, lng: 77.4200 },
      'Plot 103': { lat: 23.2700, lng: 77.4150 },
      'Plot 104': { lat: 23.2550, lng: 77.4050 },
      'Plot 105': { lat: 23.2500, lng: 77.4100 },

      // Punjab plots
      'Plot 201': { lat: 30.9010, lng: 75.8573 }, // Near Ludhiana
      'Plot 202': { lat: 30.9050, lng: 75.8600 },
      'Plot 203': { lat: 30.9100, lng: 75.8550 },
      'Plot 204': { lat: 30.8950, lng: 75.8500 },
      'Plot 205': { lat: 30.8900, lng: 75.8450 },

      // Karnataka plots
      'Plot 301': { lat: 12.9716, lng: 77.5946 }, // Near Bangalore
      'Plot 302': { lat: 12.9750, lng: 77.6000 },
      'Plot 303': { lat: 12.9800, lng: 77.5900 },
      'Plot 304': { lat: 12.9650, lng: 77.5850 },
      'Plot 305': { lat: 12.9600, lng: 77.5800 },

      // Tamil Nadu plots
      'Plot 401': { lat: 13.0827, lng: 80.2707 }, // Near Chennai
      'Plot 402': { lat: 13.0850, lng: 80.2750 },
      'Plot 403': { lat: 13.0900, lng: 80.2650 },
      'Plot 404': { lat: 13.0750, lng: 80.2600 },
      'Plot 405': { lat: 13.0700, lng: 80.2550 }
    };

    // Try to get coordinates at the most specific level first
    if (location.plot) {
      // Check if we have known coordinates for this plot
      if (knownPlotCoordinates[location.plot]) {
        console.log(`Using known coordinates for plot ${location.plot}:`, knownPlotCoordinates[location.plot]);
        return knownPlotCoordinates[location.plot];
      }

      // If not, generate deterministic coordinates based on the hierarchy
      if (location.village && location.district) {
        // First get village coordinates
        let baseCoords;

        // Check if district is known
        const districtCoords = districtCoordinates[location.district];
        if (districtCoords) {
          // Generate village coordinates based on district
          const villageHash = hashString(location.village);
          const latOffset = (villageHash % 100) / 2000; // Smaller offset for more precision
          const lngOffset = ((villageHash / 100) % 100) / 2000;

          baseCoords = {
            lat: districtCoords.lat + latOffset - 0.025,
            lng: districtCoords.lng + lngOffset - 0.025
          };
        } else if (location.state && regionCoordinates[location.state]) {
          // If district unknown but state known, use state as base
          const stateCoords = regionCoordinates[location.state];
          const districtHash = hashString(location.district || '');
          const villageHash = hashString(location.village);

          const distLatOffset = (districtHash % 100) / 1000;
          const distLngOffset = ((districtHash / 100) % 100) / 1000;

          const villLatOffset = (villageHash % 100) / 2000;
          const villLngOffset = ((villageHash / 100) % 100) / 2000;

          baseCoords = {
            lat: stateCoords.lat + distLatOffset + villLatOffset - 0.05,
            lng: stateCoords.lng + distLngOffset + villLngOffset - 0.05
          };
        } else {
          // If neither district nor state known, use central India
          const villageHash = hashString(location.village);
          baseCoords = {
            lat: 22.9734 + (villageHash % 100) / 1000 - 0.05,
            lng: 78.6569 + ((villageHash / 100) % 100) / 1000 - 0.05
          };
        }

        // Now generate plot coordinates based on village coordinates
        const plotHash = hashString(location.plot);
        // Use very small offsets to ensure plots are close together
        const plotLatOffset = (plotHash % 100) / 10000; // ~10-100 meters
        const plotLngOffset = ((plotHash / 100) % 100) / 10000;

        const plotCoords = {
          lat: baseCoords.lat + plotLatOffset - 0.005,
          lng: baseCoords.lng + plotLngOffset - 0.005
        };

        console.log(`Generated precise plot-level coordinates for ${location.plot}:`, plotCoords);
        return plotCoords;
      }
    }

    // Define specific coordinates for known villages
    const knownVillageCoordinates = {
      'Sukhi Sewania': { lat: 23.3299, lng: 77.4926 }, // Near Bhopal
      'Bairagarh': { lat: 23.2977, lng: 77.3745 },     // Near Bhopal
      'Giaspura': { lat: 30.8815, lng: 75.9236 },      // Near Ludhiana
      'Yelahanka': { lat: 13.1005, lng: 77.5963 },     // Near Bangalore
      'Chetpet': { lat: 13.0723, lng: 80.2512 }        // Near Chennai
    };

    // Try village level
    if (location.village) {
      // Check if we have known coordinates for this village
      if (knownVillageCoordinates[location.village]) {
        console.log(`Using known coordinates for village ${location.village}:`, knownVillageCoordinates[location.village]);
        return knownVillageCoordinates[location.village];
      }

      if (location.district) {
        // Similar approach for village
        const districtCoords = districtCoordinates[location.district];
        if (districtCoords) {
          const villageHash = hashString(location.village);
          const latOffset = (villageHash % 100) / 1000; // More precise offset
          const lngOffset = ((villageHash / 100) % 100) / 1000;

          const villageCoords = {
            lat: districtCoords.lat + latOffset - 0.05,
            lng: districtCoords.lng + lngOffset - 0.05
          };

          console.log(`Generated village-level coordinates for ${location.village}:`, villageCoords);
          return villageCoords;
        }
      }
    }

    // Define specific coordinates for known blocks
    const knownBlockCoordinates = {
      'Berasia': { lat: 23.6307, lng: 77.4333 },      // Near Bhopal
      'Huzur': { lat: 23.2599, lng: 77.4126 },        // Bhopal district
      'Ludhiana East': { lat: 30.9081, lng: 75.9342 }, // Ludhiana district
      'Bangalore North': { lat: 13.0550, lng: 77.5930 }, // Bangalore district
      'Egmore': { lat: 13.0732, lng: 80.2606 }        // Chennai district
    };

    // Try block level
    if (location.block) {
      // Check if we have known coordinates for this block
      if (knownBlockCoordinates[location.block]) {
        console.log(`Using known coordinates for block ${location.block}:`, knownBlockCoordinates[location.block]);
        return knownBlockCoordinates[location.block];
      }

      if (location.district) {
        const districtCoords = districtCoordinates[location.district];
        if (districtCoords) {
          const blockHash = hashString(location.block);
          const latOffset = (blockHash % 100) / 800; // More precise offset
          const lngOffset = ((blockHash / 100) % 100) / 800;

          const blockCoords = {
            lat: districtCoords.lat + latOffset - 0.0625,
            lng: districtCoords.lng + lngOffset - 0.0625
          };

          console.log(`Generated block-level coordinates for ${location.block}:`, blockCoords);
          return blockCoords;
        }
      }
    }

    // Try district level
    if (location.district) {
      if (districtCoordinates[location.district]) {
        console.log(`Using district-level coordinates for ${location.district}:`, districtCoordinates[location.district]);
        return districtCoordinates[location.district];
      } else {
        // If district not found in our database but we have state coordinates
        if (location.state && regionCoordinates[location.state]) {
          // Generate deterministic coordinates for the district based on state
          const stateCoords = regionCoordinates[location.state];
          const districtHash = hashString(location.district);
          const latOffset = (districtHash % 100) / 400; // More precise offset
          const lngOffset = ((districtHash / 100) % 100) / 400;

          const generatedDistrictCoords = {
            lat: stateCoords.lat + latOffset - 0.125,
            lng: stateCoords.lng + lngOffset - 0.125
          };

          console.log(`Generated district-level coordinates for ${location.district}:`, generatedDistrictCoords);
          return generatedDistrictCoords;
        }
      }
    }

    // Try state level
    if (location.state) {
      if (regionCoordinates[location.state]) {
        console.log(`Using state-level coordinates for ${location.state}:`, regionCoordinates[location.state]);
        return regionCoordinates[location.state];
      } else {
        // Generate coordinates for unknown state based on central India
        const stateHash = hashString(location.state);
        const latOffset = (stateHash % 100) / 100;
        const lngOffset = ((stateHash / 100) % 100) / 100;

        const generatedStateCoords = {
          lat: 22.9734 + latOffset - 0.5,
          lng: 78.6569 + lngOffset - 0.5
        };

        console.log(`Generated state-level coordinates for unknown state ${location.state}:`, generatedStateCoords);
        return generatedStateCoords;
      }
    }
  } else if (typeof location === 'string') {
    // If location is a string, use the original logic
    // Check if location is a state/region
    if (regionCoordinates[location]) {
      console.log(`Using coordinates for state: ${location}`, regionCoordinates[location]);
      return regionCoordinates[location];
    }

    // Check if location is a district
    if (districtCoordinates[location]) {
      console.log(`Using coordinates for district: ${location}`, districtCoordinates[location]);
      return districtCoordinates[location];
    }

    // Generate coordinates for unknown location
    const locationHash = hashString(location);
    const latOffset = (locationHash % 100) / 100;
    const lngOffset = ((locationHash / 100) % 100) / 100;

    const generatedCoords = {
      lat: 22.9734 + latOffset - 0.5,
      lng: 78.6569 + lngOffset - 0.5
    };

    console.log(`Generated coordinates for unknown location ${location}:`, generatedCoords);
    return generatedCoords;
  }

  // Default to central India if all else fails
  console.warn(`Could not determine coordinates for location: ${JSON.stringify(location)}. Using default.`);
  return { lat: 22.9734, lng: 78.6569 }; // Central India coordinates
};

/**
 * Simple hash function to convert a string to a number
 * Used to generate deterministic coordinates for locations
 * @param {string} str - String to hash
 * @returns {number} - Hash value
 */
const hashString = (str) => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return Math.abs(hash);
};

/**
 * Get the nearest weather station for a location
 * This is a mock function as we don't have actual weather station data
 * @param {string} location - Region or district name
 * @returns {Object} - Weather station info
 */
const getNearestWeatherStation = (location) => {
  const coordinates = getCoordinates(location);

  // Mock weather station data
  return {
    id: `WS-${Math.floor(Math.random() * 1000)}`,
    name: `${location} Agricultural Station`,
    lat: coordinates.lat + (Math.random() * 0.1 - 0.05),
    lng: coordinates.lng + (Math.random() * 0.1 - 0.05),
    elevation: Math.floor(Math.random() * 500) + 100
  };
};

/**
 * Get soil sampling points for a location
 * This generates mock sampling points around the central coordinates
 * @param {string} location - Region or district name
 * @param {number} count - Number of sampling points to generate
 * @returns {Array} - Array of sampling points
 */
const getSoilSamplingPoints = (location, count = 5) => {
  const coordinates = getCoordinates(location);
  const samplingPoints = [];

  for (let i = 0; i < count; i++) {
    // Generate points within ~10km of the central coordinates
    const latOffset = (Math.random() * 0.2 - 0.1);
    const lngOffset = (Math.random() * 0.2 - 0.1);

    samplingPoints.push({
      id: `SP-${location}-${i}`,
      lat: coordinates.lat + latOffset,
      lng: coordinates.lng + lngOffset,
      description: `Sampling point ${i+1} near ${location}`
    });
  }

  return samplingPoints;
};

module.exports = {
  getCoordinates,
  getNearestWeatherStation,
  getSoilSamplingPoints
};
