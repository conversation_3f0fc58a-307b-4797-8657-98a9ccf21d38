<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <handlers>
      <add name="iisnode" path="server.js" verb="*" modules="iisnode"/>
    </handlers>
    <rewrite>
      <rules>
        <rule name="API" stopProcessing="true">
          <match url="^api/.*" />
          <action type="Rewrite" url="server.js"/>
        </rule>
        <rule name="StaticFiles" stopProcessing="true">
          <match url="^(static|assets|css|js|images|fonts|uploads)/.*" />
          <action type="Rewrite" url="public/{R:0}"/>
        </rule>
        <rule name="Frontend" stopProcessing="true">
          <match url="^(tm|agri-expert|dashboard|farms|weather|hr|farmers|livestock|ai|chat|auth|users).*" />
          <action type="Rewrite" url="public/index.html"/>
        </rule>
        <rule name="Default">
          <match url=".*" />
          <conditions>
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="True"/>
          </conditions>
          <action type="Rewrite" url="public/index.html"/>
        </rule>
      </rules>
    </rewrite>
    <iisnode node_env="production" />
  </system.webServer>
</configuration>
