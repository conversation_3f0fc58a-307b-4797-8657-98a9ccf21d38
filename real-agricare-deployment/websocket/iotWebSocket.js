const WebSocket = require('ws');
const jwt = require('jsonwebtoken');
const IOTSensor = require('../models/IOTSensor');

class IOTWebSocket {
  constructor(server) {
    this.wss = new WebSocket.Server({ server });
    this.clients = new Map(); // Map to store client connections with their user IDs

    this.wss.on('connection', async (ws, req) => {
      try {
        // Extract token from query string
        const token = new URL(req.url, 'ws://localhost').searchParams.get('token');
        if (!token) {
          ws.close(1008, 'Authentication required');
          return;
        }

        // Verify token and get user ID
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const userId = decoded.userId;

        // Store client connection
        this.clients.set(userId, ws);

        // Send initial sensor data
        const sensors = await IOTSensor.find({ assignedTo: userId })
          .select('-readings')
          .sort({ lastUpdate: -1 });
        ws.send(JSON.stringify({ type: 'INITIAL_DATA', data: sensors }));

        // Handle client disconnect
        ws.on('close', () => {
          this.clients.delete(userId);
        });

        // Handle errors
        ws.on('error', (error) => {
          console.error('WebSocket error:', error);
          this.clients.delete(userId);
        });

      } catch (error) {
        console.error('WebSocket connection error:', error);
        ws.close(1011, 'Internal server error');
      }
    });
  }

  // Broadcast sensor updates to specific user
  broadcastToUser(userId, data) {
    const client = this.clients.get(userId);
    if (client && client.readyState === WebSocket.OPEN) {
      client.send(JSON.stringify(data));
    }
  }

  // Broadcast to all connected clients
  broadcast(data) {
    this.wss.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify(data));
      }
    });
  }
}

module.exports = IOTWebSocket; 