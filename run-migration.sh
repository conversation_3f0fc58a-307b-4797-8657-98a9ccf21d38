#!/bin/bash

echo "🚀 AgriCare 1.0 - Local to Azure Cosmos DB Migration"
echo "=================================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if local MongoDB is running
echo "🔍 Checking local MongoDB connection..."
if ! mongosh --eval "db.adminCommand('ping')" >/dev/null 2>&1; then
    echo "❌ Local MongoDB is not running or not accessible."
    echo "Please start your local MongoDB server first:"
    echo "  - On macOS: brew services start mongodb-community"
    echo "  - On Linux: sudo systemctl start mongod"
    echo "  - On Windows: net start MongoDB"
    exit 1
fi

echo "✅ Local MongoDB is running"

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install mongoose dotenv
fi

# Run the migration
echo "🔄 Starting data migration..."
echo "This will migrate data from your local MongoDB to Azure Cosmos DB"
echo ""

node migrate-to-cosmos.js

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Migration completed successfully!"
    echo "🗄️ Your data is now in Azure Cosmos DB"
    echo "🚀 You can now deploy the application with the updated pipeline"
    echo ""
    echo "Next steps:"
    echo "1. Commit the migration script: git add migrate-to-cosmos.js"
    echo "2. Deploy with Cosmos DB: git push to trigger the pipeline"
    echo "3. Test the application at: https://qagricare-f5bfeyd5cafab0f7.centralindia-01.azurewebsites.net"
else
    echo ""
    echo "❌ Migration failed!"
    echo "Please check the error messages above and try again."
    exit 1
fi
