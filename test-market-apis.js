#!/usr/bin/env node

/**
 * Test script to verify real market data APIs
 */

const axios = require('axios');

async function testAGMARKNET() {
  console.log('🔍 Testing AGMARKNET API...');
  
  const endpoints = [
    {
      name: 'AGMARKNET Resource',
      url: 'https://api.data.gov.in/resource/9ef84268-d588-465a-a308-a864a43d0070',
      params: {
        'api-key': '579b464db66ec23bdd000001cdd3946e44ce4aad7209ff7b23ac571b',
        format: 'json',
        limit: 5,
        offset: 0
      }
    },
    {
      name: 'AGMARKNET Catalog',
      url: 'https://api.data.gov.in/catalog/9ef84268-d588-465a-a308-a864a43d0070',
      params: {
        'api-key': '579b464db66ec23bdd000001cdd3946e44ce4aad7209ff7b23ac571b',
        format: 'json',
        limit: 5
      }
    }
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`\n📊 Testing ${endpoint.name}...`);
      const response = await axios.get(endpoint.url, {
        params: endpoint.params,
        timeout: 10000,
        headers: {
          'User-Agent': 'AgriCare-Test/1.0',
          'Accept': 'application/json'
        }
      });

      console.log(`✅ Status: ${response.status}`);
      console.log(`📄 Response:`, JSON.stringify(response.data, null, 2).substring(0, 500));
      
      if (response.data && response.data.records && Array.isArray(response.data.records)) {
        console.log(`📈 Records found: ${response.data.records.length}`);
        if (response.data.records.length > 0) {
          console.log(`📋 Sample record:`, JSON.stringify(response.data.records[0], null, 2));
        }
      }
      
    } catch (error) {
      console.error(`❌ ${endpoint.name} failed:`, error.message);
      if (error.response) {
        console.error(`📄 Error response:`, JSON.stringify(error.response.data, null, 2).substring(0, 300));
      }
    }
  }
}

async function testCEDA() {
  console.log('\n🔍 Testing CEDA Agri-Market API...');
  
  const endpoints = [
    'https://agrimarket.nic.in/api/v1/market-overview',
    'https://agrimarket.nic.in/api/v1/prices',
    'https://api.ceda.agrimarket.in/v1/market-overview'
  ];

  for (const url of endpoints) {
    try {
      console.log(`\n📊 Testing ${url}...`);
      const response = await axios.get(url, {
        timeout: 10000,
        headers: {
          'User-Agent': 'AgriCare-Test/1.0',
          'Accept': 'application/json'
        }
      });

      console.log(`✅ Status: ${response.status}`);
      console.log(`📄 Response:`, JSON.stringify(response.data, null, 2).substring(0, 300));
      
    } catch (error) {
      console.error(`❌ ${url} failed:`, error.message);
      if (error.response) {
        console.error(`📄 Error response:`, error.response.status, error.response.statusText);
      }
    }
  }
}

async function testPyPricing() {
  console.log('\n🔍 Testing pyPricing API...');
  
  const endpoints = [
    'https://api.pypricingapi.com/v1/market-prices',
    'https://api.github.com/repos/pypricing/pypricingapi',
    'https://pypricingapi.herokuapp.com/api/v1/prices'
  ];

  for (const url of endpoints) {
    try {
      console.log(`\n📊 Testing ${url}...`);
      const response = await axios.get(url, {
        timeout: 10000,
        headers: {
          'User-Agent': 'AgriCare-Test/1.0',
          'Accept': 'application/json'
        }
      });

      console.log(`✅ Status: ${response.status}`);
      console.log(`📄 Response:`, JSON.stringify(response.data, null, 2).substring(0, 300));
      
    } catch (error) {
      console.error(`❌ ${url} failed:`, error.message);
      if (error.response) {
        console.error(`📄 Error response:`, error.response.status, error.response.statusText);
      }
    }
  }
}

async function main() {
  console.log('🚀 Testing Real Market Data APIs\n');
  
  await testAGMARKNET();
  await testCEDA();
  await testPyPricing();
  
  console.log('\n✅ API testing completed');
}

main().catch(console.error);
